<?xml version="1.0"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.extracme.evcard</groupId>
		<artifactId>evcard-mmp</artifactId>
		<version>2.0.2</version>
	</parent>
	<artifactId>evcard-mmp-rest</artifactId>
	<name>evcard-mmp-rest</name>
	<url>http://maven.apache.org</url>
<!--	<packaging>war</packaging>-->
	<packaging>jar</packaging>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<!--javax.servlet版本冲突问题，降级tomcat版本号无效引发其他问题,暂注释-->
		<!--<tomcat.version>8.5.37</tomcat.version>-->
		<jedis.version>2.9.0</jedis.version>
		<elasticsearch.version>6.7.0</elasticsearch.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.junit.vintage</groupId>
					<artifactId>junit-vintage-engine</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		
		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-mmp-service</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml-schemas</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>4.0.0</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>4.0.0</version>
		</dependency> 
	
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml-schemas</artifactId>
			<version>4.0.0</version>
		</dependency> 
		

		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-authority-rest</artifactId>
			<version>2.2.0</version>
			<exclusions>
				<exclusion>
					<groupId>com.extracme.evcard</groupId>
					<artifactId>evcard-redis</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.extracme.evcard</groupId>
					<artifactId>evcard-sso-client</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.extracme.evcard</groupId>
			<artifactId>evcard-sso-client</artifactId>
			<version>3.0.1</version>
		</dependency>

		<!--引入 Knife4j boot starter -->
		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>knife4j-spring-boot-starter</artifactId>
			<version>2.0.9</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.elasticsearch</groupId>
			<artifactId>elasticsearch</artifactId>
			<version>${elasticsearch.version}</version>
		</dependency>
		<dependency>
			<groupId>org.elasticsearch.client</groupId>
			<artifactId>elasticsearch-rest-high-level-client</artifactId>
			<version>${elasticsearch.version}</version>
		</dependency>

	</dependencies>


	<build>
		<finalName>evcard-mmp-back</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<mainClass>com.extracme.evcard.mmp.App</mainClass>
					<outputDirectory>../target</outputDirectory>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
