<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~  Copyright (c) 2019-2020, somowhere (<EMAIL>).
  ~  <p>
  ~  Licensed under the GNU Lesser General Public License 3.0 (the "License");
  ~  you may not use this file except in compliance with the License.
  ~  You may obtain a copy of the License at
  ~  <p>
  ~ https://www.gnu.org/licenses/lgpl.html
  ~  <p>
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<configuration debug="false" scan="false">
    <springProperty scope="context" name="application.logPath" source="application.logPath"
                    defaultValue="./log"/>
    <springProperty scope="context" name="spring.application.name" source="spring.logging.filename" defaultValue="evcard-mmp"/>
    <springProperty scope="context" name="spring.profiles.active" source="spring.profiles.active" defaultValue=""/>
    <springProperty scope="context" name="spring.logging.queue.size" source="logging.queue.size" defaultValue="512"/>
    <springProperty scope="context" name="spring.logging.hidlog.level" source="logging.hidlog.level" defaultValue="INFO"/>

    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <!-- Console logOperate output -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} | ${spring.profiles.active} | ${spring.application.name} | %thread | %-5level | %logger{50} | %X{traceId} | %file:%line | %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 下面配置一些第三方包的日志过滤级别
   level:TRACE, DEBUG, INFO, WARN, ERROR, ALL 和 OFF，还有一个特俗值INHERITED或者同义词NULL，代表强制执行上级的级别。如果未设置此属性，那么当前loger将会继承上级的级别。
    addtivity:是否向上级loger传递打印信息。默认是true。
    -->
    <logger name="org.springframework.web" level="DEBUG" additivity="false"/>

    <!-- 上生产的时候记得修改info级别 ，优先生效-->
    <!-- <logger name="com.extracme.framework" level="DEBUG" /> -->
    <!-- <logger name="dao" level="DEBUG"/> -->

    <!--logback.LogbackDemo：类的全路径 -->
    <logger name="com.extracme.framework" level="DEBUG" />
    <logger name="com.extracme.framework.front.controller.LogbackDemo" level="ERROR" />

    <!-- 关闭httpclient的日志-->
    <logger name="org.apache.commons.httpclient" level="WARN"/>
    <logger name="httpclient.wire" level="WARN"/>
    <logger name="org.apache.http" level="WARN"/>



    <!-- 文件日志 每天打一个包-->
    <appender name="LOGFILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${application.logPath}/app.log</file>
        <!-- 日志分包策略：基于时间的分包策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
<!--            <FileNamePattern>${application.logPath}/${spring.application.name}.log.%d{yyyyMMdd}</FileNamePattern>-->
            <FileNamePattern>${application.logPath}/app.log.%d{yyyyMMdd}</FileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} | ${spring.profiles.active} | ${spring.application.name} | %thread | %-5level | %logger{50} | %X{traceId} | %file:%line | %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${application.logPath}/${spring.application.name}-error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${application.logPath}/%d{yyyy-MM}/error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>150MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} | ${spring.profiles.active} | ${spring.application.name} | %thread | %-5level | %logger{50} | %X{traceId} | %file:%line | %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>


    <appender name="hidLogAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${application.logPath}/evcard-hidlog.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${application.logPath}/evcard-hidlog.log.%d{yyyy-MM-dd}.zip</fileNamePattern>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern> | %d{yyyy-MM-dd HH:mm:ss.SSS} | %msg%n</pattern>
        </layout>
    </appender>

    <logger name="com.extracme.evcard.rpc.entity.HidLog" level="${hidLog.level}"  additivity="false">
        <appender-ref ref="hidLogAppender" />
    </logger>

    <appender name="serverLogAppender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${application.logPath}/evcard-server.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${application.logPath}/evcard-server.log.%d{yyyyMMdd}</fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern> %d{yyyy-MM-dd HH:mm:ss.SSS} | ${spring.profiles.active} | ${spring.application.name} | %thread | %X{traceId} | %msg%n </pattern>
        </layout>
    </appender>


    <appender name="async-info" class="ch.qos.logback.classic.AsyncAppender">
        <!-- 更改默认的队列的深度,该值会影响性能.默认值为256 -->
        <queueSize>${spring.logging.queue.size}</queueSize>
        <!-- 添加附加的appender,最多只能添加一个 -->
        <appender-ref ref="LOGFILE"/>
        <appender-ref ref="serverLogAppender"/>
    </appender>

    <logger name="com.extracme.evcard.rpc.entity.ServerLog"  additivity="false">
        <appender-ref ref="serverLogAppender" />
    </logger>


    <!-- accessAppender -->
    <appender name="accessAppender"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${application.logPath}/evcard-rest-access.log</File>
        <!-- 日志分包策略：基于时间的分包策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${application.logPath}/evcard-rest-access.log.%d{yyyy-MM-dd}.zip</FileNamePattern>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} | %msg%n</pattern>
        </layout>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <logger name="com.extracme.evcard.authority.interceptor.AccessLogInterceptor" level="${access.lever}"  additivity="false">
        <appender-ref ref="accessAppender" />
    </logger>



    <logger name="org.springframework" level="INFO"/>
    <!-- Level: FATAL 0  ERROR 3  WARN 4  INFO 6  DEBUG 7 -->
    <root level="INFO">
        <appender-ref ref="error" />
        <appender-ref ref="console"/>
        <appender-ref ref="async-info"/>
<!--        <appender-ref ref="accessAppender" />-->
    </root>
</configuration>
