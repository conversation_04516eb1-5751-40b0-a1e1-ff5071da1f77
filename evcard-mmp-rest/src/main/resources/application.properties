spring.profiles.active=siac
spring.application.name=evcard-mmp
spring.main.allow-bean-definition-overriding=true
#spring.main.web-application-type=none
server.port=8080
server.servlet.context-path=/evcard-mmp


#spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
#spring.jackson.time-zone=GMT+8


#datasource
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource

#datasource.master
spring.datasource.druid.master.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.druid.master.max-active=100
spring.datasource.druid.master.initial-size=1
spring.datasource.druid.master.filters=dbauditlog
#spring.datasource.druid.master.max-wait\u00C8\u00B1\u00CA\u00A1\u00D6\u00B5\u00CE\u00AA-1 \u00BF\u00C9\u00C4\u00DC\u00B5\u00BC\u00D6\u00C2\u00BB\u00F1\u00C8\u00A1\u00C1\u00AC\u00BD\u00D3\u00CF\u00DF\u00B3\u00CC\u00D2\u00BB\u00D6\u00B1\u00B5\u00C8\u00B4\u00FD
#spring.datasource.druid.master.max-wait=10000
spring.datasource.druid.master.min-idle=1
spring.datasource.druid.master.time-between-eviction-runs-millis=60000
spring.datasource.druid.master.min-evictable-idle-time-millis=300000
spring.datasource.druid.master.validation-query=select 'x'
spring.datasource.druid.master.test-while-idle=true
spring.datasource.druid.master.test-on-borrow=false
spring.datasource.druid.master.test-on-return=false
spring.datasource.druid.master.pool-prepared-statements=true
spring.datasource.druid.master.max-open-prepared-statements=50
spring.datasource.druid.master.max-pool-prepared-statement-per-connection-size=20


#datasource.master
spring.datasource.druid.slave.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.druid.slave.max-active=100
spring.datasource.druid.slave.initial-size=1
spring.datasource.druid.slave.filters=dbauditlog
#spring.datasource.druid.slave.max-wait\u00C8\u00B1\u00CA\u00A1\u00D6\u00B5\u00CE\u00AA-1 \u00BF\u00C9\u00C4\u00DC\u00B5\u00BC\u00D6\u00C2\u00BB\u00F1\u00C8\u00A1\u00C1\u00AC\u00BD\u00D3\u00CF\u00DF\u00B3\u00CC\u00D2\u00BB\u00D6\u00B1\u00B5\u00C8\u00B4\u00FD
#spring.datasource.druid.slave.max-wait=10000
spring.datasource.druid.slave.min-idle=1
spring.datasource.druid.slave.time-between-eviction-runs-millis=60000
spring.datasource.druid.slave.min-evictable-idle-time-millis=300000
spring.datasource.druid.slave.validation-query=select 'x'
spring.datasource.druid.slave.test-while-idle=true
spring.datasource.druid.slave.test-on-borrow=false
spring.datasource.druid.slave.test-on-return=false
spring.datasource.druid.slave.pool-prepared-statements=true
spring.datasource.druid.slave.max-open-prepared-statements=50
spring.datasource.druid.slave.max-pool-prepared-statement-per-connection-size=20


#mybatis
mybatis.mapper-locations=classpath*:com/extracme/evcard/**/sqlmap/*.xml
mybatis.configuration.variables.mmpSchema=iss
mybatis.configuration.variables.issSchema=iss
mybatis.configuration.variables.siacSchema=siac
mybatis.configuration.variables.isvSchema=isv
mybatis.configuration.variables.locationSchema=iss
mybatis.configuration.variables.ssoSchema=sso
mybatis.configuration.variables.platSchema=iplat

#sqlmapper\u00D6\u00D0\u00D2\u00FD\u00C8\u00EB\u00B5\u00C4\u00B2\u00CE\u00CA\u00FD
mybatis.configuration.variables.evcard.bdp.dns=http://evcard.oss-cn-shanghai.aliyuncs.com/test/

mybatis.configuration.variables.normal_level_consume=0
#\u00D2\u00F8\u00BF\u00A8\u00BB\u00E1\u00D4\u00B1\u00D3\u00D0\u00D0\u00A7\u00CF\u00FB\u00B7\u00D1\u00BD\u00F0\u00B6\u00EE\u00C9\u00CF\u00CF\u00DE
mybatis.configuration.variables.silver_level_consume=200
#\u00BD\u00F0\u00BF\u00A8\u00BB\u00E1\u00D4\u00B1\u00D3\u00D0\u00D0\u00A7\u00CF\u00FB\u00B7\u00D1\u00BD\u00F0\u00B6\u00EE\u00C9\u00CF\u00CF\u00DE
mybatis.configuration.variables.golden_level_consume=1000
#\u00B0\u00D7\u00BD\u00F0\u00BB\u00E1\u00D4\u00B1\u00D3\u00D0\u00D0\u00A7\u00CF\u00FB\u00B7\u00D1\u00BD\u00F0\u00B6\u00EE\u00C9\u00CF\u00CF\u00DE
mybatis.configuration.variables.platinum_level_consume=2000



mybatis.configuration.cache-enabled=true
mybatis.configuration.lazy-loading-enabled=false
mybatis.configuration.multiple-result-sets-enabled=true
mybatis.configuration.use-column-label=true
mybatis.configuration.use-generated-keys=true
mybatis.configuration.auto-mapping-behavior=partial
mybatis.configuration.default-executor-type=simple
mybatis.configuration.default-statement-timeout=25
mybatis.configuration.safe-row-bounds-enabled=false
mybatis.configuration.map-underscore-to-camel-case=false
mybatis.configuration.local-cache-scope=session
mybatis.configuration.jdbc-type-for-null=other
mybatis.configuration.lazy-load-trigger-methods=equals,clone,hashCode,toString
mybatis.configuration.logPrefix=dao.



#dubbo
#dubbo.scan.base-packages=com.extracme.evcard.activity.service,com.extracme.evcard.activity.dubboService
#dubbo.provider.application.name=evcard-activity-rpc

dubbo.application.logger=slf4j
dubbo.registry.register=true
dubbo.registry.file=./dubboregistry/dubbo-registry.properties
dubbo.protocol.id=dubbo
dubbo.protocol.name=dubbo
dubbo.protocol.heartbeat=15000
dubbo.provider.timeout=10000
dubbo.provider.threadpool=fixed
dubbo.provider.threads=200
dubbo.provider.accepts=1000
dubbo.provider.protocol=dubbo
dubbo.provider.cluster=failfast
dubbo.provider.loadbalance=roundrobin
dubbo.provider.server=netty
dubbo.consumer.lazy=true
dubbo.consumer.check=false
dubbo.consumer.cluster=failfast
dubbo.provider.port=20883
#\u00B4\u00FD\u00C8\u00B7\u00C8\u00CF\u00B7\u00FE\u00CE\u00F1\u00B6\u00CB\u00C9\u00E8\u00D6\u00C3connections\u00CA\u00C7\u00B7\u00F1\u00D3\u00D0\u00D0\u00A7&\u00BA\u00CF\u00C0\u00ED\u00B5\u00C4\u00C8\u00A1\u00D6\u00B5
dubbo.consumer.connections=1


dubbo.owner=wuyibo
dubbo.organization=extracme
dubbo.registry.address=zookeeper://**************:2181
dubbo.registry.timeout=40000




logging.config=classpath:logback-spring.xml
logging.file.max-history=30
logging.file.max-size=100MB
logging.queue.size=512

logging.level.org.apache.dubbo=warn
logging.level.org.apache.zookeeper=warn
logging.level.root=debug



#elasticjob
elasticjob.regCenter.serverLists=**************:2181
elasticjob.regCenter.namespace=elastic-job


#apollo
app.id= ${spring.application.name}
apollo.cluster=evcard
#apollo enable during spring boot applyInitializers
apollo.bootstrap.enabled=true
apollo.bootstrap.namespaces=market.sso-client,config,application,aliyunConfig,redis,es,message
apollo.bootstrap.eagerLoad.enabled=true

knife4j.enable=true
#knife4j.basic.username=evcard
#knife4j.basic.password=evcard



#restTemplate
spring.resttemplate.connectTimeout=3000
spring.resttemplate.readTimeout=5000
spring.resttemplate.usePool=false
spring.resttemplate.maxTotalConnect=128
spring.resttemplate.maxConnectPerRoute=32
spring.resttemplate.errorCountToAlarm=5
spring.resttemplate.logAllHeaders=false
spring.resttemplate.logHeaderNames=X-EXP-COOKIE

md.rest.api.baseUrl=https://md-dev.evcard.vip/
md.rest.api.getCacheGoodsModelName=mdadmin/goods/inner/getCacheGoodsModelName
md.rest.api.getCacheStore=mdadmin/store/inner/getCacheStore
md.rest.api.getOrderCountByStatusUrl=mdadmin/order/inner/getOrderCountByStatus
md.rest.api.getCacheGoodsVehicleModelRelation=mdadmin/goods/inner/getCacheGoodsVehicleModelRelation

md.rest.api.getMemberDepositInfos=mdadmin/deposit/inner/getMemberDepositInfos
md.rest.api.getMemberDepositLogs=mdadmin/deposit/inner/getMemberDepositLogs
md.rest.api.rapidRefund=mdadmin/deposit/inner/rapidRefund
md.rest.api.queryRapidRefundRecord=mdadmin/deposit/inner/queryRapidRefundRecord
md.rest.api.getPersonalContractInfo=mdadmin/order/inner/getPersonalContractInfo
md.rest.api.getContractListByPersonal=mdadmin/order/inner/getContractListByPersonal
md.rest.api.getUnpaidGoodsOrderByMId=mdadmin/order/inner/getUnpaidGoodsOrderByMId
md.rest.api.getUserPayedCount=mdadmin/pay/inner/getUserPayedCount

#???
md.rest.api.queryVehicleBrandInfoNoLogin=mdadmin/vlms/modelcenter/queryVehicleBrandInfoNoLogin
md.rest.api.queryVehicleLevelList=mdadmin/vlms/modelcenter/queryVehicleLevelList

md.rest.api.getAllTopCityInfo=mdadmin/store/inner/getStoreModeAllCityNew
md.rest.api.querySecondChannelListForMmp=mdadmin/user/inner/querySecondChannelListForMmp