spring.profiles=local

#apollo
#apollo.meta=http://apollo-dev.evcard.vip:58080
apollo.bootstrap.enabled=false
apollo.bootstrap.namespaces=
apollo.bootstrap.eagerLoad.enabled=false


#datasource
spring.datasource.druid.master.url = **********************************************************************************************************************************************************
spring.datasource.druid.master.username = devuser
spring.datasource.druid.master.password = blk2ZsEB

spring.datasource.druid.slave.url = **********************************************************************************************************************************************************
spring.datasource.druid.slave.username = devuser
spring.datasource.druid.slave.password = blk2ZsEB

#sqlmapper??????
mybatis.configuration.variables.evcard.bdp.dns = http://evcard.oss-cn-shanghai.aliyuncs.com/test/

#dubbo
dubbo.registry.address = zookeeper://zk-st.evcard.vip:2181

#redis
host = evcard-st-lan.redis.rds.aliyuncs.com
port = 6379
pwd = Wp4uJK*Vc3v2
timeout = 10000
maxWaitMillis = -1
minIdle = 8
maxIdle = 16
maxTotal = 100


#elasticjob
elasticjob.regCenter.serverLists = zk-st.evcard.vip:2181

#logger
logging.level.org.apache.dubbo = warn
logging.level.org.apache.zookeeper = warn
logging.level.root = info
logging.level.dao = info

knife4j.enable = true



spring.http.multipart.max-file-size = 200MB
spring.http.multipart.max-request-size = 200MB

spring.servlet.multipart.max-file-size = 200MB
spring.servlet.multipart.max-request-size = 200MB
#spring.servlet.multipart.max-swallow-size = 10MB
server.tomcat.max-http-post-size = 200MB
server.tomcat.max-http-form-post-size = 200MB
server.tomcat.max-swallow-size = 200MB

multipart.maxFileSize = 200MB
multipart.maxRequestSize = 200MB

server.servlet.multipart.max-file-size = 200MB
server.servlet.multipart.max-request-size = 200MB



#server.tomcat.max-http-post-size = -1
#server.tomcat.max-http-form-post-size = -1
#server.tomcat.max-http-post-size=-1
#server.tomcat.max-http-header-size=102400

sys.app.code = evcard-mmp
#???????
sys.syn.table = iss.mmp_user
#??????????
sys.syn.url = https://sso-st.evcard.vip/evcard-sso/api/userSync
sys.syn.appSecret = IuUQO0UjRfOfIDYRwCB-ZA
sys.syn.appKey = evcard-mmp


export.MembershipInfo.title = ??,???,????,??????,????,?????????,????,????,?????????,????,????,????
export.MembershipAnalysisInfo.title = ??,??,??,??,???,???
export.CouponGrantRecord.title = ??,???,????,????,????,??????,?ID,???,???,???%?,???????,????,????,????,????,????,??????,????????,????????,??????,??????,?????,?????,??????,??????,?????,???,????,???
export.CouponExchangeRecord.title = ??,???,????,????,????,??????,???ID,?????,?????,???%?,???????,????,?????,??????,????,????,??????,?????,???????,???????,??????,??????,??????,??????,??????,????,????,???????,???????,??????,??????,???????,???,????,???,??????,?????
export.MemUserTaxAmount.title = ??,??,????,????,????,??,????,?????,????,????????,????,??



#??????????????????????????????????,??????????0???1???
drivingLicense.expire.debug.mode = 1

export.EventTypeReport.title = ????,1?,2?,3?,4?,5?,6?,7?,8?,9?,10?,11?,12?

#??????????
express.import.template.url = http://evcard.oss-cn-shanghai.aliyuncs.com/prod/mmpTemplate/ExpressImportTemplate.xlsx
#???????????
coupon.import.template.url = http://evcard.oss-cn-shanghai.aliyuncs.com/prod/mmpTemplate/Import_Coupon_Template.xlsx

#debug??:0 ????:1???1?
debug.mode = 1

export.ApplyDrawBack.title = ???,????,????,????,??????

# ???????????????????
url.findSuperAdminAcountInfo = http://csms-st.evcard.vip/evcard-bvm/inner/searchAdminUserList
url.addSuperAdminAcountInfo = http://csms-st.evcard.vip/evcard-bvm/inner/saveSuperAdminUser
url.getSuperAdminAcountInfo = http://csms-st.evcard.vip/evcard-bvm/inner/getSuperAdminUser
url.modifySuperAdminAcountInfo = http://csms-st.evcard.vip/evcard-bvm/inner/updateSuperAdminUser
url.deleteSuperAdminAcountInfo = http://csms-st.evcard.vip/evcard-bvm/inner/deleteSuperAdminUser
url.forbiddenSuperAdminAcountInfo = http://csms-st.evcard.vip/evcard-bvm/inner/forbiddenAdminUser
url.recoverSuperAdminAcountInfo = http://csms-st.evcard.vip/evcard-bvm/inner/recoverAdminUser
url.getRealFreeDepositNumber = http://csms-st.evcard.vip/evcard-bvm/inner/getAgencyExemptionDepositDetail
url.getAgencyAccountInfo = http://csms-st.evcard.vip/evcard-bvm/inner/getAgencyAccountInfo

export.brandCode.title = ???,??
export.brandCodeErrorMsg.title = ????,????
import.brandCode.title = ????,?????,??,???
import.brandCode.title2 = ??????

#??????????id
brandCodeSmsTemplateId = 26
#??????
smsMode = 1

#???????????
newAuditPassSmsTemplateId = 27
#????????????
newAuditNoPassSmsTemplateId = 28


##disable origion settings of es
##add for es
#es.httpHost.host1=*************
#es.httpHost.host2=*************
#es.httpHost.host3=*************
#es.httpHost.port1=9200
#es.httpHost.port2=9200
#es.httpHost.port3=9200
#es.httpHost.schema=http
#es.esclient.connectNum=80
#es.esclient.connectPerRoute=100

#membership.search.index=bd_membership_search_index
#membership.search.type=bd_membership_search_type
#
#esSearchEnabled=0


#??????????????????????
auditPassSmsTemplateId = 47
#??????
reAuditPassSmsTemplateId = 48
#?????
auditNoPassSmsTemplateId = 49
#?????90?
remindingDrivingLicenseSmsTemplateId = 50
# ????????
url.batchUpdateAgencyByAuthId = http://csms-st.evcard.vip/evcard-bvm/inner/batchUpdateMembershipAgency
#???????????????
export.agencyMemberErrorMsg.title = ????,?????
#?????
messageFlag = sgm_songjiang,ibuick_car_sharing,sgm_car_sharing
#????????
driverLicenseExpires = 54
#?????????????
BatchImportErrorReportTitle = ????,????,????
export.CouponBatchImport.title = ???,??,???ID
url.disposeMmpTask = http://csms-st.evcard.vip/evcard-callcenter/inner/externalTask/dispose.sy
url.queryTaskList = http://csms-st.evcard.vip/evcard-callcenter/inner/externalTask/queryTaskList.sy
url.queryTaskListCount = http://csms-st.evcard.vip/evcard-callcenter/inner/externalTask/queryTaskListCount.sy
url.queryTaskDetail = http://csms-st.evcard.vip/evcard-callcenter/inner/externalTask/queryTaskDetail.sy?taskId=
url.queryCountByOrgId = http://csms-st.evcard.vip/evcard-callcenter/inner/externalTask/queryCountByOrgId.sy?orgId=
url.queryProblemType = http://csms-st.evcard.vip/evcard-callcenter/inner/externalTask/queryProblemType.sy?pid=
url.queryExportList = http://csms-st.evcard.vip/evcard-callcenter/inner/externalTask/queryExportList.sy
#????
url.getAgencyMemberList = http://csms-st.evcard.vip/evcard-bvm/inner/getAgencyMemberList
url.getAgencyRoleSpec = http://csms-st.evcard.vip/evcard-bvm/inner/getAgencyRoleSpec

mas.api.url = https://csms-st.evcard.vip/evcard-mas/api/
logout.api = loginOut

CounterfeitSmsTemplateId = 70
ReexamineSmsTemplateId = 71


activityCouponUrl = https://csms-st.evcard.vip/evcard-active/memberActivities/sweepCodeReceiveCoupon


#??????
sa_isEnable = 1

#??
cancelSignWithPuFaUrl = http://csms-st.evcard.vip/evcard-rest/pufa/cancelSignWithPuFa
#????
frozenDepositWithPuFaUrl = http://csms-st.evcard.vip/evcard-rest/pufa/frozenDepositWithPuFa
#????????
queryFrozenDepositResultUrl = http://csms-st.evcard.vip/evcard-rest/pufa/queryFrozenDepositResult
#????
unFrozenDepositWithPuFaUrl = http://csms-st.evcard.vip/evcard-rest/pufa/unFrozenDepositWithPuFa
#????????
queryUnFrozenDepositResultUrl = http://csms-st.evcard.vip/evcard-rest/pufa/queryUnFrozenDepositResult
#??
decreaseDepositWithPuFaUrl = http://csms-st.evcard.vip/evcard-rest/pufa/decreaseDepositWithPuFa
#??????
queryDecreaseDepositResultUrl = http://csms-st.evcard.vip/evcard-rest/pufa/queryDecreaseDepositResult
#??
increaseDepositWithPuFaUrl = http://csms-st.evcard.vip/evcard-rest/pufa/increaseDepositWithPuFa
#??????
queryIncreaseDepositResultUrl = http://csms-st.evcard.vip/evcard-rest/pufa/queryIncreaseDepositResult
#????
signCheckResultUrl = http://csms-st.evcard.vip/evcard-rest/pufa/signCheckResultWithPuFa

#????h5??
brandActivityCouponUrl = http://csms-st.evcard.vip/evcard-rest/brandActivityIndex.html
#?????
exportLayerPageStatisticsTitle = /report/exportLayerPageStatisticsTitle.xlsx
exportWelcomePageStatisticsTitle = /report/exportWelcomePageStatisticsTitle.xlsx
#????appkey
activityChannelKey = http://csms-st.evcard.vip/evcard-h5app/#/?register=true&appKey=
qrCodeUrl = http://csms-st.evcard.vip/evcard-rest/api/qrcode/qrCodeRedirect/
#??????
channelRewardActivity = https://csms-st.evcard.vip/evcard-active/memberActivities/register
#????????
layerActivity = https://csms-st.evcard.vip:1443/evcard-rest/indexPop.html


#???????????????????
#????????????
normal_level_consume = 0
#????????????
silver_level_consume = 200
#????????????
golden_level_consume = 1000
#????????????
platinum_level_consume = 2000

#md.rest
md.rest.api.baseUrl = https://md-st.evcard.vip/
idCardExpiresSmsTemplateId = 243
idCardApproachingJobSmsTemplateId = 242
driverLicenseExpiresSmsTemplateIdNew = 239
driverLicenseExpiresMessageNew = ????????????????EVCARD???????APP????????????????????**********?
remindingDrivingLicenseSmsTemplateIdNew = 238
mmp.shortlink.domain = http://csms-dl-st.evcard.vip
messagepush.topic = EVCARD_MESSAGE_PUSH_SIAC

mmp.wechat.appid = wx361160c0c80165bf
mmp.wechat.secret = 7f2a162462e030c92127a497d79b9d23
mmp.wechat.baseUrl = https://api.weixin.qq.com
mmp.wechat.getToken = ${mmp.wechat.baseUrl}/cgi-bin/token
mmp.wechat.getwxacodeunlimit = ${mmp.wechat.baseUrl}/wxa/getwxacodeunlimit?access_token=
mmp.wechat.genUrlLink = ${mmp.wechat.baseUrl}/wxa/generate_urllink?access_token=
mmp.wechat.page.register = pages/storeOrder/index
mmp.wechat.page.sweepCode = pages/store/list/cityList/index
unregister.secondAppKey = second_fiction_key
channelRewardActivityOld = http://csms-st.evcard.vip/evcard-rest/channelRegist.html
activityCouponUrlOld = http://csms-st.evcard.vip/evcard-rest/indexNew.html

#oss ??????
oss_endPoint = http://oss-cn-shanghai.aliyuncs.com
ali_accessId = LTAI5tJumdVNXHPLszk49yAk
ali_accessKey = ******************************
oss_bucket = evcard

env = test
showImg_title = http://evcard.oss-cn-shanghai.aliyuncs.com


#ons
ons_addr = http://onsaddr.mq-internet-access.mq-internet.aliyuncs.com:80
ali.mmp.AccessKey = LTAI5t6i6Tb9XN6gRqkstxYH
ali.mmp.SecretKey = ******************************
ali.mmp.gid = GID_EVCARD_MMP_SIAC
ali.mmp.topic = EVCARD_RAW_DATA_SIAC
#???????
ali.mmp.couponImport.topic = EVCARD_SEND_COUPON_SIAC


#enable es search
esSearchEnabled = 1

#settings of elasticSearch
httpHost.hosts = *********:9200,*********:9200,*********:9200
httpHost.schema = http
esclient.connectNum = 80
esclient.connectPerRoute = 100

ecclient.userName = elastic
ecclient.password =


membership.search.index = bd_membership_search_index
membership.search.type = bd_membership_search_type

filter.sso.excludedPages = *.js,*.css,*.map,api/thirdCoupon/offerThirdCoupon,inner/offerSweepActivityCoupon,inner/getSweepActivity,api/getBvmAgencyInfo,inner/offerBrandActivityCoupon,inner/getBrandActivity,inner/qrcode/getQRCodeUrl,inner/getChannelRewardActivity,inner/externalTask/create,inner/layer/getLayerActivity,inner/welcome/getWelcomeActivity,inner/memberInfoForBfs,api/shortlink/shortlinkRedirect,api/agencyInfoListenerFromLongShort,api/shortlink/insertOrUpdateShortlink,api/shortlink/getLongUrlParams,api/channelBlacklist/inner/insert
excludedKeys = doc.html,swagger,webjars,api-docs,webjars


#CODE
00000000 = ????
00000001 = ???????
00000002 = ????
00000003 = ???????
00000004 = ??????url
00000005 = ????????
00000006 = ???
10020003 = ????????
10020006 = ??????
10020007 = ?????????
10020010 = ????????
10020012 = ????????
10020014 = ??????

10070001 = ????

10030001 = {?} ????,??????
10030002 = {?}?????
10030003 = {?}?????????{?}??
10030004 = {?}???????
10030005 = {?}?????????
10030006 = {?}????????{?}?,???????{?}??
10030007 = {?}?????????
10030008 = {?}?????????????!
10030009 = {?}????????
10030010 = {?}????????{?}


message3 = ?????EVCARD???????%s????????????? - ???? - ???????????????????????????

pushmessage1 = ?????EVCARD???????%s????????????? - ???? - ???????????????????????????
pushmessage2 = ?????????EVCARD????????????????3~5??????????????????????????**********?

pushNewAuditPassMessage = ?????????????????????????EVCARD???????????EVCARD?????????????????400-920-8050?
pushNewAuditNoPassMessage = ????????????????????????%s???????????????????????400-920-8050?????
driverLicenseExpiresMessage = ??????????????????????EVCARD?????APP??????????????????????????????**********?
idCardExpiresMessage = ??????????????????????APP????????????????????**********?

sso.domainHost = https://sso-sit.gcsrental.com/auth/
sso.innerHost = https://sso-st.evcard.vip/evcard-sso
ssoAddr = https://sso-st.evcard.vip/evcard-sso/login.html
sso.domainName = .evcard.vip
#filter.sso.excludedPages = api/mqtt/superuser,api/mqtt/acl,api/mqtt/auth,api/v1/validateToken,api/cityTree,app/loginAjax,app/loginCheck,app/checkCheckcode,app/checkCodeValidate,app/checkCode,app/forgetPwd,app/mobileLogin,app/modifyPassword,api/forgetPwd,api/mobileLogin,api/modifyPassword,api/loginAjax,app/appLogin,loginAjax,api/testconnection,api/login,login,*.html,api/userSync,api/getUserSyncInfo,api/userAdd,api/disableUserForSystem,api/validateToken,api/orgTree,testconnection,api/appLogin,api/orgSync,api/pwdErrorTimes,api/checkCode,api/forgetPwdReset,inner/user,api/V3/checkCode,app/V3/checkCode,baiwang/invoicing,api/baiwang/invoicing,api/electricInvoice/electricRedInvoiceNotify
