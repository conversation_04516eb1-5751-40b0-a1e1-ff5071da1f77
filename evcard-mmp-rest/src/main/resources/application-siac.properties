spring.profiles=siac

#datasource
spring.datasource.druid.master.url=**************************************************************************************************************************************
spring.datasource.druid.master.username=mmpuser
spring.datasource.druid.master.password=t7Dw9HoM

spring.datasource.druid.slave.url=**************************************************************************************************************************************
spring.datasource.druid.slave.username=mmpuser
spring.datasource.druid.slave.password=t7Dw9HoM

mybatis.configuration.variables.evcard.bdp.dns=http://evcard.oss-cn-shanghai.aliyuncs.com/test/

#dubbo
dubbo.registry.address=zookeeper://**************:2181

#redis
#spring.redis.host=************
#spring.redis.port=6379

#elasticjob
elasticjob.regCenter.serverLists=**************:2181

#apollo
apollo.meta=http://apollo-st.evcard.vip:48080

#logger
logging.level.org.apache.dubbo=warn
logging.level.org.apache.zookeeper=warn
logging.level.root=info
logging.level.dao=info

knife4j.enable=true

#md.rest
md.rest.api.baseUrl=https://md-st.evcard.vip/
