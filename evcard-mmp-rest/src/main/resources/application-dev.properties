spring.profiles=dev

#datasource
spring.datasource.druid.master.url = **************************************************************************************************************************************************************
spring.datasource.druid.master.username = devuser
spring.datasource.druid.master.password = blk2ZsEB

spring.datasource.druid.slave.url = **************************************************************************************************************************************************************
spring.datasource.druid.slave.username = devuser
spring.datasource.druid.slave.password = blk2ZsEB


mybatis.configuration.variables.evcard.bdp.dns = http://evcard.oss-cn-shanghai.aliyuncs.com/dev/

#dubbo
dubbo.registry.address = zookeeper://zk-dev.evcard.vip:2181

#redis
#spring.redis.host=************
#spring.redis.port=6379

#elasticjob
elasticjob.regCenter.serverLists = zk-dev.evcard.vip:2181

#apollo
apollo.meta = http://apollo-test.evcard.vip:58080

springfox.documentation.swagger.v2.host = ***********:8094/evcard-mmp

logging.level.org.apache.dubbo = warn
logging.level.org.apache.zookeeper = warn
logging.level.root = debug
logging.level.dao = debug


#md.rest
md.rest.api.baseUrl=https://md-dev.evcard.vip/