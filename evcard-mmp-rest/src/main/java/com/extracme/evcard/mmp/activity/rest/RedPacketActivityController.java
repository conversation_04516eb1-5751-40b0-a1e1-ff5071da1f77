package com.extracme.evcard.mmp.activity.rest;

import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.bo.ThirdCouponModelParamsBO;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.activity.ActivityCouponModelPageDTO;
import com.extracme.evcard.mmp.dto.activity.ActivityCouponStatisticsDTO;
import com.extracme.evcard.mmp.dto.activity.RedPacketActivityDetailDTO;
import com.extracme.evcard.mmp.dto.activity.RedPacketActivityFullDTO;
import com.extracme.evcard.mmp.service.IRedPacketActivityService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Api(value="redPacketActivity", tags = "红包网点[13]")
@RestController
@RequestMapping("api/redPacketActivity")
public class RedPacketActivityController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    IRedPacketActivityService redPacketActivityServiceImpl;

    /**
     * 活动新增
     * @param activityFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="活动创建", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "add" ,method = RequestMethod.POST)
    public DefaultWebRespVO add(@RequestBody RedPacketActivityFullDTO activityFullDTO,
                                                HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = redPacketActivityServiceImpl.add(activityFullDTO, request);
            if (respDTO.getCode() != 0) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("新增活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "新增失败");
        }
        vo.setMessage("新增活动成功");
        return vo;
    }

    /**
     * 活动更新
     * @param activityFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="活动更新", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "update" ,method = RequestMethod.POST)
    DefaultWebRespVO update(@RequestBody RedPacketActivityFullDTO activityFullDTO,
                                            HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = redPacketActivityServiceImpl.update(activityFullDTO, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("更新活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "修改失败");
        }
        vo.setMessage("修改活动成功");
        return vo;
    }

    /**
     * 活动删除
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动删除", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "delete/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO delete(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = redPacketActivityServiceImpl.delete(id,request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("删除活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "删除失败");
        }
        vo.setMessage("删除活动成功");
        return vo;
    }

    /**
     * 活动暂停
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动暂停", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "pause/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO suspend(@PathVariable("id") Long id,
                                           HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = redPacketActivityServiceImpl.suspend(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("暂停活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "暂停失败");
        }
        vo.setMessage("暂停活动成功");
        return vo;
    }

    /**
     * 活动添加
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动停止", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "stop/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO stop(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = redPacketActivityServiceImpl.stop(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("停止活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "停止失败");
        }
        vo.setMessage("停止活动成功");
        return vo;
    }

    /**
     * 活动启动
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动启动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "start/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO start(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = redPacketActivityServiceImpl.start(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("启动活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "启动活动失败");
        }
        vo.setMessage("启动活动成功");
        return vo;
    }


    /**
     * 活动启动
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动发布", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "publish/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO publish(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = redPacketActivityServiceImpl.publish(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("发布活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "发布活动失败");
        }
        vo.setMessage("发布活动成功");
        return vo;
    }


    /**
     * 活动恢复
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动恢复", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "resume/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO resume(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = redPacketActivityServiceImpl.resume(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("恢复活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "恢复失败");
        }
        vo.setMessage("恢复活动成功");
        return vo;
    }

    /**
     * 活动详情获取
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动详情", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "query/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO queryDetails(@PathVariable("id") Long id, HttpServletRequest request){
        RedPacketActivityDetailDTO activityDetailDTO = redPacketActivityServiceImpl.queryDetails(id);
        if (null == activityDetailDTO) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动信息不存在");
        }
        return DefaultWebRespVO.getSuccessVO(activityDetailDTO);
    }

    /**
     * 查询活动发放优惠券统计
     * @param id
     * @return
     */
    @ApiOperation(value="活动发放优惠券统计", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "queryCouponStatistics/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO queryCouponStatistics(@PathVariable("id") Long id) {
        ActivityCouponStatisticsDTO resultList = redPacketActivityServiceImpl.queryCouponStatistics(id);
        if(null == resultList) {
            return new DefaultWebRespVO("-1", "活动发券统计信息不存在");
        }
        return DefaultWebRespVO.getSuccessVO(resultList);
    }

    /**
     * 券模板列表
     * @param paramsBO
     * @param request
     * @return
     */
    @ApiOperation(value="券模板列表", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "getCouponModelPage", method = RequestMethod.POST)
    public DefaultWebRespVO getCouponModelPage(@RequestBody ThirdCouponModelParamsBO paramsBO,
                                               HttpServletRequest request) {
        ActivityCouponModelPageDTO couponModelPage = redPacketActivityServiceImpl.getCouponModelPage(paramsBO);
        if (null == couponModelPage) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动券模板信息不存在");
        }
        return DefaultWebRespVO.getSuccessVO(couponModelPage);
    }
}
