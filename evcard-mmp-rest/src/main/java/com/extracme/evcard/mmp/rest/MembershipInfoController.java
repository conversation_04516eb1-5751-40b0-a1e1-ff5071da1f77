package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.bo.PageInfoBO;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.ApplyDrawBackInfoDTO;
import com.extracme.evcard.mmp.dto.MembershipAgencyDTO;
import com.extracme.evcard.mmp.dto.MembershipDTO;
import com.extracme.evcard.mmp.dto.MembershipInfoDTO;
import com.extracme.evcard.mmp.service.IEsMembershipInfoService;
import com.extracme.evcard.mmp.service.IMembershipInfoService;
import com.extracme.evcard.mmp.service.ListPageBeanBO;
import com.extracme.evcard.mmp.vo.MembershipAgencyVO;
import com.extracme.evcard.mmp.vo.MembershipVO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.constants.Constants;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.exception.BusinessRuntimeException;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.List;

/**
 * 项目名称：evcard-mmp-rest
 * 类名称：MembershipInfoController
 * 类描述：会员信息控制层
 * 创建人：sunb-孙彬
 * 创建时间：2017年9月18日下午8:20:19
 * 修改备注
 *
 * @version2.0
 */
@RestController
@RequestMapping("api")
public class MembershipInfoController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IMembershipInfoService membershipInfoServiceImpl;

    @Resource
    private IEsMembershipInfoService esMembershipInfoServiceImpl;
    
//    /**
//     * 会员信息一览
//     *
//     * @param pageNum                                 页码
//     * @param pageSize                                每页显示条数
//     * @param name                                    姓名（精确查询）
//     * @param mobilePhone                             手机号（精确查询）
//     * @param drivingLicense                          驾照号（精确查询）
//     * @param reviewStatus                            会员状态
//     * @param cardStatus                              卡类状态
//     * @param orgId                                   会员所属公司
//     * @param province                                注册所属地区（省）
//     * @param city                                    注册所属地区（市）
//     * @param area                                    注册所属地区（区）
//     * @param provinceOfApp                           APP定位地区（省）
//     * @param cityOfApp                               APP定位地区（市）
//     * @param areaOfApp                               APP定位地区（区）
//     * @param agencyId                                关联企业（模糊查询）
//     * @param exemptDeposit                           免押等级(0：不免除押金 1：普通车型 2：标准车型 3：中端车型 4：高端车型)
//     * @param infoOrigin                              备注来源（模糊查询）
//     * @param appKey                                  渠道来源（模糊查询）
//     * @param createdStartTime                        注册开始时间
//     * @param createdEndTime                          注册结束时间
//     * @param reviewStartTime                         提交审批开始时间
//     * @param reviewEndTime                           提交审批结束时间
//     * @param dataOrigin                              注册平台（0：网点注册 1：网站注册 2：管理平台注册 3:手机APP 4:微信）
//     * @param cardNoType                              卡类型（1：虚拟卡，2：物理卡）
//     * @param reviewItemId(1:姓名不正确，2：邮寄地址不正确，3：照片不正确)
//     * @param userLevelId(会员等级 0:普通会员 1:银卡会员 2:金卡会员 3:白金会员 4:黑金会员)
//     * @param reviewModeId(审核方式 1:手动审核 2:自动审核)
//     * @param userType(是否外籍 0:非外籍 1:外籍)
//     * @param needReexamine                           是否需要复查，""-全部 1-需要复查 0-不需要复查
//     * @param additionalReviewStatus                  新驾照审核状态，""-全部 0:待审核  2: 审核不通过
//     * @return
//     */
//    @RequestMapping(value = "membershipInfo", method = RequestMethod.GET)
//    public DefaultWebRespVO queryMembershipInfoList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
//                                                    @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize, @RequestParam(value = "name", required = false) String name,
//                                                    @RequestParam(value = "mobilePhone", required = false) String mobilePhone, @RequestParam(value = "reviewStatus", required = false) Integer reviewStatus,
//                                                    @RequestParam(value = "cardStatus", required = false) String cardStatus, @RequestParam(value = "orgId", required = false) String orgId,
//                                                    @RequestParam(value = "province", required = false) String province, @RequestParam(value = "city", required = false) String city,
//                                                    @RequestParam(value = "area", required = false) String area, @RequestParam(value = "provinceOfApp", required = false) String provinceOfApp,
//                                                    @RequestParam(value = "cityOfApp", required = false) String cityOfApp, @RequestParam(value = "areaOfApp", required = false) String areaOfApp,
//                                                    @RequestParam(value = "agencyId", required = false) String agencyId, @RequestParam(value = "exemptDeposit", required = false) Integer exemptDeposit,
//                                                    @RequestParam(value = "infoOrigin", required = false) String infoOrigin, @RequestParam(value = "appKey", required = false) String appKey,
//                                                    @RequestParam(value = "createdStartTime", required = false) String createdStartTime, @RequestParam(value = "createdEndTime", required = false) String createdEndTime,
//                                                    @RequestParam(value = "reviewStartTime", required = false) String reviewStartTime, @RequestParam(value = "reviewEndTime", required = false) String reviewEndTime,
//                                                    @RequestParam(value = "drivingLicense", required = false) String drivingLicense, @RequestParam(value = "dataOrigin", required = false) Integer dataOrigin,
//                                                    @RequestParam(value = "cardNoType", required = false) Integer cardNoType, @RequestParam(value = "reviewItemId", required = false) String reviewItemId,
//                                                    @RequestParam(value = "isAll", defaultValue = "0") Integer isAll, @RequestParam(value = "userLevelId", required = false) Integer userLevelId,
//                                                    @RequestParam(value = "reviewModeId", required = false) Integer reviewModeId,
//                                                    @RequestParam(value = "reviewPassedStartTime", required = false) String reviewPassedStartTime,
//                                                    @RequestParam(value = "reviewPassedEndTime", required = false) String reviewPassedEndTime, @RequestParam(value = "status", required = false) Integer status,
//                                                    @RequestParam(value = "userType", required = false) Integer userType, @RequestParam(value = "needReexamine", required = false) String needReexamine,
//                                                    @RequestParam(value = "additionalReviewStatus", required = false) String additionalReviewStatus, HttpServletRequest request) {
//
//        PageBeanBO<MembershipInfoDTO> pageBeanBO = null;
//        try {
//            pageBeanBO = membershipInfoServiceImpl.getMembershipInfo(pageNum, pageSize, name, mobilePhone, reviewStatus, cardStatus, orgId, province, city, area,
//                    provinceOfApp, cityOfApp, areaOfApp, agencyId, exemptDeposit, infoOrigin, appKey, createdStartTime, createdEndTime, reviewStartTime, reviewEndTime, drivingLicense,
//                    dataOrigin, cardNoType, reviewItemId, isAll, userLevelId, reviewModeId, reviewPassedStartTime, reviewPassedEndTime, status, userType, needReexamine, additionalReviewStatus, request);
//        } catch (BusinessRuntimeException e) {
//            e.printStackTrace();
//            log.error("系统繁忙，请稍后重试：", e);
//            if(Constants.DEFAULT_ERR_CODE.equals(e.getErrorCode())){
//                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "系统繁忙，请稍后重试...");
//            }
//        } catch (Exception e){
//            e.printStackTrace();
//            log.error("查询会员信息失败：", e);
//            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "查询会员信息失败");
//        }
//        log.debug("会员信息一览...");
//        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
//    }
    
    /**
     * 会员信息一览
     *
     * @param pageNum                                 页码
     * @param pageSize                                每页显示条数
     * @param name                                    姓名（精确查询）
     * @param mobilePhone                             手机号（精确查询）
     * @param drivingLicense                          驾照号（精确查询）
     *
     * @param cardStatus                              卡类状态
     * @param orgId                                   会员所属公司
     * @param province                                注册所属地区（省）
     * @param city                                    注册所属地区（市）
     * @param area                                    注册所属地区（区）
     * @param agencyId                                关联企业（精确查询）
     * @param agencyName                              关联企业
     * @param exemptDeposit                           免押等级(0：不免除押金 1：普通车型 2：标准车型 3：中端车型 4：高端车型)
     * @param infoOrigin                              备注来源（模糊查询）
     * @param appKey                                  渠道来源（精确查询）
     * @param appKeyName                              渠道来源（精确查询）
     * @param createdStartTime                        注册开始时间
     * @param createdEndTime                          注册结束时间
     * @param reviewStartTime                         提交审批开始时间
     * @param reviewEndTime                           提交审批结束时间
     * @param dataOrigin                              注册平台（0：网点注册 1：网站注册 2：管理平台注册 3:手机APP 4:微信）
     * @param cardNoType                              卡类型（1：虚拟卡，2：物理卡）
     * @param reviewItemId(1:姓名不正确，2：邮寄地址不正确，3：照片不正确)
     * @param userLevelId(会员等级 0:普通会员 1:银卡会员 2:金卡会员 3:白金会员 4:黑金会员)
     * @param reviewModeId(审核方式 1:手动审核 2:自动审核)
     * @param userType(是否外籍 0:非外籍 1:外籍)
     * @param needReexamine                           是否需要复查，""-全部 1-需要复查 0-不需要复查
     * @param additionalReviewStatus                  新驾照审核状态，""-全部 0:待审核  2: 审核不通过
     * @param authenticationStatus            身份证审核状态  1未认证(未上传) 2未刷脸 3待认证(待人工认证) 4已认证 5认证不通过
     * @param licAuthStatus                  驾照审核状态 1:未认证 2:待认证 3:已认证 4:认证不通过
     * @param  accountStatus                 会员状态 正常 已冻结 已注销
     * @param  reviewStatus                   会员审核状态  （-1:资料不全 0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核）
     *
     *
     * @return
     */
    @RequestMapping(value = "membershipInfo", method = RequestMethod.GET)
    public DefaultWebRespVO queryMembershipInfoList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                    @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                    @RequestParam(value = "pkId", required = false) Long pkId,
                                                    @RequestParam(value = "name", required = false) String name,
                                                    @RequestParam(value = "mobilePhone", required = false) String mobilePhone, @RequestParam(value = "reviewStatus", required = false) Integer reviewStatus,
                                                    @RequestParam(value = "cardStatus", required = false) String cardStatus, @RequestParam(value = "orgId", required = false) String orgId,
                                                    @RequestParam(value = "province", required = false) String province, @RequestParam(value = "city", required = false) String city,
                                                    @RequestParam(value = "area", required = false) String area, @RequestParam(value = "authId", required = false) String authId,
                                                    @RequestParam(value = "agencyId", required = false) String agencyId, @RequestParam(value = "agencyName", required = false) String agencyName, @RequestParam(value = "exemptDeposit", required = false) Integer exemptDeposit,
                                                    @RequestParam(value = "infoOrigin", required = false) String infoOrigin, @RequestParam(value = "appKey", required = false) String appKey, @RequestParam(value = "appKeyName", required = false) String appKeyName,
                                                    @RequestParam(value = "createdStartTime", required = false) String createdStartTime, @RequestParam(value = "createdEndTime", required = false) String createdEndTime,
                                                    @RequestParam(value = "reviewStartTime", required = false) String reviewStartTime, @RequestParam(value = "reviewEndTime", required = false) String reviewEndTime,
                                                    @RequestParam(value = "drivingLicense", required = false) String drivingLicense, @RequestParam(value = "dataOrigin", required = false) Integer dataOrigin,
                                                    @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
                                                    @RequestParam(value = "cardNoType", required = false) Integer cardNoType, @RequestParam(value = "reviewItemId", required = false) String reviewItemId,
                                                    @RequestParam(value = "userLevelId", required = false) Integer userLevelId,
                                                    @RequestParam(value = "reviewModeId", required = false) Integer reviewModeId,
                                                    @RequestParam(value = "reviewPassedStartTime", required = false) String reviewPassedStartTime,
                                                    @RequestParam(value = "reviewPassedEndTime", required = false) String reviewPassedEndTime, @RequestParam(value = "status", required = false) Integer status,
                                                    @RequestParam(value = "userType", required = false) Integer userType, @RequestParam(value = "needReexamine", required = false) String needReexamine,
                                                    @RequestParam(value = "additionalReviewStatus", required = false) String additionalReviewStatus, @RequestParam(value = "unregisterStartTime", required = false) String unregisterStartTime,
                                                    @RequestParam(value = "unregisterEndTime", required = false) String unregisterEndTime,@RequestParam(value = "accountStatus", required = false) Integer accountStatus,
                                                    @RequestParam(value = "simpleMode", required = false) String simpleMode,
                                                    @RequestParam(value = "authenticationStatus", required = false) Integer authenticationStatus,
                                                    @RequestParam(value = "authenticateStatus", required = false) Integer licElementsAuthStatus,
                                                    @RequestParam(value = "licAuthStatus", required = false) Integer licAuthStatus,
                                                    @RequestParam(value = "sesameCreditAuthorization", required = false) Integer sesameCreditAuthorization,
                                                    @RequestParam(value = "idCardAuditStartTime", required = false) String idCardAuditStartTime,
                                                    @RequestParam(value = "idCardAuditEndTime", required = false) String idCardAuditEndTime,
                                                    @RequestParam(value = "idCardSubmitStartTime", required = false) String idCardSubmitStartTime,
                                                    @RequestParam(value = "idCardSubmitEndTime", required = false) String idCardSubmitEndTime,
                                                    @RequestParam(value = "idCardAuditType", required = false) Integer idCardAuditType,
                                                    @RequestParam(value = "licAuditStartTime", required = false) String licAuditStartTime,
                                                    @RequestParam(value = "licAuditEndTime", required = false) String licAuditEndTime,
                                                    @RequestParam(value = "licSubmitStartTime", required = false) String licSubmitStartTime,
                                                    @RequestParam(value = "licSubmitEndTime", required = false) String licSubmitEndTime,
                                                    @RequestParam(value = "licAuditType", required = false) Integer licAuditType,
                                                    HttpServletRequest request) {
    	DefaultWebRespVO result = null;
        
        try {
        	if(simpleMode == null && esMembershipInfoServiceImpl.isEsSearchEnabled()) {
	        	PageInfoBO<MembershipInfoDTO> pageInfoBO = esMembershipInfoServiceImpl.getMembershipInfo(pageNum, pageSize, pkId, name, mobilePhone, authId, reviewStatus,
	        			cardStatus, orgId, province, city, area, agencyId, agencyName, exemptDeposit, infoOrigin, appKey, appKeyName, createdStartTime, createdEndTime,
                        reviewStartTime, reviewEndTime, drivingLicense, dataOrigin, cardNoType, reviewItemId, userLevelId, reviewModeId, reviewPassedStartTime,
                        reviewPassedEndTime, status, userType, needReexamine, additionalReviewStatus, unregisterStartTime, unregisterEndTime, accountStatus,
                        authenticationStatus, licElementsAuthStatus, licAuthStatus, sesameCreditAuthorization,idCardAuditStartTime,idCardAuditEndTime,idCardSubmitStartTime,idCardSubmitEndTime,
                        idCardAuditType,licAuditStartTime,licAuditEndTime,licSubmitStartTime,licSubmitEndTime,licAuditType, request);
                result = DefaultWebRespVO.getSuccessVO(pageInfoBO);
            }
        	else{
                ListPageBeanBO<MembershipInfoDTO> pageBeanBO = membershipInfoServiceImpl.getMembershipInfo(pageNum, pageSize, pkId, name, mobilePhone, authId, reviewStatus, cardStatus, orgId, province, city, area,
                        null, null, null, agencyName, exemptDeposit, infoOrigin, appKeyName, createdStartTime, createdEndTime, reviewStartTime, reviewEndTime, drivingLicense,
                        dataOrigin, cardNoType, reviewItemId, isAll, userLevelId, reviewModeId, reviewPassedStartTime, reviewPassedEndTime, status, userType, needReexamine, additionalReviewStatus,
                        unregisterStartTime, unregisterEndTime, accountStatus, authenticationStatus, licElementsAuthStatus, licAuthStatus, sesameCreditAuthorization, idCardAuditStartTime,
                        idCardAuditEndTime,idCardSubmitStartTime,idCardSubmitEndTime, idCardAuditType,licAuditStartTime,licAuditEndTime,licSubmitStartTime,licSubmitEndTime,licAuditType,request);
        		result = DefaultWebRespVO.getSuccessVO(pageBeanBO);
        	}      	
        
        } catch (BusinessRuntimeException e) {
            log.error("系统繁忙，请稍后重试：", e);
            if(Constants.DEFAULT_ERR_CODE.equals(e.getErrorCode())){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "系统繁忙，请稍后重试...");
            }
        } catch (Exception e){
            log.error("查询会员信息失败：", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "查询会员信息失败。");
            
        }
        log.debug("会员信息一览...");
        return result;
    }

    /**
     * 导出信息一览
     *
     * @param authId                                  会员ID（字符串）
     * @param name                                    姓名（精确查询）
     * @param mobilePhone                             手机号（精确查询）
     * @param drivingLicense                          驾照号（精确查询）
     * @param reviewStatus                            会员状态
     * @param cardStatus                              卡类状态
     * @param orgId                                   会员所属公司
     * @param province                                注册所属地区（省）
     * @param city                                    注册所属地区（市）
     * @param area                                    注册所属地区（区）
     * @param provinceOfApp                           APP定位地区（省）
     * @param cityOfApp                               APP定位地区（市）
     * @param areaOfApp                               APP定位地区（区）
     * @param agencyId                                关联企业（模糊查询）
     * @param exemptDeposit                           免押等级(0：不免除押金 1：普通车型 2：标准车型 3：中端车型 4：高端车型)
     * @param infoOrigin                              备注来源（模糊查询）
     * @param appKey                                  渠道来源（模糊查询）
     * @param createdStartTime                        注册开始时间
     * @param createdEndTime                          注册结束时间
     * @param reviewStartTime                         提交审批开始时间
     * @param reviewEndTime                           提交审批结束时间
     * @param dataOrigin                              注册平台（0：网点注册 1：网站注册 2：管理平台注册 3:手机APP 4:微信）
     * @param cardNoType                              卡类型（1：虚拟卡，2：物理卡）
     * @param reviewItemId(1:姓名不正确，2：邮寄地址不正确，3：照片不正确)
     * @param userLevelId(会员等级 0:普通会员 1:银卡会员 2:金卡会员 3:白金会员 4:黑金会员)
     * @param reviewModeId(审核方式 1:手动审核 2:自动审核)
     * @param userType(是否外籍 1:非外籍 0:外籍)
     *
     * @param idCardAuditStartTime                    身份审核开始时间
     * @param idCardAuditEndTime                      身份审核结束时间
     * @param idCardSubmitStartTime                   身份证提交开始时间
     * @param idCardSubmitEndTime                     身份证提交结束时间
     * @param idCardAuditType                          身份证审核方式   (审核方式 1:手动审核 2:自动审核)
     *
     * @param licAuditStartTime                 驾驶证审核开始时间
     * @param licAuditEndTime                   驾照审核结束时间
     * @param licSubmitStartTime                驾照提交开始时间
     * @param licSubmitEndTime                   驾照提交结束时间
     * @param licAuditType                        驾照审核方式  (审核方式 1:手动审核 2:自动审核)
     * @return
     */
    @RequestMapping(value = "exportMembershipInfo", method = RequestMethod.GET)
    public DefaultWebRespVO exoprtMembershipInfo(@RequestParam(value = "pkId", required = false) Long pkId,
                                                 @RequestParam(value = "authId", required = false) String authIds, @RequestParam(value = "name", required = false) String name,
                                                 @RequestParam(value = "authIdKey", required = false) String authId,
                                                 @RequestParam(value = "mobilePhone", required = false) String mobilePhone, @RequestParam(value = "reviewStatus", required = false) Integer reviewStatus,
                                                 @RequestParam(value = "cardStatus", required = false) String cardStatus, @RequestParam(value = "orgId", required = false) String orgId,
                                                 @RequestParam(value = "province", required = false) String province, @RequestParam(value = "city", required = false) String city,
                                                 @RequestParam(value = "area", required = false) String area, @RequestParam(value = "provinceOfApp", required = false) String provinceOfApp,
                                                 @RequestParam(value = "cityOfApp", required = false) String cityOfApp, @RequestParam(value = "areaOfApp", required = false) String areaOfApp,
                                                 @RequestParam(value = "agencyId", required = false) String agencyId, @RequestParam(value = "agencyName", required = false) String agencyName, 
                                                 @RequestParam(value = "exemptDeposit", required = false) Integer exemptDeposit,
                                                 @RequestParam(value = "infoOrigin", required = false) String infoOrigin, 
                                                 @RequestParam(value = "appKey", required = false) String appKey, @RequestParam(value = "appKeyName", required = false) String appKeyName,
                                                 @RequestParam(value = "createdStartTime", required = false) String createdStartTime, @RequestParam(value = "createdEndTime", required = false) String createdEndTime,
                                                 @RequestParam(value = "reviewStartTime", required = false) String reviewStartTime, @RequestParam(value = "reviewEndTime", required = false) String reviewEndTime,
                                                 @RequestParam(value = "drivingLicense", required = false) String drivingLicense, @RequestParam(value = "dataOrigin", required = false) Integer dataOrigin,
                                                 @RequestParam(value = "cardNoType", required = false) Integer cardNoType, @RequestParam(value = "reviewItemId", required = false) String reviewItemId,
                                                 @RequestParam(value = "userLevelId", required = false) Integer userLevelId, @RequestParam(value = "reviewModeId", required = false) Integer reviewModeId,
                                                 @RequestParam(value = "reviewPassedStartTime", required = false) String reviewPassedStartTime,
                                                 @RequestParam(value = "reviewPassedEndTime", required = false) String reviewPassedEndTime, @RequestParam(value = "status", required = false) Integer status,
                                                 @RequestParam(value = "userType", required = false) Integer userType, @RequestParam(value = "unregisterStartTime", required = false) String unregisterStartTime,
                                                 @RequestParam(value = "unregisterEndTime", required = false) String unregisterEndTime, @RequestParam(value = "accountStatus", required = false) Integer accountStatus,
                                                 @RequestParam(value = "authenticationStatus", required = false) Integer authenticationStatus,
                                                 @RequestParam(value = "authenticateStatus", required = false) Integer licElementsAuthStatus,
                                                 @RequestParam(value = "licAuthStatus", required = false) Integer licAuthStatus,
                                                 @RequestParam(value = "sesameCreditAuthorization", required = false) Integer sesameCreditAuthorization,
                                                 @RequestParam(value = "idCardAuditStartTime", required = false) String idCardAuditStartTime,
                                                 @RequestParam(value = "idCardAuditEndTime", required = false) String idCardAuditEndTime,
                                                 @RequestParam(value = "idCardSubmitStartTime", required = false) String idCardSubmitStartTime,
                                                 @RequestParam(value = "idCardSubmitEndTime", required = false) String idCardSubmitEndTime,
                                                 @RequestParam(value = "idCardAuditType", required = false) Integer idCardAuditType,
                                                 @RequestParam(value = "licAuditStartTime", required = false) String licAuditStartTime,
                                                 @RequestParam(value = "licAuditEndTime", required = false) String licAuditEndTime,
                                                 @RequestParam(value = "licSubmitStartTime", required = false) String licSubmitStartTime,
                                                 @RequestParam(value = "licSubmitEndTime", required = false) String licSubmitEndTime,
                                                 @RequestParam(value = "licAuditType", required = false) Integer licAuditType,
                                                 HttpServletRequest request, HttpServletResponse response) {
        DefaultServiceRespDTO respDTO = membershipInfoServiceImpl.exportMembershipInfo(authIds, pkId, name, mobilePhone, authId, reviewStatus, cardStatus, orgId, province, city, area, provinceOfApp,
                cityOfApp, areaOfApp, agencyId, agencyName, exemptDeposit, infoOrigin, appKey, appKeyName, createdStartTime, createdEndTime, reviewStartTime, reviewEndTime, drivingLicense, dataOrigin,
                cardNoType, reviewItemId, userLevelId, reviewModeId, reviewPassedStartTime, reviewPassedEndTime, status, userType, unregisterStartTime, unregisterEndTime, accountStatus,
                authenticationStatus, licElementsAuthStatus, licAuthStatus, sesameCreditAuthorization,idCardAuditStartTime,idCardAuditEndTime,idCardSubmitStartTime,idCardSubmitEndTime,
                idCardAuditType,licAuditStartTime,licAuditEndTime,licSubmitStartTime,licSubmitEndTime,licAuditType,request, response);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("导出会员信息...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "会员资料导出成功");
    }


    /**
     * 批量修改关联企业
     *
     * @param membershipAgencyVO 关联企业信息
     * @return
     */
    @RequestMapping(value = "membershipAgencyInfo", method = RequestMethod.PUT)
    public DefaultWebRespVO updateAgency(@RequestBody MembershipAgencyVO membershipAgencyVO, HttpServletRequest request) {
        MembershipAgencyDTO membershipAgencyDTO = new MembershipAgencyDTO();
        BeanCopyUtils.copyProperties(membershipAgencyVO, membershipAgencyDTO);
        //update by Elin 20180816 批量修改关联企业 version1.4.0
        List<String> authIds = membershipAgencyVO.getAuthIds();
        DefaultServiceRespDTO respDTO = membershipInfoServiceImpl.updateAgency(authIds, membershipAgencyDTO, request);
        if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        } else if (respDTO.getCode() == -10) {
            return new DefaultWebRespVO("-10", respDTO.getMessage());
        }
        log.debug("批量修改关联企业...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, respDTO.getMessage());
    }

    /**
     * 批量修改免押等级
     *
     * @param authId 证件号
     * @return
     */
    @RequestMapping(value = "membershipExemptdepositInfo/{authId}", method = RequestMethod.PUT)
    public DefaultWebRespVO updateExemptdeposit(@PathVariable String authId, @RequestBody MembershipAgencyVO membershipAgencyVO, HttpServletRequest request) {
        MembershipAgencyDTO membershipAgencyDTO = new MembershipAgencyDTO();
        BeanCopyUtils.copyProperties(membershipAgencyVO, membershipAgencyDTO);
        DefaultServiceRespDTO respDTO = membershipInfoServiceImpl.updateExemptdeposit(authId, membershipAgencyDTO, request);
        if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("批量修改免除租车押金...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功");
    }

    /**
     * 批量修改备注来源
     *
     * @param authId 证件号
     * @return
     */
    @RequestMapping(value = "membershipInfoOrginInfo/{authId}", method = RequestMethod.PUT)
    public DefaultWebRespVO updateInfoOrgin(@PathVariable String authId, @RequestBody MembershipAgencyVO membershipAgencyVO, HttpServletRequest request) {
        MembershipAgencyDTO membershipAgencyDTO = new MembershipAgencyDTO();
        BeanCopyUtils.copyProperties(membershipAgencyVO, membershipAgencyDTO);
        DefaultServiceRespDTO respDTO = membershipInfoServiceImpl.updateInfoOrgin(authId, membershipAgencyDTO, request);
        if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("修改备注来源...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功");
    }

    /**
     * 新增简单会员
     * @param membershipVO  手机号 和 姓名
     * @param request
     * @return
     */
    @RequestMapping(value = "simpleMembershipInfo", method = RequestMethod.POST)
    public DefaultWebRespVO quickAddMembershipInfo(@RequestBody MembershipVO membershipVO, HttpServletRequest request) {
        MembershipDTO membershipDTO = new MembershipDTO();
        BeanCopyUtils.copyProperties(membershipVO, membershipDTO);
        DefaultServiceRespDTO respDTO = membershipInfoServiceImpl.quickAddMembershipInfo(membershipDTO, request);
        if (respDTO.getCode() != 0) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }else{
            return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, respDTO.getMessage());
        }
    }

    /**
     * 新增会员
     *
     * @param membershipVO
     * @param request
     * @return
     */
    @RequestMapping(value = "membershipInfo", method = RequestMethod.POST)
    public DefaultWebRespVO addMembershipInfo(
//            @RequestParam(value = "drivingLicenseImg") CommonsMultipartFile drivingLicenseImg,
//                                              @RequestParam(value = "fileNoImg") CommonsMultipartFile fileNoImg,
//                                              @RequestParam(value = "idcardPic", required = false) CommonsMultipartFile idcardPic,
//                                              @RequestParam(value = "holdIdcardPic", required = false) CommonsMultipartFile holdIdcardPic,
//                                              @RequestParam(value = "faceRecognitionImg") CommonsMultipartFile faceRecognitionImg,
                                              MembershipVO membershipVO, HttpServletRequest request) {
        MultipartHttpServletRequest multipartRequest =     (MultipartHttpServletRequest) request;

        CommonsMultipartFile drivingLicenseImg = null;
        try {
            drivingLicenseImg = (CommonsMultipartFile)multipartRequest.getFile("drivingLicenseImg");
        }catch (Exception e){
            log.warn("drivingLicenseImg：前端传参错误！");
            e.printStackTrace();
        }
        //驾照副本
        CommonsMultipartFile fileNoImg = null;
        try {
           fileNoImg = (CommonsMultipartFile)multipartRequest.getFile("fileNoImg");
        }catch (Exception e){
            log.warn("fileNoImg：前端传参错误！");
            e.printStackTrace();
        }
        CommonsMultipartFile idcardPic = null;
        try {
            idcardPic = (CommonsMultipartFile)multipartRequest.getFile("idCardPic");
        }catch (Exception e){
            log.warn("idCardPic：前端传参错误！");
            e.printStackTrace();
        }

        CommonsMultipartFile idcardBackPic = null;
        try {
            idcardBackPic = (CommonsMultipartFile)multipartRequest.getFile("idCardBackPic");
        }catch (Exception e){
            log.warn("idCardBackPic：前端传参错误！");
            e.printStackTrace();
        }

        CommonsMultipartFile holdIdcardPic = null;
        try {
            holdIdcardPic = (CommonsMultipartFile)multipartRequest.getFile("holdIdcardPic");
        }catch (Exception e){
            log.warn("holdIdcardPic：前端传参错误！");
            e.printStackTrace();
        }
        CommonsMultipartFile faceRecognitionImg = null;
        try {
            faceRecognitionImg = (CommonsMultipartFile)multipartRequest.getFile("faceRecognitionImg");
        }catch (Exception e){
            log.warn("faceRecognitionImg：前端传参错误！");
            e.printStackTrace();
        }


        MembershipDTO membershipDTO = new MembershipDTO();
        BeanCopyUtils.copyProperties(membershipVO, membershipDTO);
        DefaultServiceRespDTO respDTO = membershipInfoServiceImpl.addMembershipInfo(drivingLicenseImg, fileNoImg, idcardPic,idcardBackPic,holdIdcardPic,faceRecognitionImg,membershipDTO, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("新增会员信息...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功");
    }

    /**
     * 修改会员
     *
     * @param membershipVO
     * @param authId       证件号
     * @param request
     * @return
     */
    @RequestMapping(value = "membershipInfo/{authId}", method = RequestMethod.PUT)
    public DefaultWebRespVO updateMembershipInfo(@PathVariable String authId, @RequestBody MembershipVO membershipVO, HttpServletRequest request) {
        MembershipDTO membershipDTO = new MembershipDTO();
        BeanCopyUtils.copyProperties(membershipVO, membershipDTO);
        DefaultServiceRespDTO respDTO = null;
        try {
            respDTO = membershipInfoServiceImpl.updateMembershipInfo(authId, membershipDTO, request);
        }catch (Exception e){
            log.error("修改会员失败, authId=" + authId, e);
            DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("修改会员信息失败！");
            return defaultWebRespVO;
        }
        if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("修改会员信息...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功");
    }

    /**
     * 批量修改会员所属单位
     *
     * @param membershipVO
     * @param request
     * @return
     */
    @RequestMapping(value = "mmpOrgId/{authId}", method = RequestMethod.PUT)
    public DefaultWebRespVO updateMmpOrgId(@PathVariable String authId, @RequestBody MembershipVO membershipVO, HttpServletRequest request) {
        MembershipDTO membershipDTO = new MembershipDTO();
        BeanCopyUtils.copyProperties(membershipVO, membershipDTO);
        DefaultServiceRespDTO respDTO = membershipInfoServiceImpl.updateMmpOrgId(authId, membershipDTO, request);
        if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("批量修改会员所属单位...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功");
    }

    /**
     * 查询会员退押申请记录
     *
     * @param pageNum        页码
     * @param pageSize       每页显示条数
     * @param isAll          0-不统计 1-统计总数
     * @param name           姓名（精确查询）
     * @param mobilePhone    手机号（精确查询）
     * @param applyStartTime 申请开始日期（精确查询）
     * @param applyEndTime   申请结束日期（精确查询）
     * @param applyStatus    处理状态（精确查询）
     * @param orgId          所属机构（精确查询）
     * @return
     */
    @RequestMapping(value = "memberDrawBackList", method = RequestMethod.GET)
    public DefaultWebRespVO memberDrawBackList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                               @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                               @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
                                               @RequestParam(value = "name", required = false) String name,
                                               @RequestParam(value = "mobilePhone", required = false) String mobilePhone,
                                               @RequestParam(value = "applyStartTime", required = false) String applyStartTime,
                                               @RequestParam(value = "applyEndTime", required = false) String applyEndTime,
                                               @RequestParam(value = "applyStatus", required = false) Integer applyStatus,
                                               @RequestParam(value = "orgId", required = false) String orgId) {
    	try {
    		 if(StringUtils.isBlank(mobilePhone) && (StringUtils.isBlank(applyStartTime) || StringUtils.isBlank(applyEndTime))) {
    			 return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "至少要手机号或时间其中一项才可查询");
             }
			if(StringUtils.isBlank(mobilePhone) && StringUtils.isNotBlank(applyStartTime) && StringUtils.isNotBlank(applyEndTime)) {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			    Calendar calendar = Calendar.getInstance();
			    	calendar.setTime(sdf.parse(applyStartTime));
			    	calendar.add(Calendar.MONTH, 3);
			    	String endTime = DateFormatUtils.ISO_DATE_FORMAT.format(calendar.getTime());
			    	if(endTime.compareTo(applyEndTime)<0) {
			    		return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "无手机号，时间不能超过三个月");
			    	}
			}
		} catch (ParseException e) {
			new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "查询失败");
			log.debug("查询异常"+e);
		}
        PageBeanBO<ApplyDrawBackInfoDTO> pageBeanBO = membershipInfoServiceImpl.memberDrawBackList(pageNum, pageSize, isAll, name, mobilePhone,
                applyStartTime, applyEndTime, applyStatus, orgId);
        log.debug("查询会员退押申请记录");
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    /**
     * 导出会员退押申请记录
     *
     * @param name           姓名（精确查询）
     * @param mobilePhone    手机号（精确查询）
     * @param applyStartTime 申请开始日期（精确查询）
     * @param applyEndTime   申请结束日期（精确查询）
     * @param orgId          所属机构（精确查询）
     * @return
     */
    @RequestMapping(value = "exportMemberDrawBackList", method = RequestMethod.GET)
    public DefaultWebRespVO exportMemberDrawBackList(@RequestParam(value = "name", required = false) String name,
                                                     @RequestParam(value = "mobilePhone", required = false) String mobilePhone,
                                                     @RequestParam(value = "applyStartTime", required = false) String applyStartTime,
                                                     @RequestParam(value = "applyEndTime", required = false) String applyEndTime,
                                                     @RequestParam(value = "orgId", required = false) String orgId,
                                                     @RequestParam(value = "applyStatus", required = false) Integer applyStatus,
                                                     HttpServletRequest request, HttpServletResponse response) {
        DefaultServiceRespDTO respDTO = membershipInfoServiceImpl.exportMemberDrawBackList(name, mobilePhone, applyStartTime,
                applyEndTime, orgId, applyStatus, request, response);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("导出会员退押申请记录");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "会员退押申请记录导出成功！");
    }


    /**
     * 批量修改关联企业会员错误信息导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "exportAgencyMemberErrorMsg", method = RequestMethod.GET)
    public DefaultWebRespVO exportAgencyMemberErrorMsg(HttpServletRequest request, HttpServletResponse response) {
        DefaultServiceRespDTO respDTO = membershipInfoServiceImpl.exportAgencyMemberErrorMsg(request, response);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("批量修改关联企业会员错误信息导出");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "批量修改关联企业会员错误信息导出成功");
    }

    /**
     * 重新认证
     * @param authId
     * @return
     */
    @RequestMapping(value = "reCertification/{authId}", method = RequestMethod.PUT)
    public DefaultWebRespVO reCertification(@PathVariable String authId, HttpServletRequest request){
        DefaultServiceRespDTO defaultServiceRespDTO = membershipInfoServiceImpl.reCertification(authId, request);
        if (defaultServiceRespDTO.getCode() == -1){
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, defaultServiceRespDTO.getMessage());
        }
        log.debug("会员驾照三要素重新认证");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "三要素重新认证接口调用成功");
    }

    /**
     *  会员驾照三要素认证日志
     * @param authId
     * @return
     */
    @RequestMapping(value = "getDriverLicenseAuthenticateLog", method = RequestMethod.GET)
    public DefaultWebRespVO getDriverLicenseAuthenticateLog(@RequestParam("authId") String authId){
        log.debug("获取会员三要素认证日志");
        DefaultWebRespVO respDTO = membershipInfoServiceImpl.getDriverLicenseAuthenticateLog(authId);
        return respDTO;
    }

    /**
     * 会员修改手机号
     * @param authId
     * @return
     */
    @RequestMapping(value = "updateUserMobile/{authId}/{mobile}", method = RequestMethod.PUT)
    public DefaultWebRespVO updateUserMobile(@PathVariable("authId") String authId, @PathVariable("mobile") String mobile, HttpServletRequest request){
        log.debug("会员修改手机号");
        DefaultServiceRespDTO respDTO = membershipInfoServiceImpl.updateUserMobile(authId, mobile, request);
        if (respDTO.getCode() == -1){
            return new DefaultWebRespVO("-1", respDTO.getMessage());
        }
        return new DefaultWebRespVO("0", respDTO.getMessage());
    }
}
