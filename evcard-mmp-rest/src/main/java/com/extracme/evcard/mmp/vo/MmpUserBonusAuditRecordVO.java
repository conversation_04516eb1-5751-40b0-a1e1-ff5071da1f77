package com.extracme.evcard.mmp.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MmpUserBonusAuditRecordVO implements Serializable {

    //当前页
    @ApiModelProperty(value = "当前页")
    private Integer pageNum = 1;

    //页面数量
    @ApiModelProperty(value = "页面数量")
    private Integer pageSize = 10;

    //会员的id编号
    @ApiModelProperty(value = "会员的id编号")
    private long  userId;

    //邀请人的id
    @ApiModelProperty(value = "邀请人的id")
    private String originAuthId;

    //邀请人所属公司id
    @ApiModelProperty(value = "邀请人所属公司id")
    private String originOrgId;

    //会员所属公司的名字
    @ApiModelProperty(value = "会员所属公司的名字")
    private String originOrgName;

    //邀请人的姓名
    @ApiModelProperty(value = "邀请人的姓名")
    private String originName;

    //邀请人的手机号
    @ApiModelProperty(value = "邀请人的手机号")
    private String originMobilePhone;

    //邀请人的微信openid
    @ApiModelProperty(value = "邀请人的微信openid")
    private String weiXinOpenId;

    //订单实付金额
    @ApiModelProperty(value = "订单实付金额")
    private BigDecimal totalAmount;

    //结算日期
    @ApiModelProperty(value = "结算日期")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date orderPayTime;

    //结算开始时间日期
    @ApiModelProperty(value = "结算开始时间日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date orderPayStartTime;
    //结算结束时间日期
    @ApiModelProperty(value = "结算结束时间日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date orderPayShutDownTime;

    //被邀请人的订单列表
    @ApiModelProperty(value = "被邀请人的订单列表")
    private Integer rewardCount;

    //审核日期
    @ApiModelProperty(value = "审核日期")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date reviewTime;
    //审核开始时间
    @ApiModelProperty(value = "审核开始时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date reviewStartTime;
    //审核结束时间
    @ApiModelProperty(value = "审核结束时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date reviewShutDownTime;

    //审核状态
    @ApiModelProperty(value = "审核状态")
    private Integer rewardStatus;

    //审核人名称
    @ApiModelProperty(value = "审核人名称")
    private String auditName;

    //被邀请人的信息
    @ApiModelProperty(value = "被邀请人的信息")
    private List<Map<String,Object>> mapList;

}
