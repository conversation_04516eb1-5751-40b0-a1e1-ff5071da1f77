package com.extracme.evcard.mmp.vo;

/**
 *
 * 项目名称: evcard-mmp-rest
 * 类名称: CardInfoVO
 * 类描述: TODO
 * 创建人: chenjx-陈佳鑫
 * 创建时间: 2017年9月26日 下午3:20:44
 * 修改备注:
 *
 *      * @param hyType 会员类型（0 外部会员，1内部会员，2企业会员）
     * @param authId 组织id
     * @param cardNo 卡号
     * @param status 卡操作  0暂停会员卡 1恢复会员卡 2注销会员卡
     * @param remark 备注
     * @param internalNo 内部卡号
     * @param request
     *
 * @version1.0
 *
 */
public class CardInfoVO {
    /** 会员类型（0 外部会员，1内部会员，2企业会员）*/
    private String hyType;
    /** 组织id */
    private String authId;

    /** 卡号 */
    private String cardNo;
    /** 卡操作  0暂停会员卡 1恢复会员卡 2注销会员卡 */
    private String status;
    /** 备注 */
    private String remark;
    /** 内部卡号 */
    private String internalNo;
    /** 卡恢复时间   1：七天  2：三十天 3：永久**/
    private String recoverTime;

    public String getRecoverTime() {
        return recoverTime;
    }

    public void setRecoverTime(String recoverTime) {
        this.recoverTime = recoverTime;
    }

    public String getHyType() {
        return hyType;
    }

    public void setHyType(String hyType) {
        this.hyType = hyType;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getInternalNo() {
        return internalNo;
    }

    public void setInternalNo(String internalNo) {
        this.internalNo = internalNo;
    }

}
