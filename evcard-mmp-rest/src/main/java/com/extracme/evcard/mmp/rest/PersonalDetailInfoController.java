package com.extracme.evcard.mmp.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.activity.dto.LotteryRecordDto;
import com.extracme.evcard.activity.dto.LotteryRecordQueryDto;
import com.extracme.evcard.activity.dubboService.ILotteryActivityService;
import com.extracme.evcard.mmp.bo.ContractInfoByPersonal;
import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.service.IMemberProfileService;
import com.extracme.evcard.mmp.service.IMmpMemberReviewService;
import com.extracme.evcard.mmp.service.IPersonalDetailInfoService;
import com.extracme.evcard.mmp.vo.*;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import java.util.List;
import java.util.Map;

/**
 * 项目名称：evcard-mmp-rest 类名称：PersonalDetailInfoController 类描述：会员个人信息Controller
 * 创建：qianhao 创建时间：2017年9月21日下午3:15:32 修改备注
 *
 * @version 1.0
 */
@RestController
@RequestMapping("api")
public class PersonalDetailInfoController {

	private final Logger log = LoggerFactory.getLogger(this.getClass());
	@Resource
	IPersonalDetailInfoService iPersonalDetailInfoServiceImpl;

	@Resource
    IMmpMemberReviewService mmpMemberReviewServiceImpl;

	@Resource
	IMemberProfileService memberProfileServiceImpl;

	@Resource
	ILotteryActivityService lotteryActivityService;

	/**
	 * 会员基本资料
	 *
	 * @param authId
	 *            用户id
	 * @param agencyId
	 *            每页显示条数
	 * @return
	 */
	@RequestMapping(value = "initLoad", method = RequestMethod.GET)
	public DefaultWebRespVO initLoad(@RequestParam(value = "authId") String authId,
			@RequestParam(value = "agencyId") String agencyId) {
		MembershipDetailDTO membershipDetailDTO = memberProfileServiceImpl.getMemberDetailByAuthId(authId, agencyId);
		if (null == membershipDetailDTO) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "查询会员信息不存在");
		}
		return DefaultWebRespVO.getSuccessVO(membershipDetailDTO);
	}

	/**
	 * 重新验证
	 *
	 * @param authId
	 *            用户id
	 * @return
	 */
	@RequestMapping(value = "againVerify/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO againVerify(@PathVariable String authId, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.againVerify(authId, request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		Map<String, String> map = JSON.parseObject(respDTO.getData(), Map.class);
		vo.setData(map);
		vo.setMessage("提交成功！");
		return vo;
	}

	/**
	 * 驾照冒用
	 *
	 * @param authId
	 *            用户id
	 * @return
	 */
	@RequestMapping(value = "handleCounterfeit/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO handleCounterfeit(@PathVariable String authId, @RequestBody MembershipVO membershipVO,
			HttpServletRequest request) {

		MembershipDTO membershipDTO = new MembershipDTO();
		BeanCopyUtils.copyProperties(membershipVO, membershipDTO);
		membershipDTO.setAuthId(authId);
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.updateHandleCounterfeit(membershipDTO, request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功！");
	}

	/**
	 * 审核通过
	 *
	 * @param authId
	 *            用户id
	 * @return
	 */
	@RequestMapping(value = "updateAuthIdItems/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO updateAuthIdItems(@PathVariable String authId,
			@RequestBody UpdateAuthIdItemsVO updateAuthIdItemsVO, HttpServletRequest request) {

		UpdateAuthIdItemsDTO updateAuthIdItemsDTO = new UpdateAuthIdItemsDTO();
		BeanCopyUtils.copyProperties(updateAuthIdItemsVO, updateAuthIdItemsDTO);
		updateAuthIdItemsDTO.setAuthId(authId);
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.updateAuthIdItems(updateAuthIdItemsDTO, request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功！");
	}

	/**
	 * 更新用户审核项
	 *
	 * @param authId
	 *            用户id
	 * @return
	 */
	@RequestMapping(value = "updateReviewItems/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO updateReviewItems(@PathVariable String authId,
			@RequestBody UpdateAuthIdItemsVO updateAuthIdItemsVO, HttpServletRequest request) {

		UpdateAuthIdItemsDTO updateAuthIdItemsDTO = new UpdateAuthIdItemsDTO();
		BeanCopyUtils.copyProperties(updateAuthIdItemsVO, updateAuthIdItemsDTO);
		updateAuthIdItemsDTO.setAuthId(authId);
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.updateReviewItems(updateAuthIdItemsDTO, request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功！");
	}

	/**
	 * 审核不通过
	 *
	 * @param authId
	 *            用户id
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "sendNotThroughReasons/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO sendNotThroughReasons(@PathVariable String authId,
			@RequestBody UpdateAuthIdItemsVO updateAuthIdItemsVO, HttpServletRequest request) throws Exception {

		MemberReviewInput updateAuthIdItemsDTO = new MemberReviewInput();
		BeanCopyUtils.copyProperties(updateAuthIdItemsVO, updateAuthIdItemsDTO);
		updateAuthIdItemsDTO.setAuthId(authId);
		DefaultServiceRespDTO respDTO = mmpMemberReviewServiceImpl.reviewFailed(updateAuthIdItemsDTO,
				request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功！");
	}

	/**
	 * 清空设备更换次数
	 *
	 * @param authId
	 *            用户id
	 * @return
	 */
	@RequestMapping(value = "cleanChangeNum/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO cleanChangeNum(@PathVariable String authId, HttpServletRequest request) {

		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.updateCleanChangeNum(authId, request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功！");
	}

	/**
	 * 重置密码
	 *
	 * @param authId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "resetPassword/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO resetPassword(@PathVariable String authId, HttpServletRequest request) {
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.updatePassword(authId, request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功！");

	}

	/**
	 * 删除会员
	 *
	 * @param authId
	 * @param accountReasonVO
	 *            :删除原因
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "deleteMember/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO deleteMember(@PathVariable String authId, @RequestBody AccountReasonVO accountReasonVO,
			HttpServletRequest request) {
		AccountReasonDTO accountReasonDTO = new AccountReasonDTO();
		BeanCopyUtils.copyProperties(accountReasonVO, accountReasonDTO);
		try {
			DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.deleteMember(authId, accountReasonDTO, request);
			if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
			return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功！");
		}
		catch (Exception ex) {
		    log.error("删除失败， authId=" + authId, ex);
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "提交失败！");
		}
	}

	/**
	 * 账户信息
	 *
	 * @param authId
	 * @return
	 */
	@RequestMapping(value = "accountInfo/{authId}", method = RequestMethod.GET)
	public DefaultWebRespVO accountInfoList(@PathVariable String authId) {
		AccountInfoDTO accountInfoDTO = iPersonalDetailInfoServiceImpl.query(authId);

		log.debug("账户一览...");
		return DefaultWebRespVO.getSuccessVO(accountInfoDTO);
	}


	@RequestMapping(value = "lottery/list", method = RequestMethod.POST)
	public DefaultWebRespVO accountInfoList(@RequestBody LotteryRecordQueryDto queryDto) {
		try{
			if(queryDto != null && StringUtils.isNotBlank(queryDto.getAuthId())) {
				PageBeanBO<LotteryRecordDto> list = lotteryActivityService.queryPage(queryDto);
				return DefaultWebRespVO.getSuccessVO(list);
			}
		}catch (Exception ex) {
			log.error("查询用户抽奖记录失败, authId=" + queryDto.getAuthId(), ex);
		}
		return new DefaultWebRespVO("-1", "查询失败");
	}

	/**
	 * 预授权转押金
	 *
	 * @param authId
	 *            证件号
	 * @param accountReasonVO
	 * @return
	 */
	@RequestMapping(value = "receiveMoney/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO updateReceiveMoney(@PathVariable String authId,
			@RequestBody AccountReasonVO accountReasonVO, HttpServletRequest request) {
		AccountReasonDTO accountReasonDTO = new AccountReasonDTO();
		BeanCopyUtils.copyProperties(accountReasonVO, accountReasonDTO);
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.updateReceivemoney(authId, accountReasonDTO,
				request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("预授权转押金...");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "操作成功！");

	}

	/**
	 * 预授权交易一览
	 *
	 * @param pageNum
	 *            查询页码
	 * @param pageSize
	 *            每页查询条数
	 * @param authId
	 *            证件号
	 * @return
	 */
	@RequestMapping(value = "queryRecords/{authId}", method = RequestMethod.GET)
	public DefaultWebRespVO queryRecords(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
			@RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
			@RequestParam(value = "isAll", defaultValue = "0") Integer isAll, @PathVariable String authId) {
		PageBeanBO<AmountChargeHistoryDTO> pageBeanBO = iPersonalDetailInfoServiceImpl.queryRecords(pageNum, pageSize,
				isAll, authId);
		log.debug("交易记录一览...");
		return DefaultWebRespVO.getSuccessVO(pageBeanBO);

	}

	/**
	 * 订单情况一览
	 *
	 * @param pageNum
	 *            第几页
	 * @param pageSize
	 *            每页显示几条
	 * @param authId
	 *            会员证件号
	 * @return 订单信息
	 */
	@RequestMapping(value = "orderInfoList/{authId}", method = RequestMethod.GET)
	public DefaultWebRespVO queryOrderInfoList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
			@RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize, @PathVariable String authId,
			@RequestParam(value = "orderType", defaultValue = "0") String orderType,
			@RequestParam(value = "isAll", defaultValue = "0") Integer isAll) {
		PageBeanBO<OrderDTO> pageBeanBO = iPersonalDetailInfoServiceImpl.queryAll(pageNum, pageSize, authId, orderType,
				isAll);

		log.debug("订单情况一览...");
		return DefaultWebRespVO.getSuccessVO(pageBeanBO);

	}

	/**
	 * 订单情况一览
	 *
	 * @param pageNum
	 *            第几页
	 * @param pageSize
	 *            每页显示几条
	 * @param authId
	 *            会员证件号
	 * @return 订单信息
	 */
	@RequestMapping(value = "mdOrderInfoList/{authId}", method = RequestMethod.GET)
	public DefaultWebRespVO queryMdOrderInfoList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
											   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize, @PathVariable String authId) {
		PageBeanBO<ContractInfoByPersonal> pageBeanBO = iPersonalDetailInfoServiceImpl.queryMdOrderInfoList(pageNum, pageSize, authId);
		return DefaultWebRespVO.getSuccessVO(pageBeanBO);

	}

	/**
	 * 日志一览
	 *
	 * @param pageNum
	 * @param pageSize
	 * @param authId
	 * @param isAll
	 * @return
	 */
	@RequestMapping(value = "userOperatorLog/{authId}", method = RequestMethod.GET)
	public DefaultWebRespVO queryUserInfoList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
			@RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize, @PathVariable String authId,
			@RequestParam(value = "isAll", defaultValue = "0") Integer isAll) {
		PageBeanBO<UserOperatorLogDTO> pageBeanBO = iPersonalDetailInfoServiceImpl.queryUserAll(pageNum, pageSize,
				authId, isAll);

		log.debug("日志操作情况一览...");
		return DefaultWebRespVO.getSuccessVO(pageBeanBO);

	}

	/**
	 * 会员卡资料
	 *
	 * @param authId
	 *            会员id
	 * @param authType
	 * @return
	 */
	@RequestMapping(value = "cardInfors/{authId}", method = RequestMethod.GET)
	public DefaultWebRespVO queryCard(@PathVariable String authId,
			@RequestParam(value = "authType", defaultValue = "0") String authType) {
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.getMembershipCardInfo(authId, authType);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		List<MembershipCardDTO> list = JSON.parseObject(respDTO.getData(), List.class);
		log.debug("会员卡信息一览...");
		return DefaultWebRespVO.getSuccessVO(list);
	}

	/**
	 * 验证暂停或注销的会员卡是否在使用
	 *
	 * @param cardNo
	 *            卡号
	 * @param cardStatus
	 *            需要进行的操作 0暂停； 1恢复 2注销
	 * @param action
	 *            执行操作中文名称 暂停； 恢复 注销
	 * @return
	 */
	@RequestMapping(value = "cardUseringInfo", method = RequestMethod.GET)
	public DefaultWebRespVO CheckOrderByCardNo(@RequestParam(value = "cardNo", required = true) String cardNo,
			@RequestParam(value = "cardStatus", required = true) Integer cardStatus,
			@RequestParam(value = "action", defaultValue = "0") String action) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.checkOrderByCardNo(cardNo, cardStatus, action);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		Map<String, Object> map = JSON.parseObject(respDTO.getData(), Map.class);
		log.debug("验证暂停或注销的会员卡是否在使用...");
		vo.setMessage("提交成功");
		vo.setData(map);
		return vo;
	}

	/**
	 * 更新卡信息
	 *
	 * @param hyType
	 *            会员类型（0 外部会员，1内部会员，2企业会员）
	 * @param authId
	 *            组织id
	 * @param cardNo
	 *            卡号
	 * @param status
	 *            卡操作 0暂停会员卡 1恢复会员卡 2注销会员卡
	 * @param remark
	 *            备注
	 * @param internalNo
	 *            内部卡号
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "cardInfo/{cardNo}", method = RequestMethod.PUT)
	public DefaultWebRespVO updateCardInfo(@PathVariable String cardNo, @RequestBody CardInfoVO cardInfoVO,
			HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		CardInfoDTO cardInfoDTO = new CardInfoDTO();
		BeanCopyUtils.copyProperties(cardInfoVO, cardInfoDTO);
		cardInfoDTO.setCardNo(cardNo);
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.updateCardInfoStatus(cardInfoDTO, request);
		Map<String, Object> map = JSON.parseObject(respDTO.getData(), Map.class);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("修改卡信息...");
		vo.setMessage("提交成功");
		vo.setData(map);
		return vo;
	}

	/**
	 * 签约认证
	 *
	 * @param mobilePhone
	 *            手机号
	 * @return
	 */
	@RequestMapping(value = "signCheckResultWithPuFa/{mobilePhone}", method = RequestMethod.PUT)
	public DefaultWebRespVO updateSignCheckResultWithPuFa(@PathVariable String mobilePhone,
			HttpServletRequest request) {
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.updateSignCheckResultWithPuFa(mobilePhone,
				request);
		if (respDTO.getCode() == -1) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		if (respDTO.getCode() == 99) {
			return new DefaultWebRespVO(Contants.RETURN_AUTHORITY_CODE, respDTO.getMessage());
		}
		log.debug("签约认证...");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "验证结果已发送，签约审核同步结果成功！");
	}

	/**
	 * 冻结押金
	 *
	 * @param frozenDepositWithPuFaVO
	 *            手机号
	 * @return
	 */
	@RequestMapping(value = "frozenDepositWithPuFa", method = RequestMethod.PUT)
	public DefaultWebRespVO updateFrozenDepositWithPuFa(@RequestBody FrozenDepositWithPuFaVO frozenDepositWithPuFaVO,
			HttpServletRequest request) {
		FrozenDepositWithPuFaDTO frozenDepositWithPuFaDTO = new FrozenDepositWithPuFaDTO();
		BeanCopyUtils.copyProperties(frozenDepositWithPuFaVO, frozenDepositWithPuFaDTO);
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl
				.updateFrozenDepositWithPuFa(frozenDepositWithPuFaDTO);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("冻结押金...");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "冻结押金成功！");
	}

	/**
	 * 移除黑名单
	 *
	 * @param mmpStatusVO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "mmpStatus", method = RequestMethod.PUT)
	public DefaultWebRespVO updateMmpStatus(@RequestBody MmpStatusVO mmpStatusVO, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		MmpStatusDTO mmpStatusDTO = new MmpStatusDTO();
		BeanCopyUtils.copyProperties(mmpStatusVO, mmpStatusDTO);
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.updateMmpStatus(mmpStatusDTO, request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("移除黑名单...");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功");
	}

	/**
	 * 车辆押金预授权交易一览
	 *
	 * @param pageNum
	 *            查询页码
	 * @param pageSize
	 *            每页查询条数
	 * @param authId
	 *            证件号
	 * @return
	 */
	@RequestMapping(value = "queryDepositRecords/{authId}", method = RequestMethod.GET)
	public DefaultWebRespVO queryDepositRecords(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
			@RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
			@RequestParam(value = "isAll", defaultValue = "0") Integer isAll, @PathVariable String authId) {
		PageBeanBO<AmountChargeHistoryDTO> pageBeanBO = iPersonalDetailInfoServiceImpl.queryDepositRecords(pageNum,
				pageSize, isAll, authId);
		log.debug("车辆押金预授权交易记录一览...");
		return DefaultWebRespVO.getSuccessVO(pageBeanBO);

	}

	/**
	 * 高端车预授权转押金
	 *
	 * @param authId
	 *            证件号
	 * @param accountReasonVO
	 * @return
	 */
	@RequestMapping(value = "depositReceiveMoney/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO updateDepositReceiveMoney(@PathVariable String authId,
			@RequestBody AccountReasonVO accountReasonVO, HttpServletRequest request) {
		AccountReasonDTO accountReasonDTO = new AccountReasonDTO();
		BeanCopyUtils.copyProperties(accountReasonVO, accountReasonDTO);
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.updateDepositReceiveMoney(authId,
				accountReasonDTO, request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("高端车预授权转押金...");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "操作成功！");

	}

	/**
	 * 普通退还押金
	 *
	 * @param authId
	 * @return
	 */
	@RequestMapping(value = "returnDeposit/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO returnDeposit(@PathVariable String authId, HttpServletRequest request) {
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.returnDeposit(authId, request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("普通退还押金...");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "已退还");
	}

	/**
	 * 高端车退还押金
	 *
	 * @param authId
	 * @return
	 */
	@RequestMapping(value = "returnDepositLevel/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO returnDepositLevel(@PathVariable String authId, HttpServletRequest request) {
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.returnDepositLevel(authId, request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("高端车退还押金...");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "已退还");
	}

	/**
	 * 解冻普通预授权
	 *
	 * @param authId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "preAuthThaw/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO preAuthThaw(@PathVariable String authId, HttpServletRequest request) {
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.preAuthThaw(authId, request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("解冻普通预授权...");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "解冻成功");
	}

	/**
	 * 解冻高端预授权
	 *
	 * @param authId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "preAuthThawLevel/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO preAuthThawLevel(@PathVariable String authId, HttpServletRequest request) {
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.preAuthThawLevel(authId, request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("解冻高端预授权...");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "解冻成功");
	}

	/**
	 * 复查通过
	 *
	 * @param authId
	 *            用户id
	 * @return
	 */
	@RequestMapping(value = "updateReexamineStatus/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO updateReexamineStatus(@PathVariable String authId, HttpServletRequest request) {
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.updateReexamineStatus(authId, request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功");
	}

	/**
	 * 复查不通过
	 *
	 * @param authId
	 *            用户id
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "updateNoPassReexamineStatus/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO updateNoPassReexamineStatus(@PathVariable String authId,
			@RequestBody UpdateAuthIdItemsVO updateAuthIdItemsVO, HttpServletRequest request) throws Exception {
		UpdateAuthIdItemsDTO updateAuthIdItemsDTO = new UpdateAuthIdItemsDTO();
		BeanCopyUtils.copyProperties(updateAuthIdItemsVO, updateAuthIdItemsDTO);
		updateAuthIdItemsDTO.setAuthId(authId);
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.updateNoPassReexamineStatus(updateAuthIdItemsDTO,
				request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功");
	}

	/**
	 * 编辑身份证号
	 *
	 * @param authId
	 * @param IdCardNumber
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "updateIdCardNumber/{authId}/{IdCardNumber}", method = RequestMethod.PUT)
	public DefaultWebRespVO updateIdCardNumber(@PathVariable String authId, @PathVariable String IdCardNumber,
			HttpServletRequest request) throws Exception {
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.updateIdCardNumber(authId, IdCardNumber,
				request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功");
	}

	/**
	 * 新驾照信息审核替换驾照信息
	 *
	 * @param authId
	 *            用户id
	 * @return
	 */
	@RequestMapping(value = "synchronizeAdditionalInfo/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO synchronizeAdditionalInfo(@PathVariable String authId,
			@RequestBody UpdateAuthIdItemsVO updateAuthIdItemsVO, HttpServletRequest request) {
		UpdateAuthIdItemsDTO updateAuthIdItemsDTO = new UpdateAuthIdItemsDTO();
		BeanCopyUtils.copyProperties(updateAuthIdItemsVO, updateAuthIdItemsDTO);
		updateAuthIdItemsDTO.setAuthId(authId);
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.synchronizeAdditionalInfo(updateAuthIdItemsDTO,
				request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功");
	}

	/**
	 * 新驾照信息审核不通过
	 *
	 * @param authId
	 *            用户id
	 * @return
	 */
	@RequestMapping(value = "updateAdditionalInfoNotThroughReasons/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO updateAdditionalInfoNotThroughReasons(@PathVariable String authId,
			@RequestBody UpdateAuthIdItemsVO updateAuthIdItemsVO, HttpServletRequest request) {
		MemberReviewInput updateAuthIdItemsDTO = new MemberReviewInput();
		BeanCopyUtils.copyProperties(updateAuthIdItemsVO, updateAuthIdItemsDTO);
		updateAuthIdItemsDTO.setAuthId(authId);
		DefaultServiceRespDTO respDTO = mmpMemberReviewServiceImpl.reviewAdditionFailed(updateAuthIdItemsDTO, request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功");
	}

	/**
	 * 会员资料详情作于修改
	 *
	 * @param authId
	 * @return
	 */
	@RequestMapping(value = "getMemberShipDetailByAuthId/{authId}", method = RequestMethod.GET)
	public DefaultWebRespVO getMemberShipDetailByAuthId(@PathVariable("authId") String authId) {
		DefaultServiceRespDTO respDTO = memberProfileServiceImpl.getMemberShipBasicByAuthId(authId);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		MembershipDetailDTO membershipDetailDTO = JSON.parseObject(respDTO.getData(), MembershipDetailDTO.class);
		return DefaultWebRespVO.getSuccessVO(membershipDetailDTO);
	}

	/**
	 * 芝麻信用操作记录
	 * 
	 * @param authId
	 * @return
	 */
	@RequestMapping(value = "querySesameCreditRecord", method = RequestMethod.GET)
	public DefaultWebRespVO querySesameCreditRecord(
			@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
			@RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
			@RequestParam(value = "isAll", defaultValue = "0") Integer isAll, @RequestParam("authId") String authId) {
		PageBeanBO<SesameCreditRecordDTO> pageBeanBO = iPersonalDetailInfoServiceImpl.querySesameCreditRecord(pageNum,
				pageSize, isAll, authId);
		log.debug("查询芝麻信用操作记录");
		return DefaultWebRespVO.getSuccessVO(pageBeanBO);
	}

	//IMemAccountService iMemAccountService;
	/**
	 * 芝麻授权信用解绑
	 * @param authId
	 * @return
	 */
	@RequestMapping(value = "memberAccount/unbindZhimaAuth/{authId}")
	public DefaultWebRespVO unbindZhimaAuth(@PathVariable String authId){
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.unbindZhimaAuth(authId);
		if (respDTO.getCode() != 0){
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE,"芝麻信用解绑成功");
	}
	/**
	 * 赠送E币- 个人
	 * 
	 * @param memberShipAccountVO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "presentEAmount/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO presentEAmount(@RequestBody MemberShipAccountVO memberShipAccountVO,
			@PathVariable String authId, HttpServletRequest request) {
		MemberShipAccountDTO memberShipAccountDTO = new MemberShipAccountDTO();
		BeanCopyUtils.copyProperties(memberShipAccountVO, memberShipAccountDTO);
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.presentEamount(memberShipAccountDTO, authId,
				request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("赠送E币- 个人");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "赠送成功");
	}

	/**
	 * 扣除赠送E币-个人
	 * 
	 * @param memberShipAccountVO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "deductPresentEAmount/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO deductPresentEAmount(@RequestBody MemberShipAccountVO memberShipAccountVO,
			@PathVariable String authId, HttpServletRequest request) {
		MemberShipAccountDTO memberShipAccountDTO = new MemberShipAccountDTO();
		BeanCopyUtils.copyProperties(memberShipAccountVO, memberShipAccountDTO);
		DefaultServiceRespDTO respDTO = iPersonalDetailInfoServiceImpl.deductPresentEamount(memberShipAccountDTO,
				authId, request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("扣除赠送E币-个人");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "扣除成功");
	}

	/**
	 * 查询E币赠送记录
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param isAll
	 * @param authId
	 * @return
	 */
	@RequestMapping(value = "queryEAmountPresentInfo", method = RequestMethod.GET)
	public DefaultWebRespVO queryEAmountPresentInfo(
			@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
			@RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
			@RequestParam(value = "isAll", defaultValue = "0") Integer isAll, @RequestParam("authId") String authId) {
		PageBeanBO<EAccountRecordDTO> pageBeanBO = iPersonalDetailInfoServiceImpl.queryEamountPresentInfo(pageNum, pageSize, isAll,
				authId);
		log.debug("查询E币赠送记录");
		return DefaultWebRespVO.getSuccessVO(pageBeanBO);
	}

	/**
	 * 显示会员敏感信息
	 * 不记录查看日志
	 * @param authId
	 *            会员id
	 * @param messageType
	 *            类型 1：手机号; 2:驾照号;3：驾照图片;4：人脸图片; 5:邮寄地址 ;6：姓名；7：身份证号码
	 *			  8:驾照副页 9: 身份照片 10：手持身份证照片 11：身份证件编号 16：身份证 正面照片   17：身份证 反面照片  <br>
	 * @return
	 */
	@RequestMapping(value = "showMemberSecretInfo/{authId}/{messageType}", method = RequestMethod.GET)
	public DefaultWebRespVO showMemberSecretInfo(@PathVariable("authId") String authId,
												 @PathVariable("messageType" ) Integer messageType, HttpServletRequest request) {
		DefaultServiceRespDTO respDTO = memberProfileServiceImpl.showMemberSecretInfo(authId, messageType, false, request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(String.valueOf(respDTO.getCode()), respDTO.getMessage());
		}
		return DefaultWebRespVO.getSuccessVO(respDTO.getData());
	}

	/**
	 * 查看会员驾照照片: 驾照正页照片&副页
	 * 记录查看日志
	 * @param authId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "showMemberdrivingLicenseImgUrl/{authId}/{messageType}", method = RequestMethod.GET)
	public DefaultWebRespVO showMemberdrivingLicenseImgUrl(@PathVariable("authId") String authId,
														   @PathVariable("messageType" ) Integer messageType, HttpServletRequest request) {
		DefaultServiceRespDTO respDTO = memberProfileServiceImpl.showMemberSecretInfo(authId, messageType, true, request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(String.valueOf(respDTO.getCode()), respDTO.getMessage());
		}
		return DefaultWebRespVO.getSuccessVO(respDTO.getData());
	}
	
	/**
	 * 用户意愿（审核不通过）
	 * @param authId
	 * @param updateAuthIdItemsVO
	 * @param request
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "auditFailed/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO auditFailed(@PathVariable String authId,
			@RequestBody UpdateAuthIdItemsVO updateAuthIdItemsVO, HttpServletRequest request) throws Exception {

		MemberReviewInput updateAuthIdItemsDTO = new MemberReviewInput();
		BeanCopyUtils.copyProperties(updateAuthIdItemsVO, updateAuthIdItemsDTO);
		updateAuthIdItemsDTO.setAuthId(authId);
		DefaultServiceRespDTO respDTO = mmpMemberReviewServiceImpl.reviewFailedUserApply(updateAuthIdItemsDTO,
				request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功！");
	}

	/**
	 * 获取新驾照信息
	 * @param authId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "memberAddtionalInfo/{authId}", method = RequestMethod.GET)
	public DefaultWebRespVO getAddtionalInfo(@PathVariable("authId") String authId, HttpServletRequest request) {
		//校验操作权限
		DefaultServiceRespDTO respDTO = memberProfileServiceImpl.checkAuthorityByOrg(authId, request);
		if (!Contants.STATUS_OK.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		//获取会员信息
		MembershipAdditionalDetailDTO detailDTO = memberProfileServiceImpl.getMemberAdditionalInfo(authId);
		if(null != detailDTO) {
			return DefaultWebRespVO.getSuccessVO(detailDTO);
		}
		return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "新驾照信息不存在");
	}

	/**
	 * 账号解除注销冻结
	 *
	 * @param authId 用户id
	 * @return
	 * @throws Exception
	 */
	@RequestMapping(value = "accountRecover/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO accountRecover(@PathVariable String authId, @RequestBody AccountRecoverVO accountRecoverVO,
										   HttpServletRequest request) throws Exception {
		//校验操作权限
		DefaultServiceRespDTO respDTO = memberProfileServiceImpl.checkAuthorityByOrg(authId, request);
		if (!Contants.STATUS_OK.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		accountRecoverVO.setAuthId(authId);
		respDTO = memberProfileServiceImpl.accountRecover(authId, accountRecoverVO.getRemark(), request);
		if (!Contants.CODE_SUCCESS.equals(respDTO.getCode())) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "解除冻结成功");
	}

	/**
	 * 驾驶证三要素手动认证通过
	 * @param authId
	 * @return
	 */
	@RequestMapping(value = "licenseElementsAuthSuccess/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO licenseElementsAuthSuccess(@PathVariable String authId,
													   @RequestBody(required=false) OperateRemarkDTO operateRemarkDTO,
													   HttpServletRequest request){
		if(operateRemarkDTO == null || StringUtils.isBlank(operateRemarkDTO.getRemark())) {
			operateRemarkDTO = new OperateRemarkDTO();
			operateRemarkDTO.setRemark("手动三要素认证通过");
			//return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "请填写手动验证通过的原因");
		}
		operateRemarkDTO.setAuthId(authId);
		DefaultServiceRespDTO defaultServiceRespDTO = mmpMemberReviewServiceImpl.licenseElementsAuthSuccess(operateRemarkDTO, request);
		if (defaultServiceRespDTO.getCode() != 0){
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, defaultServiceRespDTO.getMessage());
		}
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "三要素手动验证通过成功");
	}

	/**
	 * 押金监管-查询用户押金账户信息
	 * @param authId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "memberDeposit/account/{authId}", method = RequestMethod.GET)
	public DefaultWebRespVO queryDepositAccount(@PathVariable("authId") String authId, HttpServletRequest request) {
		try{
			//校验操作权限
			MemberDepositAccountDto accountDto = iPersonalDetailInfoServiceImpl.queryDepositAccount(authId);
			if(accountDto != null) {
				return DefaultWebRespVO.getSuccessVO(accountDto);
			}
		}catch (Exception ex) {
			log.error("获取会员押金账户信息失败，authId=" + authId, ex);
		}
		return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "新驾照信息不存在");
	}

	/**
	 * 押金监管-退还企业监管户押金
	 * @param authId
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "memberDeposit/returnEnterpriseDeposit/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO returnEnterpriseDeposit(@PathVariable("authId") String authId, HttpServletRequest request) {
		try{
            ComModel comModel = ComUtil.getUserInfo(request);
            String operator = comModel.getCreateOperName();
            Long operatorId = comModel.getCreateOperId();
            DefaultServiceRespDTO result = iPersonalDetailInfoServiceImpl.returnEnterpriseDeposit(authId, operatorId, operator);
			if(result != null) {
				return new DefaultWebRespVO(String.valueOf(result.getCode()), result.getMessage());
			}
		}catch (Exception ex) {
			log.error("退还企业监管户押金失败，authId=" + authId, ex);
		}
		return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "退还企业监管户押金失败");
	}

    /**
     * 押金监管-个人电子户退押
     * @param authId
     * @param request
     * @return
     */
    @RequestMapping(value = "memberDeposit/returnPersonalDeposit/{authId}", method = RequestMethod.PUT)
    public DefaultWebRespVO returnPersonalDeposit(@PathVariable("authId") String authId, HttpServletRequest request) {
        try{
            ComModel comModel = ComUtil.getUserInfo(request);
            String operator = comModel.getCreateOperName();
            Long operatorId = comModel.getCreateOperId();
            DefaultServiceRespDTO result = iPersonalDetailInfoServiceImpl.returnPersonalDeposit(authId, operatorId, operator);
            if(result != null) {
                return new DefaultWebRespVO(String.valueOf(result.getCode()), result.getMessage());
            }
        }catch (Exception ex) {
            log.error("退还个人电子户押金失败，authId=" + authId, ex);
        }
        return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "退还个人电子户押金失败");
    }


	/**
	 * 押金监管-预授权转押金
	 *
	 * @param authId
	 *            证件号
	 * @param accountReasonVO
	 * @return
	 */
	@RequestMapping(value = "memberDeposit/preAuthorization/{authId}", method = RequestMethod.PUT)
	public DefaultWebRespVO preAuthorization(@PathVariable String authId,
											   @RequestBody AccountReasonVO accountReasonVO, HttpServletRequest request) {
		return updateDepositReceiveMoney(authId, accountReasonVO, request);
	}


	/**
	 * 押金监管-预授权交易记录
	 *
	 * @param pageNum
	 *            查询页码
	 * @param pageSize
	 *            每页查询条数
	 * @param authId
	 *            证件号
	 * @return
	 */
	@RequestMapping(value = "memberDeposit/preAuthorizationRecord/{authId}", method = RequestMethod.GET)
	public DefaultWebRespVO queryPreAuthorizationRecords(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
										 @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
										 @RequestParam(value = "isAll", defaultValue = "0") Integer isAll, @PathVariable String authId) {
		return queryRecords(pageNum, pageSize, isAll, authId);
	}
}
