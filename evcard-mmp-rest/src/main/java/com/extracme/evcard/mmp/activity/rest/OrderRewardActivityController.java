package com.extracme.evcard.mmp.activity.rest;

import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.bo.ThirdCouponModelParamsBO;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.activity.ActivityCouponModelPageDTO;
import com.extracme.evcard.mmp.dto.activity.ActivitySmsLogDto;
import com.extracme.evcard.mmp.dto.activity.OrderRewardActivityDetailDTO;
import com.extracme.evcard.mmp.dto.activity.OrderRewardActivityFullDTO;
import com.extracme.evcard.mmp.service.IOrderRewardActivityService;
import com.extracme.evcard.mmp.vo.BaseResultVo;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 口令红包活动相关配置接口
 */
@RestController
@RequestMapping("api/orderRewardActivity/")
@Api(value="orderRewardActivity", tags = "订单完成奖励活动[17]")
public class OrderRewardActivityController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    IOrderRewardActivityService orderRewardActivityService;

    /**
     * 活动新增
     * @param activityFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="活动创建", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "add" ,method = RequestMethod.POST)
    public BaseResultVo add(@RequestBody OrderRewardActivityFullDTO activityFullDTO,
                            HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = orderRewardActivityService.add(activityFullDTO, request);
            if (respDTO.getCode() != 0) {
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("新增活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "新增失败");
        }
        vo.setMessage("新增活动成功");
        return vo;
    }

    /**
     * 活动更新
     * @param activityFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="活动修改", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "update" ,method = RequestMethod.POST)
    public BaseResultVo update(@RequestBody OrderRewardActivityFullDTO activityFullDTO,
                            HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = orderRewardActivityService.update(activityFullDTO, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("更新活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "修改失败");
        }
        vo.setMessage("修改活动成功");
        return vo;
    }

    /**
     * 活动删除
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动删除", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @RequestMapping(value = "delete/{id}", method = RequestMethod.PUT)
    public BaseResultVo delete(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = orderRewardActivityService.delete(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("删除活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "删除失败");
        }
        vo.setMessage("删除活动成功");
        return vo;
    }

    /**
     * 活动暂停
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动暂停", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @RequestMapping(value = "pause/{id}", method = RequestMethod.PUT)
    public BaseResultVo suspend(@PathVariable("id") Long id,
                             HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = orderRewardActivityService.suspend(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("暂停活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "暂停失败");
        }
        vo.setMessage("暂停活动成功");
        return vo;
    }

    /**
     * 活动添加
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动停止", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @RequestMapping(value = "stop/{id}", method = RequestMethod.PUT)
    public BaseResultVo stop(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = orderRewardActivityService.stop(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("停止活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "停止失败");
        }
        vo.setMessage("停止活动成功");
        return vo;
    }

    /**
     * 活动启动
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动启动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @RequestMapping(value = "start/{id}", method = RequestMethod.PUT)
    public BaseResultVo start(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = orderRewardActivityService.start(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("启动活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "启动活动失败");
        }
        vo.setMessage("启动活动成功");
        return vo;
    }

    /**
     * 活动恢复
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动恢复", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @RequestMapping(value = "resume/{id}", method = RequestMethod.PUT)
    public BaseResultVo resume(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = orderRewardActivityService.resume(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("恢复活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "恢复失败");
        }
        vo.setMessage("恢复活动成功");
        return vo;
    }


    /**
     * 活动发布
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动发布", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @RequestMapping(value = "publish/{id}", method = RequestMethod.PUT)
    public BaseResultVo publish(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = orderRewardActivityService.publish(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("发布活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "发布失败");
        }
        vo.setMessage("发布活动成功");
        return vo;
    }

    /**
     * 活动详情获取
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动详情", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @RequestMapping(value = "query/{id}", method = RequestMethod.GET)
    public BaseResultVo<OrderRewardActivityDetailDTO> queryDetails(@PathVariable("id") Long id, HttpServletRequest request){
        OrderRewardActivityDetailDTO activityDetailDTO = orderRewardActivityService.queryDetails(id);
        if (null == activityDetailDTO) {
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "活动信息不存在");
        }
        return BaseResultVo.getSuccessVO(activityDetailDTO);
    }

    /**
     * 券模板列表
     *
     * @param paramsBO
     * @param request
     * @return
     */
    @ApiOperation(value="券模板列表", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "getCouponModelPage", method = RequestMethod.POST)
    public BaseResultVo<ActivityCouponModelPageDTO> getCouponModelPage(@RequestBody ThirdCouponModelParamsBO paramsBO,
                                               HttpServletRequest request) {
        ActivityCouponModelPageDTO couponModelPage = orderRewardActivityService.getCouponModelPage(paramsBO);
        if (null == couponModelPage) {
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "活动券模板信息不存在");
        }
        logger.debug("获取活动发券信息...");
        return BaseResultVo.getSuccessVO(couponModelPage);
    }

    @ApiOperation(value="活动短信发送日志", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @RequestMapping(value = "getActivitySmsLog/{activityId}", method = RequestMethod.GET)
    public BaseResultVo<ActivitySmsLogDto> getActivitySmsLog(@PathVariable("activityId") Long activityId){
        logger.info("导出订单完成奖励活动，活动编号activityId:{}", activityId);
        try {
            ActivitySmsLogDto activitySmsLog = orderRewardActivityService.getActivitySmsLog(activityId);
            return BaseResultVo.getSuccessVO(activitySmsLog);
        } catch (Exception e) {
            logger.error("导出订单完成奖励活动短信发送日志失败，activityId:{}", activityId, e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "导出失败");
        }
    }
}
