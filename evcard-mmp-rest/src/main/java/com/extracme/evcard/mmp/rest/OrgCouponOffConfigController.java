package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.bo.OrgCouponConfigSelectBO;
import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.service.IOrgCouponOfferConfigService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RequestMapping("api")
@RestController
@ResponseBody
public class OrgCouponOffConfigController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    IOrgCouponOfferConfigService orgCouponOfferConfigService;

    /**
     * 查询子公司优惠券限制配置列表
     * @param request
     * @return
     */
    @RequestMapping(value="couponOfferConfiger/list", method={RequestMethod.GET})
    public DefaultWebRespVO getOrgCouponLimitList(@RequestParam(value = "orgId", required = true) String orgId,
                                                  @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                  @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                  @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
                                                  HttpServletRequest request){
        DefaultWebRespVO vo = null;
        //获取登录账号所属组织机构
        ComModel comModel = ComUtil.getUserInfo(request);
        String curOrgId = comModel.getOrgId();
        try {
            OrgCouponConfigSelectBO queryBo = new OrgCouponConfigSelectBO();
            queryBo.setIsAll(isAll);
            queryBo.setPageNum(pageNum);
            queryBo.setPageSize(pageSize);
            queryBo.setOrgId(orgId);
            PageBeanBO<OrgCouponLimitDTO> pageBeanBO = orgCouponOfferConfigService.getCouponConfigList(queryBo);
            vo = DefaultWebRespVO.getSuccessVO(pageBeanBO);
        }
        catch (Exception ex) {
            log.error("优惠券发放限制获取失败", ex);
            vo = new DefaultWebRespVO();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("查询失败");
        }
        return vo;
    }

    /**
     * 查询子公司优惠券限制配置详情
     * @param request
     * @return
     */
    @RequestMapping(value="couponOfferConfiger/detail", method={RequestMethod.GET})
    public DefaultWebRespVO getActivityCouponLimitDetail(@RequestParam(value = "orgId", required = true) String orgId,
                                               @RequestParam(value = "activityKey", required = false) String activityKey,
                                               HttpServletRequest request){
        DefaultWebRespVO vo = null;
        try {
            if (StringUtils.isNotBlank(activityKey)) {
                ActivityOfferConfigDTO configDTO = orgCouponOfferConfigService.getCouponConfigDetail(orgId, activityKey);
                vo = DefaultWebRespVO.getSuccessVO(configDTO);
            } else {
                OrgCouponLimitDetailDTO configDTO = orgCouponOfferConfigService.getCouponConfigDetail(orgId);
                vo = DefaultWebRespVO.getSuccessVO(configDTO);
            }
        }
        catch (Exception ex) {
                log.error("优惠券发放限制获取失败", ex);
                vo = new DefaultWebRespVO();
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("查询失败");
            }
        return vo;
    }

    /**
     * 保存子公司优惠券限制配置
     *
     * @param configDto 新增对象dto
     * @param request
     * @return
     */
    @RequestMapping(value = "couponOfferConfiger/save", method = RequestMethod.POST)
    public DefaultWebRespVO saveOrgCouponConfig(@RequestBody OrgCouponLimitDetailDTO configDto, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            String orgId = configDto.getOrgId();
            OperatorDTO operator = new OperatorDTO();
            ComModel comModel = ComUtil.getUserInfo(request);
            operator.setOperatorName(comModel.getCreateOperName());
            operator.setOperatorId(comModel.getCreateOperId());

            DefaultServiceRespDTO respDTO = orgCouponOfferConfigService.saveOrgCouponConfig(orgId, configDto, operator);
            if (respDTO.getCode() != 0) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception ex) {
            log.error("优惠券发放限制保存失败", ex);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("提交失败");
            return vo;
        }
        vo.setMessage("提交成功");
        return vo;
    }

    /**
     * 保存子公司优惠券限制配置
     *
     * @param configDto 新增对象dto
     * @param request
     * @return
     */
    @RequestMapping(value = "couponOfferConfiger/batchSave", method = RequestMethod.POST)
    public DefaultWebRespVO saveOrgCouponConfig(@RequestBody OrgCouponLimitBatchDTO configDto, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            OperatorDTO operator = new OperatorDTO();
            ComModel comModel = ComUtil.getUserInfo(request);
            operator.setOperatorName(comModel.getCreateOperName());
            operator.setOperatorId(comModel.getCreateOperId());
            if(CollectionUtils.isEmpty(configDto.getOrgIds())) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "请选择子公司");
            }
            if(configDto.getOrgIds().size() > 50) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "请选择1~50个子公司");
            }
            DefaultServiceRespDTO respDTO = orgCouponOfferConfigService.saveOrgCouponConfig(configDto.getOrgIds(),
                    configDto.getActivitys(), operator);
            if (respDTO.getCode() != 0) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception ex) {
            log.error("优惠券发放限制保存失败", ex);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("提交失败");
            return vo;
        }
        vo.setMessage("提交成功");
        return vo;
    }

    /**
     * 日志一览
     *
     * @param pageNum  页码
     * @param pageSize 条数
     * @param isAll    是否统计
     * @param orgId
     * @return vo 返回值
     */
    @RequestMapping(value = "couponOfferConfiger/logs/{orgId}", method = RequestMethod.GET)
    public DefaultWebRespVO queryChannelOperatorLogList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                        @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                        @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
                                                        @PathVariable(value = "orgId") String orgId) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            PageBeanBO<UserOperatorLogDTO> pageBeanBO =
                    orgCouponOfferConfigService.queryConfigOperateLog(pageNum, pageSize, isAll, orgId);
            if (CollectionUtils.isEmpty(pageBeanBO.getList())) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("日志记录不存在");
                return vo;
            }
            return DefaultWebRespVO.getSuccessVO(pageBeanBO);
        } catch (Exception e) {
            log.error(ComUtil.getExceptionMsg(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
        }
        log.debug("日志操作情况一览...");
        return vo;
    }
}
