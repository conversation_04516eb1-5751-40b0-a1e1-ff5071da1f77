package com.extracme.evcard.mmp.vo;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import lombok.Data;
@Data
public class AnnouncementVO implements Serializable{

	private static final long serialVersionUID = 2533414704921443518L;
	/**
	 * 公告ID
	 */
	private Long id;
	/**
	 * 公告名称
	 */
	private String name;
	/**
	 * 公告状态
	 */
	private Integer announcementStatus;
	/**
	 * 展示频率 0 仅公告一次 1 每天一次 2 永久展示
	 */
	private Integer showFrequency;
	/**
	 * 关闭方式 0 手动关闭  1 自动关闭
	 */
	private Integer closeWay;
	/**
	 * 自动关闭时长
	 */
	private Integer autoCloseDuration;
	/**
	 * 公告内容
	 */
	private String announcementContent;
	/**
	 * 运营公司
	 */
	private String orgId;
	/**
	 * 运营城市
	 */
	private String city;
	/**
	 * 运营城市编号
	 */
	private String cityId;
	/**
	 * 开始时间
	 */
	private String startTime;
	/**
	 * 结束时间
	 */
	private String endTime;
	/**
     * 城市ID列表
     */
    private List<Long> cityList;
    /**
     * 公告链接
     */
    private String announcementUrl;
	/**
	 * 展示的平台 0：全部 1：app 2：h5 3：微信小程序 4：支付宝小程序
	 */
	private List<Integer> platformList;
}
