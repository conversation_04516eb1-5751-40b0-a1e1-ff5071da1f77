package com.extracme.evcard.mmp;

import com.github.xiaoymin.knife4j.spring.annotations.EnableKnife4j;
import io.swagger.annotations.ApiOperation;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import springfox.bean.validators.configuration.BeanValidatorPluginsConfiguration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.ParameterBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Parameter;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.Arrays;
import java.util.List;

@Configuration
@EnableWebMvc
@EnableSwagger2
@EnableKnife4j
@Import(BeanValidatorPluginsConfiguration.class)
//指定缺省值
@ConditionalOnProperty(value = {"knife4j.enable"}, matchIfMissing = true)
public class SwaggerConfiguration {

	private List<Parameter> parameterBuilder() {
		return Arrays.asList(new ParameterBuilder().parameterType("header")
				.name("token")
				.description("token")
				.modelRef(new ModelRef("string"))
				.required(false).build());
	};

	@Bean
	public Docket createRestApi() {
		return new Docket(DocumentationType.SWAGGER_2)
				//.enable(enable)
				.apiInfo(meta())
				.select()
				//显示此包下全部接口
				.apis(RequestHandlerSelectors.basePackage("com.extracme.evcard.mmp.rest"))//扫描的包路径
				//.apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
				.paths(PathSelectors.any())
				.build()
				.globalOperationParameters(parameterBuilder())
				.groupName("0.mmp");
	}
	
	private ApiInfo meta() {
		return new ApiInfoBuilder()
				.title("会员系统")
				.description("mmp-apidoc")
				.version("1.0.0")
				.build();
	}


    @Bean
    public Docket createActivityRestApi() {
        return new Docket(DocumentationType.SWAGGER_2)
				//.enable(enable)
                .apiInfo(actMeta())
                .select()
				//仅显示此包下带了api注解的接口
                .apis(RequestHandlerSelectors.basePackage("com.extracme.evcard.mmp.activity.rest"))//扫描的包路径
                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .paths(PathSelectors.any())
                .build()
				.globalOperationParameters(parameterBuilder())
                .groupName("1.activity");
    }

    private ApiInfo actMeta() {
        return new ApiInfoBuilder()
                .title("市场活动")
                .description("activity-apidoc")
                .version("1.0.0")
                .build();
    }

	/**
	 * 会员任务中心单独分组
	 * ps. groupName若为中文需要处理
	 * @return
	 */
	@Bean(name="tcsRest")
	public Docket createTcsRestApi() {
		return new Docket(DocumentationType.SWAGGER_2)
				//.enable(enable)
				.apiInfo(metaTcs())
				.select()
				.apis(RequestHandlerSelectors.basePackage("com.extracme.evcard.mmp.tcs.rest"))//扫描的包路径
				//.apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
				.paths(PathSelectors.any())
				.build()
				.globalOperationParameters(parameterBuilder())
				.groupName("2.tcs");
	}

	private ApiInfo metaTcs() {
		return new ApiInfoBuilder()
				.title("会员任务中心")
				.description("tcs-apidoc")
				.version("1.0.0")
				.build();
	}

	@Bean
	public Docket createCommonRestApi() {
		return new Docket(DocumentationType.SWAGGER_2)
				//.enable(enable)
				.apiInfo(commonMeta())
				.select()
				//仅显示此包下全部接口
				.apis(RequestHandlerSelectors.basePackage("com.extracme.evcard.mmp.common.rest"))//扫描的包路径
				//.apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
				.paths(PathSelectors.any())
				.build()
				.globalOperationParameters(parameterBuilder())
				.groupName("99.common");
	}

	private ApiInfo commonMeta() {
		return new ApiInfoBuilder()
				.title("通用工具")
				.description("common-apidoc")
				.version("1.0.0")
				.build();
	}
}