package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.OperatorDTO;
import com.extracme.evcard.mmp.dto.QuickLinksConfigDTO;
import com.extracme.evcard.mmp.dto.QuickLinksConfigOperateLogDTO;
import com.extracme.evcard.mmp.service.IQuickLinksConfigService;
import com.extracme.evcard.mmp.vo.QuickLinksConfigVO;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.rpc.exception.BusinessRuntimeException;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("api/quickLinksConfig")
public class QuickLinksConfigController {

    @Autowired
    private IQuickLinksConfigService quickLinksConfigService;

    @ApiOperation(value="查询金刚区配置", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @RequestMapping(value = "query", method = RequestMethod.GET)
    public DefaultWebRespVO queryQuickLinksConfig() {
        List<QuickLinksConfigDTO> quickLinksConfig = quickLinksConfigService.getQuickLinksConfig();
        return DefaultWebRespVO.getSuccessVO(quickLinksConfig);
    }

    @ApiOperation(value="修改金刚区配置", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "update", method = RequestMethod.POST)
    public DefaultWebRespVO updateQuickLinksConfig(@RequestBody QuickLinksConfigVO quickLinksConfigVO, HttpServletRequest request) throws BusinessException {
        if (quickLinksConfigVO == null) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "参数错误");
        }
        ComModel comModel = ComUtil.getUserInfo(request);
        if (comModel == null) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "用户未登录");
        }
        OperatorDTO operatorDTO = new OperatorDTO();
        operatorDTO.setOperatorId(comModel.getCreateOperId());
        operatorDTO.setOperatorName(comModel.getCreateOperName());
        try {
            quickLinksConfigService.updateQuickLinksConfig(quickLinksConfigVO.getData(), operatorDTO);
            return DefaultWebRespVO.SUCCESS;
        } catch (BusinessRuntimeException e) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, e.getMessage());
        } catch (Exception e) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "系统繁忙，请稍后再试");
        }

    }

    @ApiOperation(value="查询金刚区配置日志", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @RequestMapping(value = "logs", method = RequestMethod.GET)
    public DefaultWebRespVO queryQuickLinksConfigLogs(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                      @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        PageBeanBO<QuickLinksConfigOperateLogDTO> quickLinksConfigLogs = quickLinksConfigService.getQuickLinksConfigLogs(pageNum, pageSize);
        return DefaultWebRespVO.getSuccessVO(quickLinksConfigLogs);
    }
}
