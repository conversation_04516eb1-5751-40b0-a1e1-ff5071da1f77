package com.extracme.evcard.mmp.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020/2/19 15:04
 */
@Data
public class SaveDailyRentOilPriceConfigVo {

    /**
     * 机构id
     */
    private List<String> orgIdList;

    /**
     * 车型编号
     */
    private Long vehicleModelSeq;

    /**
     * 修改时 主键id
     */
    private Long id;

    /**
     * 油价
     */
    @NotNull(message = "请输入油价")
    //@Range(min = 0, max = 999999, message = "油价值必须在0.01~999999之间")
    private BigDecimal oilPrice;

    /**
     * 燃油标号
     */
    @NotNull(message = "请输入燃油标号")
    //@Range(min = 1, max = 999999, message = "燃油标号值必须在1~999999之间")
    private Integer oilLabel;

    /**
     * 生效时间
     */
    @NotNull(message = "请选择生效时间")
    private Date startTime;
}
