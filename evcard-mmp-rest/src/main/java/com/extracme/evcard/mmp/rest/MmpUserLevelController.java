package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.MmpUserLevelRequestDTO;
import com.extracme.evcard.mmp.service.IMmpUserLevelService;
import com.extracme.evcard.mmp.vo.MmpUserLevelRequestVO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 项目名称：evcard-mmp-rest
 * 类名称：MmpUserLevelController
 * 类描述：会员等级信息控制层
 * 创建人：sunb-孙彬
 * 创建时间：2017年11月6日上午10:24:43
 * 修改备注
 *
 * @version2.0
 */
@RestController
@RequestMapping("api")
public class MmpUserLevelController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IMmpUserLevelService mmpUserLevelServiceImpl;

    /**
     * 新增会员等级信息
     *
     * @param mmpUserLevelRequestVO
     * @param request
     * @return
     */
    @RequestMapping(value = "mmpUserLevelInfo", method = RequestMethod.POST)
    public DefaultWebRespVO addMmpUserLevelAnalysisInfo(@RequestBody MmpUserLevelRequestVO mmpUserLevelRequestVO, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            MmpUserLevelRequestDTO mmpUserLevelRequestDTO = new MmpUserLevelRequestDTO();
            BeanCopyUtils.copyProperties(mmpUserLevelRequestVO, mmpUserLevelRequestDTO);
            DefaultServiceRespDTO respDTO = mmpUserLevelServiceImpl.addMmpUserLevelInfo(mmpUserLevelRequestDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            vo.setMessage("添加失败");
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("新增会员等级信息...");
        return vo;
    }
}
