package com.extracme.evcard.mmp.vo;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * Created by Elin on 2017/11/22.
 * 会员事件记录详情
 */
public class CreditEventRecordDetailVO {

    /**
     * 事件编号
     */
    private Long eventId;

    /**
     * 会员id
     */
    private String authId;

    /**
     * 会员姓名
     */
    private String authName;

    /**
     * 手机号
     */
    private String mobilePhone;

    /**
     * 会员所属id
     */
    private String orgId;

    /**
     * 会员所属
     */
    private String orgName;

    /**
     * 事件类型id
     */
    private Long eventTypeId;

    /**
     * 事件记录详情
     */
    private String eventDesc;

    /**
     * 操作来源
     */
    private String eventSource;

    /**
     * 事件类型名称
     */
    private String eventName;

    /**
     * 事件凭证图片地址 多张逗号分隔
     */
    private String eventImagePath;

    /**
     * 事件凭证 文件路径
     */
    private String eventFilePath;

    /**
     * 事件类型性质 1-正面 0-负面
     */
    private String eventNature;

    /**
     * 分值
     */
    private Integer amount;

    /**
     * 是否直接加入黑名单 false-否 true-是
     */
    private Boolean blackList;

    /**
     * 事件状态（0-撤销 1-正常）
     */
    private Integer eventStatus;

    /**
     * 创建时间
     */
    private Date eventCreateTime;

    /**
     * 创建人id
     */
    private Long eventCreateOperId;

    /**
     * 创建人姓名
     */
    private String eventCreateOperName;

    /**
     * 申诉编号
     */
    private Long appealId;

    /**
     * 申诉时间
     */
    private Date appealTime;

    /**
     * 申诉状态（0-未申诉 1 已申诉）
     */
    private Integer appealStatus;

    /**
     * 申诉描述
     */
    private String appealDesc;

    /**
     * 申诉凭证上传图片路径，多张以逗号分隔
     */
    private String appealImagePath;

    /**
     * 申诉凭证 文件路径
     */
    private String appealFilePath;

    /**
     * 处理人id
     */
    private Long handleUserId;
    /**
     * 处理人姓名
     */
    private String handleUser;
    /**
     * 处理时间
     */
    private Date handleTime;

    /**
     * 通知话术
     */
    private String handleRemark;

    /**
     * 处理结果0-未处理 1-同意 2-驳回
     */
    private Integer handleResult;

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Long getEventTypeId() {
        return eventTypeId;
    }

    public void setEventTypeId(Long eventTypeId) {
        this.eventTypeId = eventTypeId;
    }

    public String getEventDesc() {
        return eventDesc;
    }

    public void setEventDesc(String eventDesc) {
        this.eventDesc = eventDesc;
    }

    public String getEventSource() {
        return eventSource;
    }

    public void setEventSource(String eventSource) {
        this.eventSource = eventSource;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public String getEventImagePath() {
        return eventImagePath;
    }

    public void setEventImagePath(String eventImagePath) {
        this.eventImagePath = eventImagePath;
    }

    public String getEventFilePath() {
        return eventFilePath;
    }

    public void setEventFilePath(String eventFilePath) {
        this.eventFilePath = eventFilePath;
    }

    public String getEventNature() {
        return eventNature;
    }

    public void setEventNature(String eventNature) {
        this.eventNature = eventNature;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public Boolean getBlackList() {
        return blackList;
    }

    public void setBlackList(Boolean blackList) {
        this.blackList = blackList;
    }

    public Integer getEventStatus() {
        return eventStatus;
    }

    public void setEventStatus(Integer eventStatus) {
        this.eventStatus = eventStatus;
    }

    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    public Date getEventCreateTime() {
        return eventCreateTime;
    }

    public void setEventCreateTime(Date eventCreateTime) {
        this.eventCreateTime = eventCreateTime;
    }

    public Long getEventCreateOperId() {
        return eventCreateOperId;
    }

    public void setEventCreateOperId(Long eventCreateOperId) {
        this.eventCreateOperId = eventCreateOperId;
    }

    public String getEventCreateOperName() {
        return eventCreateOperName;
    }

    public void setEventCreateOperName(String eventCreateOperName) {
        this.eventCreateOperName = eventCreateOperName;
    }

    public Long getAppealId() {
        return appealId;
    }

    public void setAppealId(Long appealId) {
        this.appealId = appealId;
    }

    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    public Date getAppealTime() {
        return appealTime;
    }

    public void setAppealTime(Date appealTime) {
        this.appealTime = appealTime;
    }

    public Integer getAppealStatus() {
        return appealStatus;
    }

    public void setAppealStatus(Integer appealStatus) {
        this.appealStatus = appealStatus;
    }

    public String getAppealDesc() {
        return appealDesc;
    }

    public void setAppealDesc(String appealDesc) {
        this.appealDesc = appealDesc;
    }

    public String getAppealImagePath() {
        return appealImagePath;
    }

    public void setAppealImagePath(String appealImagePath) {
        this.appealImagePath = appealImagePath;
    }

    public String getAppealFilePath() {
        return appealFilePath;
    }

    public void setAppealFilePath(String appealFilePath) {
        this.appealFilePath = appealFilePath;
    }

    public Long getHandleUserId() {
        return handleUserId;
    }

    public void setHandleUserId(Long handleUserId) {
        this.handleUserId = handleUserId;
    }

    public String getHandleUser() {
        return handleUser;
    }

    public void setHandleUser(String handleUser) {
        this.handleUser = handleUser;
    }

    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    public Date getHandleTime() {
        return handleTime;
    }

    public void setHandleTime(Date handleTime) {
        this.handleTime = handleTime;
    }

    public String getHandleRemark() {
        return handleRemark;
    }

    public void setHandleRemark(String handleRemark) {
        this.handleRemark = handleRemark;
    }

    public Integer getHandleResult() {
        return handleResult;
    }

    public void setHandleResult(Integer handleResult) {
        this.handleResult = handleResult;
    }

    public String getAuthName() {
        return authName;
    }

    public void setAuthName(String authName) {
        this.authName = authName;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }
}
