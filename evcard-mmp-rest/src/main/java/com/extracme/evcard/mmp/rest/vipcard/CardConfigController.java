package com.extracme.evcard.mmp.rest.vipcard;

import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.CardStyleDto;
import com.extracme.evcard.mmp.service.vipcard.ICardConfigService;
import com.extracme.evcard.rpc.vipcard.dto.CardConfigDto;
import com.extracme.evcard.rpc.vipcard.dto.CardModelListViewDto;
import com.extracme.evcard.rpc.vipcard.dto.CardModelQueryDto;
import com.extracme.evcard.tcs.provider.api.dto.FileItemDTO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/12/28
 */
@Slf4j
@RestController
@Api(value="vipcard", tags = "用户权益-卡片管理")
@RequestMapping("api/vipcard/card")
public class CardConfigController {
    @Resource
    ICardConfigService cardConfigService;

    /**
     * 活动新增
     * @param configDTO
     * @param request
     * @return
     */
    @RequestMapping(value = "add" ,method = RequestMethod.POST)
    public DefaultWebRespVO add(@RequestParam(value = "backFile", required = false) CommonsMultipartFile imageFile,
                                CardConfigDto configDTO, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            if(configDTO == null) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "参数不完整");
            }
            FileItemDTO fileDto = null;
            if(NumberUtils.INTEGER_ZERO.equals(configDTO.getStyleType())) {
                fileDto = ComUtil.fromMultipartFile(imageFile, "卡面样式背景图");
                if(fileDto == null) {
                    return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "请上传卡面图片");
                }
            }
            DefaultServiceRespDTO respDTO = cardConfigService.add(configDTO, fileDto, request);
            if (respDTO.getCode() != 0) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            log.error("新增卡片异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "新增失败");
        }
        vo.setMessage("新增卡片成功");
        return vo;
    }

    /**
     * 卡片更新
     * @param configDTO
     * @param request
     * @return
     */
    @RequestMapping(value = "update" ,method = RequestMethod.POST)
    DefaultWebRespVO update(@RequestParam(value = "backFile", required = false) CommonsMultipartFile imageFile,
                            CardConfigDto configDTO, HttpServletRequest request){
        /**
         * TODO 处理图片-》url
         */
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            FileItemDTO fileDto = null;
            if(NumberUtils.INTEGER_ZERO.equals(configDTO.getStyleType())) {
                fileDto = ComUtil.fromMultipartFile(imageFile, "卡面样式背景图");
            }
            DefaultServiceRespDTO respDTO = cardConfigService.update(configDTO, fileDto, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            log.error("更新卡片异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "修改失败");
        }
        vo.setMessage("修改卡片成功");
        return vo;
    }

    /**
     * 卡片禁用
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "disable/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO disable(@PathVariable("id") Long id,
                             HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = cardConfigService.disable(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            log.error("禁用卡片异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "禁用失败");
        }
        vo.setMessage("禁用卡片成功");
        return vo;
    }

    /**
     * 卡片启用
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "enable/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO enable(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = cardConfigService.enable(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            log.error("启用卡片异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "启用失败");
        }
        vo.setMessage("启用卡片成功");
        return vo;
    }

    /**
     * 卡片详情获取
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "info/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO queryDetails(@PathVariable("id") Long id, HttpServletRequest request){
        CardModelListViewDto result = cardConfigService.queryDetail(id);
        if (null == result) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "卡片信息不存在");
        }
        return DefaultWebRespVO.getSuccessVO(result);
    }

    /**
     * 查询卡片配置列表
     * @param queryDto
     * @param request
     * @return
     */
    @RequestMapping(value = "list", method = RequestMethod.POST)
    public DefaultWebRespVO queryList(@RequestBody CardModelQueryDto queryDto, HttpServletRequest request) {
        PageBeanBO<CardModelListViewDto> pageBean = cardConfigService.queryPage(queryDto);
        return DefaultWebRespVO.getSuccessVO(pageBean);
    }

    @RequestMapping(value = "styleList", method = RequestMethod.GET)
    public DefaultWebRespVO queryList(HttpServletRequest request) {
        List<CardStyleDto> list = cardConfigService.getStyleList();
        return DefaultWebRespVO.getSuccessVO(list);
    }
}
