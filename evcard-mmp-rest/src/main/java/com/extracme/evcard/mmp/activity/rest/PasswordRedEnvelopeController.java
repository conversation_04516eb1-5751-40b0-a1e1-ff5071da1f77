package com.extracme.evcard.mmp.activity.rest;

import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.bo.ThirdCouponModelParamsBO;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.activity.ActivityCouponModelPageDTO;
import com.extracme.evcard.mmp.dto.activity.PasswordRedEnvelopeDetailDTO;
import com.extracme.evcard.mmp.dto.activity.PasswordRedEnvelopeFullDTO;
import com.extracme.evcard.mmp.service.IPasswordRedEnvelopeService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 口令红包活动相关配置接口
 */
@Api(value="PasswordRedEnvelope", tags = "口令红包活动[16]")
@RestController
@RequestMapping("api/PasswordRedEnvelope/")
public class PasswordRedEnvelopeController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    IPasswordRedEnvelopeService passwordRedEnvelopeServiceImpl;

    /**
     * 活动新增
     * @param activityFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="活动创建", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "add" ,method = RequestMethod.POST)
    public DefaultWebRespVO add(@RequestBody PasswordRedEnvelopeFullDTO activityFullDTO,
                                HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = passwordRedEnvelopeServiceImpl.add(activityFullDTO, request);
            if (respDTO.getCode() != 0) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("新增活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "新增失败");
        }
        vo.setMessage("新增活动成功");
        return vo;
    }

    /**
     * 活动更新
     * @param activityFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="活动修改", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "update" ,method = RequestMethod.POST)
    DefaultWebRespVO update(@RequestBody PasswordRedEnvelopeFullDTO activityFullDTO,
                            HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = passwordRedEnvelopeServiceImpl.update(activityFullDTO, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("更新活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "修改失败");
        }
        vo.setMessage("修改活动成功");
        return vo;
    }

    /**
     * 活动删除
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动删除", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "delete/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO delete(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = passwordRedEnvelopeServiceImpl.delete(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("删除活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "删除失败");
        }
        vo.setMessage("删除活动成功");
        return vo;
    }

    /**
     * 活动暂停
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动暂停", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "pause/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO suspend(@PathVariable("id") Long id,
                             HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = passwordRedEnvelopeServiceImpl.suspend(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("暂停活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "暂停失败");
        }
        vo.setMessage("暂停活动成功");
        return vo;
    }

    /**
     * 活动添加
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动停止", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "stop/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO stop(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = passwordRedEnvelopeServiceImpl.stop(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("停止活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "停止失败");
        }
        vo.setMessage("停止活动成功");
        return vo;
    }

    /**
     * 活动启动
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动启动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "start/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO start(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = passwordRedEnvelopeServiceImpl.start(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("启动活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "启动活动失败");
        }
        vo.setMessage("启动活动成功");
        return vo;
    }

    /**
     * 活动恢复
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动恢复", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "resume/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO resume(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = passwordRedEnvelopeServiceImpl.resume(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("恢复活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "恢复失败");
        }
        vo.setMessage("恢复活动成功");
        return vo;
    }


    /**
     * 活动发布
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动发布", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "publish/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO publish(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = passwordRedEnvelopeServiceImpl.publish(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("发布活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "发布失败");
        }
        vo.setMessage("发布活动成功");
        return vo;
    }

    /**
     * 活动详情获取
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动详情", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "query/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO queryDetails(@PathVariable("id") Long id, HttpServletRequest request){
        PasswordRedEnvelopeDetailDTO activityDetailDTO = passwordRedEnvelopeServiceImpl.queryDetails(id);
        if (null == activityDetailDTO) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动信息不存在");
        }
        return DefaultWebRespVO.getSuccessVO(activityDetailDTO);
    }

    /**
     * 配置情况查询
     *
     * @param password             口令
     * @param startDate           日期： yyyy-MM-dd
     * @param endDate             日期： yyyy-MM-dd
     * @return vo
     * @remark 不包含已停止和已删除的活动
     */
    @ApiOperation(value="配置情况查询", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "pageNum",value = "起始页",required = true),
            @ApiImplicitParam(name = "pageSize",value = "每页展示的行数",required = true),
            @ApiImplicitParam(name = "isAll",value = "合理化，是否显示总页数",required = true),
            @ApiImplicitParam(name = "id",value = "活动id",required = true),
            @ApiImplicitParam(name = "password",value = "口令",required = true),
            @ApiImplicitParam(name = "startDate",value = "开始时间",required = true),
            @ApiImplicitParam(name = "endDate",value = "结束时间",required = true)
    })
    @RequestMapping(value = "keyList", method = RequestMethod.GET)
    public DefaultWebRespVO keyList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                    @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                    @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
                                    @RequestParam(value = "id", required = false) Long id,
                                    @RequestParam(value = "password", required = false) String password,
                                    @RequestParam(value = "startDate") String startDate,
                                    @RequestParam(value = "endDate") String endDate) {
        //TODO 参数检查
//        if(StringUtils.isBlank(date) || (date.length() != 8 && date.length() != 10)) {
//            return new DefaultWebRespVO("-1", "参数不合法");
//        }
        PageBeanBO<PasswordRedEnvelopeDetailDTO> pageBeanBO = passwordRedEnvelopeServiceImpl.queryByPassword(
                pageNum, pageSize, isAll,
                id, password, startDate, endDate);
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    /**
     * 券模板列表
     *
     * @param paramsBO
     * @param request
     * @return
     */
    @ApiOperation(value="券模板列表", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "getCouponModelPage", method = RequestMethod.POST)
    public DefaultWebRespVO getCouponModelPage(@RequestBody ThirdCouponModelParamsBO paramsBO,
                                               HttpServletRequest request) {
        ActivityCouponModelPageDTO couponModelPage = passwordRedEnvelopeServiceImpl.getCouponModelPage(paramsBO);
        if (null == couponModelPage) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动券模板信息不存在");
        }
        logger.debug("获取活动发券信息...");
        return DefaultWebRespVO.getSuccessVO(couponModelPage);
    }
}
