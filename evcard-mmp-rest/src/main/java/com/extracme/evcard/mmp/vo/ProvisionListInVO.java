package com.extracme.evcard.mmp.vo;

/**
 * 项目名称：evcard-mmp-rest
 * 类名称：QueryUserInfoListVO
 * 类描述：条款List查询条件实体
 * 创建人：chenchun-陈春
 * 创建时间：2017年8月22日 下午13:03:08
 * 修改备注：
 * @version1.0
 */
public class ProvisionListInVO {

    /**
     * 条款状态（1：待发布 2：使用中 3：已停止）
     */
    private Integer provisionStatus;

    /**
     * 类型（1：会员守则 2：隐私政策 3：Q&A）
     */
    private Integer provisionType;

    /**
     * 0 法大大 1e签宝 空
     */
    private String supplier;

    /**
     * 版本号
     */
    private String version;

    /**
     * 开始时间 格式：yyyy-MM-dd hh:mm:ss
     */
    private String startTime;

    /**
     * 结束时间 格式：yyyy-MM-dd hh:mm:ss
     */
    private String endTime;

    /** 页数 **/
    private int pageNum = 1;

    /** 每页的记录数 **/
    private int pageSize = 10;

    /** 是否显示记录总条数（0：否；1是） **/
    private int isAll = 0;

    public Integer getProvisionStatus() {
        return provisionStatus;
    }

    public void setProvisionStatus(Integer provisionStatus) {
        this.provisionStatus = provisionStatus;
    }

    public Integer getProvisionType() {
        return provisionType;
    }

    public void setProvisionType(Integer provisionType) {
        this.provisionType = provisionType;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getIsAll() {
        return isAll;
    }

    public void setIsAll(int isAll) {
        this.isAll = isAll;
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }
}
