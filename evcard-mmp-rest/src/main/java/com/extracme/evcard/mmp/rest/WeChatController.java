package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.MmpShortLinkLogDTO;
import com.extracme.evcard.mmp.dto.MmpShortLinkManagementDTO;
import com.extracme.evcard.mmp.dto.MmpWeChatRequestDto;
import com.extracme.evcard.mmp.dto.MmpWeChatResponse;
import com.extracme.evcard.mmp.service.MmpShortlinkManagementService;
import com.extracme.evcard.mmp.service.wechat.WeChatService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR> 微信小程序接口调用
 */
@RestController
@RequestMapping("api/wechat")
@Api(tags = "微信小程序接口文档")
public class WeChatController {
	@Autowired
	private WeChatService weChatService;


	private final Logger log = LoggerFactory.getLogger(this.getClass());

	/**
	 * 获取token
	 * @return
	 */
	@GetMapping("/getToken")
	public DefaultWebRespVO  getToken(Boolean refresh){
		DefaultWebRespVO vo = null;
		try {
			String token = weChatService.getToken(refresh);
			vo = DefaultWebRespVO.getSuccessVO(token);
		}catch (Exception ex) {
			log.error("获取微信api的Token异常", ex);
			vo = new DefaultWebRespVO();
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("获取微信api的Token失败");
		}
		return vo;
	}
	/**
	 * 获取不限制的小程序码
	 * @return
	 */
	@PostMapping("/getwxacodeunlimit")
	public DefaultWebRespVO  getwxacodeunlimit(String page, String scene, Integer tryNum){

		DefaultWebRespVO vo = null;
		try {
			MmpWeChatResponse mmpWeChatResponse = weChatService.getwxacodeunlimit(page, scene, tryNum);
			vo = DefaultWebRespVO.getSuccessVO(mmpWeChatResponse);
		}catch (Exception ex) {
			log.error("获取不限制的小程序码异常", ex);
			vo = new DefaultWebRespVO();
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("获取不限制的小程序码失败");
		}
		return vo;
	}

	/**
	 * 获取小程序链接
	 * @return
	 */
	@PostMapping("/getgenerateUrllink")
	public DefaultWebRespVO  getgenerateUrllink(@RequestBody MmpWeChatRequestDto mmpWeChatRequestDto){

		DefaultWebRespVO vo = null;
		try {
			MmpWeChatResponse mmpWeChatResponse = weChatService.getgenerateUrllink(mmpWeChatRequestDto);
			vo = DefaultWebRespVO.getSuccessVO(mmpWeChatResponse);
		}catch (Exception ex) {
			log.error("获取小程序链接异常", ex);
			vo = new DefaultWebRespVO();
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("获取小程序链接失败");
		}
		return vo;
	}
}
