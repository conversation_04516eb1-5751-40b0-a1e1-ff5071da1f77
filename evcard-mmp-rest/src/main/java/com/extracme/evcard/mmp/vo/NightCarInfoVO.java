package com.extracme.evcard.mmp.vo;

import java.util.List;

import com.extracme.evcard.mmp.dto.ActivityVehicleDTO;

public class NightCarInfoVO {
    private String id;
    // 活动名称
    private String activityName;
    // 所属公司
    private String orgId;
    // 活动开始日期
    private String activityStartDate;
    // 活动结束日期
    private String activityEndDate;
    // 活动车牌
    private String activityLicensePlate;
    // 活动周
    private String activityWeeds;
    // 活动开始时间
    private String activityStartTime;
    // 活动结束时间
    private String activityEndTime;

    private List<ActivityVehicleDTO> list;

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getActivityStartDate() {
        return activityStartDate;
    }

    public void setActivityStartDate(String activityStartDate) {
        this.activityStartDate = activityStartDate;
    }

    public String getActivityEndDate() {
        return activityEndDate;
    }

    public void setActivityEndDate(String activityEndDate) {
        this.activityEndDate = activityEndDate;
    }

    public String getActivityLicensePlate() {
        return activityLicensePlate;
    }

    public void setActivityLicensePlate(String activityLicensePlate) {
        this.activityLicensePlate = activityLicensePlate;
    }

    public String getActivityWeeds() {
        return activityWeeds;
    }

    public void setActivityWeeds(String activityWeeds) {
        this.activityWeeds = activityWeeds;
    }

    public String getActivityStartTime() {
        return activityStartTime;
    }

    public void setActivityStartTime(String activityStartTime) {
        this.activityStartTime = activityStartTime;
    }

    public String getActivityEndTime() {
        return activityEndTime;
    }

    public void setActivityEndTime(String activityEndTime) {
        this.activityEndTime = activityEndTime;
    }

    public List<ActivityVehicleDTO> getList() {
        return list;
    }

    public void setList(List<ActivityVehicleDTO> list) {
        this.list = list;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

}
