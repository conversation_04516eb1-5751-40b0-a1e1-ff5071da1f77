package com.extracme.evcard.mmp.rest.vipcard;

import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.service.vipcard.IMmpUserCardService;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/12/28
 */
@Slf4j
@Api(value="vipcard", tags = "用户权益-付费会员卡")
@RestController
@RequestMapping("api/vipcard")
public class MmpVipCardController {
    @Resource
    IMmpUserCardService mmpUserCardService;

    /**
     * 用户付费会员卡列表查询
     * @param queryDto
     * @param request
     * @return
     */
    @RequestMapping(value = "userCard/list/{authId}", method = RequestMethod.POST)
    public DefaultWebRespVO queryUserCardsPage(@PathVariable("authId") String authId,
                                      @RequestBody MemberCardQueryDto queryDto, HttpServletRequest request) {
        PageBeanBO<MemberCardListViewDto> pageBean = mmpUserCardService.queryUserCardsPage(authId, queryDto);
        return DefaultWebRespVO.getSuccessVO(pageBean);
    }

    /**
     * 用户付费会员卡-购买明细
     * @param request
     * @return
     */
    @RequestMapping(value = "userCard/purchaseList/{authId}/{cardId}", method = RequestMethod.GET)
    public DefaultWebRespVO queryUserCardPurchaseLogs(@PathVariable("authId") String authId,
                                      @PathVariable("cardId") Long cardId, HttpServletRequest request) {
        List result = mmpUserCardService.queryUserPurchaseListByCardId(authId, cardId);
        return DefaultWebRespVO.getSuccessVO(result);
    }

    /**
     * 卡片作废
     * @param authId
     * @param cardId
     * @param request
     * @return
     */
    @RequestMapping(value = "userCard/disable/{authId}/{cardId}", method = RequestMethod.PUT)
    DefaultWebRespVO disable(@PathVariable("authId") String authId,
                             @PathVariable("cardId") Long cardId,
                             HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = mmpUserCardService.disableMemberCard(authId, cardId, request);
            vo.setMessage(respDTO.getMessage());
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            log.error("作废卡片异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "作废失败");
        }
        return vo;
    }


    /**
     * 用户付费会员卡列表查询
     * @param queryDto
     * @param request
     * @return
     */
    @RequestMapping(value = "purchaseRecord/list", method = RequestMethod.POST)
    public DefaultWebRespVO queryUserCardsPage(@RequestBody CardPurchasePageQueryDto queryDto, HttpServletRequest request) {
        PageBeanBO<CardPurchaseListDetailDto> pageBean = mmpUserCardService.queryCardPurchaseRecordsPage(queryDto);
        return DefaultWebRespVO.getSuccessVO(pageBean);
    }

    /**
     * 导出购买记录
     *
     * @param queryDto
     * @return
     */
    @RequestMapping(value = "purchaseRecord/export", method = RequestMethod.GET)
    public DefaultWebRespVO exportCardPurchaseList(CardPurchasePageQueryDto queryDto,
                                               HttpServletRequest request, HttpServletResponse response) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        DefaultServiceRespDTO respDTO = mmpUserCardService.exportCardPurchaseList(queryDto, response);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        vo.setMessage("导出成功！");
        return vo;
    }

    /**
     * 用户付费会员卡-折扣详情
     * @param queryDto
     * @param request
     * @return
     */
    @RequestMapping(value = "userCard/discountDetail", method = RequestMethod.POST)
    public DefaultWebRespVO queryCardUseDetailPage(@RequestBody CardDiscountInfoQueryDto queryDto, HttpServletRequest request) {
        if(queryDto == null || queryDto.getUserCardNo() == null) {
            return new DefaultWebRespVO("-1", "请提供用户卡编号");
        }
        PageBeanBO<UserCardDiscountViewDto> pageBean = mmpUserCardService.selectCardDiscountPage(queryDto);
        return DefaultWebRespVO.getSuccessVO(pageBean);
    }


    /**
     * 开始导入优惠券
     * @param id
     * @param file
     * @param issueType 1赠送 2兑换
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "userCard/import", method = RequestMethod.POST)
    public DefaultWebRespVO startCouponImport(@RequestParam(value = "file1", required = false) CommonsMultipartFile file,
                                              @RequestParam(value = "userKeys", required = false) String userKeys,
                                              @RequestParam(value = "id", required = true) Long id,
                                              @RequestParam(value = "issueType", required = true) int issueType,
                                              @RequestParam(value = "importType", required = true) int importType,
                                              HttpServletRequest request,
                                              HttpServletResponse response){

        if(file == null && StringUtils.isBlank(userKeys)) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "请导入发卡名单或录入会员信息");
        }
        try {
            OperatorDto optUser = new OperatorDto();
            ComModel comModel = ComUtil.getUserInfo(request);
            optUser.setOriginSystem("mmp");
            optUser.setOperatorId(comModel.getCreateOperId());
            optUser.setOperatorName(comModel.getCreateOperName());

            DefaultServiceRespDTO respDTO = null;
            if(file != null) {
                DiskFileItem fi = (DiskFileItem) file.getFileItem();
                File file1 = fi.getStoreLocation();
                String fileName = file.getOriginalFilename();
                respDTO = mmpUserCardService.importUserCards(id, file1, fileName, issueType, importType, optUser);
            }else {
                String[] array = StringUtils.split(userKeys, ",");
                if(array == null || array.length == 0) {
                    return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "请录入会员手机号或会员id");
                }
                respDTO = mmpUserCardService.importUserCards(id, Arrays.asList(array), issueType, importType, optUser);
            }
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
            if (respDTO.getCode() == 1){
                return new DefaultWebRespVO("1", respDTO.getMessage());
            }
            return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "导入完成");
        } catch (Exception e) {
            log.warn("导入名单发卡：操作失败, id=" + id, e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "导入失败");
        }
    }

    /**
     * 下载导入失败错误报告
     * @return
     */
    @ApiOperation(value="错误报告下载", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id",value = "活动id", required = true, dataType = "String", paramType ="path")})
    @RequestMapping(value = "userCard/downloadImportErrorReport", method = RequestMethod.GET)
    public DefaultWebRespVO downloadErrorTemplate(
            @RequestParam(value = "id", required = true) Long activityId,
            HttpServletRequest request, HttpServletResponse response){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        try {
            mmpUserCardService.getErrorReport(activityId, request, response);
        }catch (Exception e){
            log.error("导入名单发卡：下载错误报告出错, activityId=" + activityId, e);
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("下载失败");
            return defaultWebRespVO;
        }
        return defaultWebRespVO;
    }

    /**
     * 下载导入模板
     * @return
     */
    @RequestMapping(value = "userCard/getImportTemplateUrl", method = RequestMethod.GET)
    public DefaultWebRespVO downloadErrorTemplate(
            @RequestParam(value = "importType", required = true) int importType,
            HttpServletRequest request, HttpServletResponse response){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        try {
            String url = mmpUserCardService.getTemplateUrl(importType);
            return DefaultWebRespVO.getSuccessVO(url);
        }catch (Exception e){
            log.error("导入名单发卡：获取模板url失败, importType=" + importType, e);
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("获取模板失败");
            return defaultWebRespVO;
        }
    }
}
