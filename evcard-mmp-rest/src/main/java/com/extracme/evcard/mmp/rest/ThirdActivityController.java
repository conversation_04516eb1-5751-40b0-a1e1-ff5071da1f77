package com.extracme.evcard.mmp.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.bo.ShopInfoParamsBO;
import com.extracme.evcard.mmp.bo.ThirdCouponModelParamsBO;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.ShopInfoFullDTO;
import com.extracme.evcard.mmp.dto.ThirdActivityDetailDTO;
import com.extracme.evcard.mmp.dto.ThirdActivityFullDTO;
import com.extracme.evcard.mmp.dto.ThirdOfferCouponDTO;
import com.extracme.evcard.mmp.dto.activity.ActivityCouponModelPageDTO;
import com.extracme.evcard.mmp.service.IMarketActivityService;
import com.extracme.evcard.mmp.service.IThirdActivityService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2017/12/13
 * \* Time: 15:35
 * \* To change this template use File | Settings | File Templates.
 * \* Description:第三方活动发券配置
 * \
 */
@Api(value="thirdCoupon", tags = "第三方活动发券[1]")
@RestController
@RequestMapping("api/thirdCoupon")
public class ThirdActivityController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IThirdActivityService thirdActivityServiceImpl;

    @Resource
    IMarketActivityService marketActivityServiceImpl;

    /**
     * 新增第三方活动发券配置
     *
     * @param thirdActivityFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="新增第三方活动发券", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "addThirdOfferCouponActivity", method = RequestMethod.POST)
    public DefaultWebRespVO addThirdOfferCouponActivity(@RequestBody ThirdActivityFullDTO thirdActivityFullDTO,
                                                        HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = thirdActivityServiceImpl.addThirdOfferCouponActivity(thirdActivityFullDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("新增第三方活动发券配置信息...");
        return vo;
    }

    /**
     * 新增第三方活动发券配置
     *
     * @param thirdActivityFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="复制第三方活动发券", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "copyThirdOfferCouponActivity", method = RequestMethod.POST)
    public DefaultWebRespVO copyThirdOfferCouponActivity(@RequestBody ThirdActivityFullDTO thirdActivityFullDTO,
                                                        HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = thirdActivityServiceImpl.copyThirdOfferCouponActivity(thirdActivityFullDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            log.error("复制异常={}",JSON.toJSONString(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("复制成功");
        log.debug("复制新增第三方活动发券配置信息...");
        return vo;
    }

    /**
     * 修改第三方活动发券配置
     *
     * @param thirdActivityFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="修改第三方活动发券配置", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @RequestMapping(value = "updateThirdOfferCouponActivity", method = RequestMethod.PUT)
    public DefaultWebRespVO updateThirdOfferCouponActivity(@RequestBody ThirdActivityFullDTO thirdActivityFullDTO,
                                                           HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = thirdActivityServiceImpl.updateThirdOfferCouponActivity(thirdActivityFullDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("修改第三方活动发券配置信息...");
        return vo;
    }


    /**
     * 获取第三方活动发券信息
     *
     * @param id
     * @return
     */
    @ApiOperation(value="获取第三方活动发券信息", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "thirdOfferCouponActivityDetail/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO thirdOfferCouponActivityDetail(@PathVariable("id") Long id) {
        ThirdActivityDetailDTO thirdActivityDetailDTO = thirdActivityServiceImpl.thirdOfferCouponActivityDetail(id);
        if (null == thirdActivityDetailDTO) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动信息不存在");
        }
        log.debug("获取第三方活动发券配置信息...");
        return DefaultWebRespVO.getSuccessVO(thirdActivityDetailDTO);
    }

    /**
     * 获取第三方优惠券模板列表
     *
     * @param paramsBO
     * @return
     */
    @ApiOperation(value="券模板列表", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "getThirdCouponModelPage", method = RequestMethod.POST)
    public DefaultWebRespVO getThirdCouponModelPage(@RequestBody ThirdCouponModelParamsBO paramsBO) {
        ActivityCouponModelPageDTO thirdActivityCouponModelDTO = marketActivityServiceImpl.getCouponModelPage(paramsBO);
        return DefaultWebRespVO.getSuccessVO(thirdActivityCouponModelDTO);
    }

    /**
     * 删除活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @ApiOperation(value="活动删除", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "deleteActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO deleteActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = thirdActivityServiceImpl.deleteActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("删除活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已删除");
    }

    /**
     * 停止第三方活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @ApiOperation(value="活动暂停", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "suspendThirdActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO suspendThirdActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = thirdActivityServiceImpl.suspendThirdActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("停止第三方活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已停止");
    }

    /**
     * 立即开始第三方活动
     *
     * @param id      活动id
     * @param request
     * @return
     */
    @ApiOperation(value="立即开始第三方活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "immediateStartThirdActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO immediateStartThirdActivity(@PathVariable("id") Long id,
                                                        HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = thirdActivityServiceImpl.immediateStartThirdActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("立即开始第三方活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已开始");
    }

    /**
     * 活动发布（审核通过）
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动发布（审核通过）", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "publish/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO publish(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = thirdActivityServiceImpl.publish(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已发布");
    }

    /**
     * 暂停第三方活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @ApiOperation(value="暂停第三方活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "pauseThirdActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO pauseThirdActivity(@PathVariable("id") Long id,
                                               HttpServletRequest request) {
        DefaultServiceRespDTO respDTO  = thirdActivityServiceImpl.pauseThirdActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("暂停第三方活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已暂停");
    }

    /**
     * 恢复第三方活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @ApiOperation(value="恢复第三方活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "resumeThirdActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO resumeThirdActivity(@PathVariable("id") Long id,
                                                HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = thirdActivityServiceImpl.resumeThirdActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("恢复第三方活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已恢复");
    }

    /**
     * 发放第三方活动券
     *
     * @param thirdOfferCouponDTO
     * @return
     */
    @ApiOperation(value="发放第三方活动券", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "offerThirdCoupon", method = RequestMethod.POST)
    public DefaultWebRespVO offerThirdCoupon(@RequestHeader String sn, @RequestHeader String timestamp,
                                             @RequestBody ThirdOfferCouponDTO thirdOfferCouponDTO) {
        log.info("----------第三方发放优惠券调用接口offerThirdCoupon已进入------参数：sign " + sn +
                " timestamp: " + timestamp + " thirdOfferCouponDto: " + JSON.toJSONString(thirdOfferCouponDTO));
        DefaultServiceRespDTO respDTO = thirdActivityServiceImpl.offerThirdActivityCoupon(sn, timestamp, thirdOfferCouponDTO);
        if (null != respDTO.getCode() && respDTO.getCode() != 0) {
            log.info("发放第三方活动券失败..." + respDTO.getMessage());
            return new DefaultWebRespVO(respDTO.getCode().toString(), respDTO.getMessage());
        }else{
            return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "优惠券已发放");
        }
    }

//    /**
//     * 获得网点
//     *
//     * @param shopInfoParamsBO
//     * @return
//     */
//    @RequestMapping(value = "getShopInfos", method = RequestMethod.POST)
//    public DefaultWebRespVO getShopInfos(@RequestBody ShopInfoParamsBO shopInfoParamsBO) {
//        PageBeanBO<ShopInfoFullDTO> pageBeanBO = thirdActivityServiceImpl.getShopInfos(shopInfoParamsBO);
//        log.debug("获得所有网点...");
//        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
//    }

    /*@RequestMapping(value = "initCouponStatistics", method = RequestMethod.GET)
    public DefaultWebRespVO initCouponStatistics() {
        DefaultServiceRespDTO defaultServiceRespDTO = thirdActivityServiceImpl.initCouponStatistics();
        return new DefaultWebRespVO(defaultServiceRespDTO.getCode().toString(), defaultServiceRespDTO.getMessage());
    }*/
}