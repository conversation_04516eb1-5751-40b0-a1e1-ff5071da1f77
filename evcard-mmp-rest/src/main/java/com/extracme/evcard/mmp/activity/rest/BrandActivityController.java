package com.extracme.evcard.mmp.activity.rest;

import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.bo.ThirdCouponModelParamsBO;
import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.BrandActivityFullDTO;
import com.extracme.evcard.mmp.dto.activity.ActivityCouponModelPageDTO;
import com.extracme.evcard.mmp.dto.activity.BrandActivityDetailDTO;
import com.extracme.evcard.mmp.service.IBrandActivityService;
import com.extracme.evcard.mmp.vo.BrandActivityFullVO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2018/7/2
 * \* Time: 14:23
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 品牌合作活动发券
 * \
 */
@Api(value="brandActivity", tags = "品牌合作活动发券[8]")
@RestController
@RequestMapping("api/brandActivity")
public class BrandActivityController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IBrandActivityService brandActivityServiceImpl;

    private DefaultWebRespVO checkUploadImageFile(String fileName, DefaultWebRespVO vo) {
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
        // 允许上传的文件类型
        String suffixList = "jpg,png,jpeg";
        if (!suffixList.contains(suffix.trim().toLowerCase())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("上传失败:图片限制格式是jpg,jpeg,png型");
            return vo;
        }
        return vo;
    }

    private DefaultWebRespVO checkUploadFile(String fileName, DefaultWebRespVO vo) {
        // 允许上传的文件类型
        String suffixList = "xls,xlsx";
        if (null != fileName) {
            String suffix = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
            if (!suffixList.contains(suffix.trim().toLowerCase())) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("文件上传失败:文件限制格式应该为xls,xlsx型！");
                return vo;
            }
        }

        return vo;
    }

    /**
     * 新增品牌活动
     *
     * @param brandActivityFullVO
     * @param request
     * @return
     */
    @ApiOperation(value="新增品牌活动", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "addBrandActivity", method = RequestMethod.POST)
    public DefaultWebRespVO addBrandActivity(@RequestParam(value = "imageFile") CommonsMultipartFile imageFile,
                                             @RequestParam(value = "codeFile") CommonsMultipartFile codeFile,
                                             BrandActivityFullVO brandActivityFullVO,
                                             HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            String imageFileName = imageFile.getOriginalFilename();
            InputStream imageFileInputStream = imageFile.getInputStream();
            checkUploadImageFile(imageFileName, vo);
            if (vo.getCode().equals(Contants.RETURN_ERROR_CODE)) {
                return vo;
            }
            String codeFileName = codeFile.getOriginalFilename();
            InputStream codeFileInputStream = codeFile.getInputStream();
            checkUploadFile(codeFileName, vo);
            if (vo.getCode().equals(Contants.RETURN_ERROR_CODE)) {
                return vo;
            }
            BrandActivityFullDTO brandActivityFullDTO = new BrandActivityFullDTO();
            BeanCopyUtils.copyProperties(brandActivityFullVO, brandActivityFullDTO);
            DefaultServiceRespDTO respDTO = brandActivityServiceImpl.add(imageFileInputStream,
                    imageFileName, codeFileInputStream, codeFileName, brandActivityFullDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (IOException e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("新增品牌活动失败");
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("新增品牌活动信息...");
        return vo;
    }


    /**
     * 修改品牌活动
     *
     * @param brandActivityFullVO
     * @param request
     * @return
     */
    @ApiOperation(value="修改品牌活动", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "updateBrandActivity", method = RequestMethod.POST)
    public DefaultWebRespVO updateBrandActivity(@RequestParam(value = "imageFile", required = false) CommonsMultipartFile imageFile,
                                                @RequestParam(value = "codeFile", required = false) CommonsMultipartFile codeFile,
                                                BrandActivityFullVO brandActivityFullVO,
                                                HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            String imageFileName = null;
            InputStream imageFileInputStream = null;
            if (null != imageFile) {
                imageFileName = imageFile.getOriginalFilename();
                imageFileInputStream = imageFile.getInputStream();
                checkUploadImageFile(imageFileName, vo);
                if (vo.getCode().equals(Contants.RETURN_ERROR_CODE)) {
                    return vo;
                }
            }
            String codeFileName = null;
            InputStream codeFileInputStream = null;
            if (null != codeFile) {
                codeFileName = codeFile.getOriginalFilename();
                codeFileInputStream = codeFile.getInputStream();
                checkUploadFile(codeFileName, vo);
                if (vo.getCode().equals(Contants.RETURN_ERROR_CODE)) {
                    return vo;
                }
            }
            BrandActivityFullDTO brandActivityFullDTO = new BrandActivityFullDTO();
            BeanCopyUtils.copyProperties(brandActivityFullVO, brandActivityFullDTO);
            DefaultServiceRespDTO respDTO = brandActivityServiceImpl.update(imageFileInputStream,
                    imageFileName, codeFileInputStream, codeFileName, brandActivityFullDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (IOException e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("修改品牌活动失败");
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("修改品牌活动信息...");
        return vo;
    }

    /**
     * 获取品牌发券活动详情信息
     *
     * @param id 活动id
     * @return
     */
    @ApiOperation(value="获取品牌发券活动详情信息", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "brandActivityDetail/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO brandActivityDetail(@PathVariable("id") Long id) {
        BrandActivityDetailDTO brandActivityDetail = brandActivityServiceImpl.queryDetails(id);
        if (null == brandActivityDetail) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动信息不存在");
        }
        log.debug("获取品牌发券活动详情信息...");
        return DefaultWebRespVO.getSuccessVO(brandActivityDetail);
    }

    /**
     * 获取品牌发券活动优惠券模板列表
     *
     * @param paramsBO
     * @return
     */
    @ApiOperation(value="获取品牌发券活动优惠券模板列表", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "getBrandCouponModelPage", method = RequestMethod.POST)
    public DefaultWebRespVO getBrandCouponModelPage(@RequestBody ThirdCouponModelParamsBO paramsBO) {
        ActivityCouponModelPageDTO activityCouponModelPageDTO = brandActivityServiceImpl.getCouponModelPage(paramsBO);
        log.debug("获取品牌发券活动优惠券模板列表...");
        return DefaultWebRespVO.getSuccessVO(activityCouponModelPageDTO);
    }


    /**
     * 下载已导入的品牌券码
     *
     * @param request
     * @param response
     * @return
     */
    @ApiOperation(value="下载已导入的品牌券码", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "exportBrandCouponCode", method = RequestMethod.GET)
    public DefaultWebRespVO exportBrandCouponCode(@RequestParam("id") long activityId,
                                                  HttpServletRequest request, HttpServletResponse response) {
        DefaultServiceRespDTO respDTO = brandActivityServiceImpl.exportBrandCouponCode(activityId, request, response);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "品牌券码导出成功");
    }


    /**
     * 立即开始品牌活动
     *
     * @param id      活动id
     * @param request
     * @return
     */
    @ApiOperation(value="立即开始品牌活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "immediateStartBrandActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO immediateStartBrandActivity(@PathVariable("id") Long id,
                                                        HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = brandActivityServiceImpl.start(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("立即开始品牌活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已开始");
    }


    /**
     * 立即开始品牌活动
     *
     * @param id      活动id
     * @param request
     * @return
     */
    @ApiOperation(value="发布品牌活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "publish/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO publish(@PathVariable("id") Long id,
                                                        HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = brandActivityServiceImpl.publish(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("立即开始品牌活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已发布");
    }


    /**
     * 停止品牌活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @ApiOperation(value="停止品牌活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "suspendBrandActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO stop(@PathVariable("id") Long id, HttpServletRequest request) {
        ComModel comModel = ComUtil.getUserInfo(request);
        String createOperName = comModel.getCreateOperName();
        Long createOperId = comModel.getCreateOperId();
        String operatorContent = "停止";
        DefaultServiceRespDTO respDTO = brandActivityServiceImpl.stop(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("停止品牌活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已停止");
    }


    /**
     * 删除品牌活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @ApiOperation(value="删除品牌活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "deleteBrandActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO delete(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = brandActivityServiceImpl.delete(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("删除品牌活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已删除");
    }
}