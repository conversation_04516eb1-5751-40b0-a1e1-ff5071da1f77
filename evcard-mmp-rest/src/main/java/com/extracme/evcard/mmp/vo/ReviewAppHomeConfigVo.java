package com.extracme.evcard.mmp.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @description审核app显示配置
 * @date 2020年10月22日 12:03
 */
@Data
public class ReviewAppHomeConfigVo {

    /**
     * 主键
     */
    @NotNull(message = "编号不能为空")
    private List<Long> id;

    /**
     * 1 审核通过 2 审核不通过
     */
    @NotNull(message = "请选择审核状态")
    //@Range(min = 1,max = 2,message = "选择方式有误")
    private Integer reviewStatus;

}
