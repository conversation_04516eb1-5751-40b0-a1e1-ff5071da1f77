package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.service.NightCarInfoService;
import com.extracme.evcard.mmp.vo.NightCarInfoVO;
import com.extracme.evcard.mmp.vo.NightIdVO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 项目名称: evcard-mmp-rest
 * 类名称: NightCarInfoController
 * 类描述: TODO
 * 创建人: lilin-李林
 * 创建时间: 2017年11月7日 下午3:33:39
 * 修改备注:
 * @version1.0
 *
 */
@RestController
@RequestMapping("api")
public class NightCarInfoController {
    /**
     * log
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * @Resource
     */
    @Resource
    private NightCarInfoService nightCarInfoServiceImpl;

    /**
     * 包夜车活动一览
     * @param pageNum 页码
     * @param pageSize 每页显示条数
     * @param isAll 活动名称
     * @param name    活动名称
     * @param id  活动ID
     * @param activityStatus 活动状态
     * @param orgId  所属公司
     * @param createdStartTime  活动开始时间
     * @param createdEndTime    活动结束时间
     * @param createOperName 操作人
     * @return  vo  返回值
     */
    @RequestMapping(value = "nightCarInfoList", method = RequestMethod.GET)
    public DefaultWebRespVO queryNightCarInfoList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                  @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize, @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
                                                  @RequestParam(value = "name", required = false) String name, @RequestParam(value = "id", required = false) String id,
                                                  @RequestParam(value = "activityStatus", required = false) Integer activityStatus, @RequestParam(value = "orgId", required = false) String orgId,
                                                  @RequestParam(value = "createdStartTime", required = false) String createdStartTime, @RequestParam(value = "createdEndTime", required = false) String createdEndTime,
                                                  @RequestParam(value = "type", required = false) Integer type, @RequestParam(value = "createOperName", required = false) String createOperName ) {
        PageBeanBO<NightCarInfoDTO> pageBeanBO = nightCarInfoServiceImpl.queryNightCarInfoList(pageNum, pageSize, isAll, name, id, activityStatus, orgId, createdStartTime, createdEndTime, type, createOperName);
        log.debug("包夜车信息一览...");
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);

    }

    /**
     * 新增包夜车
     * @param nightCarInfoVO 对象
     * @param request  请求
     * @return vo  返回值
     */
    @RequestMapping(value = "nightCarInfo", method = RequestMethod.POST)
    public DefaultWebRespVO addMembershipInfo(@RequestBody NightCarInfoVO nightCarInfoVO, HttpServletRequest request) {
        NightCarDTO nightCarDTO = new NightCarDTO();
        BeanCopyUtils.copyProperties(nightCarInfoVO, nightCarDTO);
        DefaultServiceRespDTO respDTO = nightCarInfoServiceImpl.addNightCarInfo(nightCarDTO, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("新增包夜车信息...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功");
    }

    /**
     * 修改包夜车
     * @param id id
     * @param nightCarInfoVO 对象
     * @param request 请求
     * @return vo  返回值
     */
    @RequestMapping(value = "updateNightCarInfo", method = RequestMethod.PUT)
    public DefaultWebRespVO updateNightCarInfo(@RequestBody NightCarInfoVO nightCarInfoVO, HttpServletRequest request) {
        NightCarDTO nightCarDTO = new NightCarDTO();
        BeanCopyUtils.copyProperties(nightCarInfoVO, nightCarDTO);
        DefaultServiceRespDTO respDTO = nightCarInfoServiceImpl.updateNightCarInfo(nightCarDTO.getId(), nightCarDTO, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("修改包夜车信息...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功");
    }

    /**
     * 停止包夜车活动
     * @param id id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "suspendNightCarInfo", method = RequestMethod.PUT)
    public DefaultWebRespVO suspendNightCarInfo(@RequestBody NightIdVO nightIdVO, HttpServletRequest request) {

        DefaultServiceRespDTO respDTO = nightCarInfoServiceImpl.updateNightCar(nightIdVO.getId(), request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("停止包夜车");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动截止日期更新到今天");
    }

    /**
     * 删除包夜车活动
     * @param id id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "deleteNightCarInfo", method = RequestMethod.PUT)
    public DefaultWebRespVO deleteNightCarInfo(@RequestBody NightIdVO nightIdVO, HttpServletRequest request) {

        DefaultServiceRespDTO respDTO = nightCarInfoServiceImpl.deleteNightCarInfo(nightIdVO.getId(), request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("删除包夜车");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已删除");
    }

    /**
     * 包夜车活动详情
     * @param id id
     * @return vo 返回值
     */
    @RequestMapping(value = "queryCarInfo", method = RequestMethod.GET)
    public DefaultWebRespVO queryCarInfo(@RequestParam(value = "id", required = true) String id) {

        NightCarActivityDTO nightCarActivityDTO = nightCarInfoServiceImpl.queryCarInfo(id);
        if(null == nightCarActivityDTO){
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE,"获取详情失败");
        }
        log.debug("包夜车活动详情");
        return DefaultWebRespVO.getSuccessVO(nightCarActivityDTO);
    }

    /**
     * 日志一览
     * @param pageNum 页码
     * @param pageSize 条数
     * @param id id
     * @return vo 返回值
     */
    @RequestMapping(value = "nightCarOperatorLog", method = RequestMethod.GET)
    public DefaultWebRespVO queryUserInfoList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize, @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
            @RequestParam(value = "id", required = true) String id) {
        PageBeanBO<NightCarOperatorLogDTO> pageBeanBO = new PageBeanBO<>();
        try {
            pageBeanBO = nightCarInfoServiceImpl.queryUserAll(pageNum, pageSize, isAll, id);
//            if (CollectionUtils.isEmpty(pageBeanBO.getList())) {
//                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE,"日志记录不存在");
//            }
        }catch (Exception e){
            e.printStackTrace();
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE,"获取日志记录失败");
        }

        log.debug("日志操作情况一览...");
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);

    }

    @RequestMapping(value = "vehicleModel", method = RequestMethod.GET)
    public DefaultWebRespVO queryVehicleModel(HttpServletRequest request) {
        List<VehicleModel> list = nightCarInfoServiceImpl.queryVehicleModel(request);
        return DefaultWebRespVO.getSuccessVO(list);

    }
}
