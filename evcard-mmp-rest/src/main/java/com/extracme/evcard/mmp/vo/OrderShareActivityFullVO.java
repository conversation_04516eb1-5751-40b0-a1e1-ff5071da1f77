package com.extracme.evcard.mmp.vo;

import com.extracme.evcard.mmp.dto.ThirdCouponModelDTO;

import java.io.Serializable;
import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2018/7/9
 * \* Time: 17:20
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 订单分享活动VO
 * \
 */
public class OrderShareActivityFullVO implements Serializable {

    private static final long serialVersionUID = 4432247959015128211L;
    /**
     * 活动名称
     */
    private String activityName;

    private String remark;

    /**
     * 组织机构ID
     */
    private String orgId;
    /**
     * 活动开始日期
     */
    private String activityStartDate;
    /**
     * 活动结束日期
     */
    private String activityEndDate;
    /**
     * 每个订单分享链接可领取次数
     */
    private Integer orderShareLimit;
    /**
     * 每个用户单链接可领取次数
     */
    private Integer userLinkLimit;
    /**
     * 每个用户单天可领取次数
     */
    private Integer userDayLimit;
    /**
     * 订单金额
     */
    private Double orderAmount;
    /**
     * 封面图片
     */
    private String coverPicture;
    /**
     * 分享标题
     */
    private String shareTitle;
    /**
     * 分享文案
     */
    private String shareContent;
    /**
     * 活动/免责说明
     */
    private String activityDesc;
    /**
     * 广告位1图片
     */
    private String adPicture1;
    /**
     * 广告位2图片
     */
    private String adPicture2;
    /**
     * 广告位链接1
     */
    private String adUrl1;
    /**
     * 广告位链接2
     */
    private String adUrl2;
    /**
     * 出现概率1
     */
    private Integer couponRate1;
    /**
     * 出现概率2
     */
    private Integer couponRate2;
    /**
     * 出现概率3
     */
    private Integer couponRate3;
    /**
     * 出现概率4
     */
    private Integer couponRate4;
    /**
     * 出现概率5
     */
    private Integer couponRate5;
    /**
     * 需要传入优惠券模板 因form-data提交时包含文件，则上传list无法映射
     */
    private String couponModelsVal1;
    private String couponModelsVal2;
    private String couponModelsVal3;
    private String couponModelsVal4;
    private String couponModelsVal5;
    /**
     * 不需要传入优惠券模板
     */
    private List<ThirdCouponModelDTO> couponModels;
    /**
     * 修改时 传入修改过的优惠券模板id 因form-data提交时包含文件，则上传list无法映射
     */
    private String updateCouponSeqListVal;
    /**
     * 修改时 修改过的优惠券模板id 不需要传入
     */
    private List<Long> updateCouponSeqList;
    /**
     * 修改时 传入活动配置id
     */
    private Long shareActivityId;
    /**
     * 修改时 传入活动id
     */
    private Long id;
    /**
     * 背景颜色
     */
    private Integer backgroundColor;
    /**
     * 官网地址
     */
    private String officialWebAddress;
    /**
     * 客服电话
     */
    private String serviceTelephone;

    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getActivityStartDate() {
        return activityStartDate;
    }

    public void setActivityStartDate(String activityStartDate) {
        this.activityStartDate = activityStartDate;
    }

    public String getActivityEndDate() {
        return activityEndDate;
    }

    public void setActivityEndDate(String activityEndDate) {
        this.activityEndDate = activityEndDate;
    }

    public Double getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(Double orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getCoverPicture() {
        return coverPicture;
    }

    public void setCoverPicture(String coverPicture) {
        this.coverPicture = coverPicture;
    }

    public String getShareTitle() {
        return shareTitle;
    }

    public void setShareTitle(String shareTitle) {
        this.shareTitle = shareTitle;
    }

    public String getShareContent() {
        return shareContent;
    }

    public void setShareContent(String shareContent) {
        this.shareContent = shareContent;
    }

    public String getActivityDesc() {
        return activityDesc;
    }

    public void setActivityDesc(String activityDesc) {
        this.activityDesc = activityDesc;
    }

    public String getAdPicture1() {
        return adPicture1;
    }

    public void setAdPicture1(String adPicture1) {
        this.adPicture1 = adPicture1;
    }

    public String getAdPicture2() {
        return adPicture2;
    }

    public void setAdPicture2(String adPicture2) {
        this.adPicture2 = adPicture2;
    }

    public String getAdUrl1() {
        return adUrl1;
    }

    public void setAdUrl1(String adUrl1) {
        this.adUrl1 = adUrl1;
    }

    public String getAdUrl2() {
        return adUrl2;
    }

    public void setAdUrl2(String adUrl2) {
        this.adUrl2 = adUrl2;
    }

    public Integer getCouponRate1() {
        return couponRate1;
    }

    public void setCouponRate1(Integer couponRate1) {
        this.couponRate1 = couponRate1;
    }

    public Integer getCouponRate2() {
        return couponRate2;
    }

    public void setCouponRate2(Integer couponRate2) {
        this.couponRate2 = couponRate2;
    }

    public Integer getCouponRate3() {
        return couponRate3;
    }

    public void setCouponRate3(Integer couponRate3) {
        this.couponRate3 = couponRate3;
    }

    public Integer getCouponRate4() {
        return couponRate4;
    }

    public void setCouponRate4(Integer couponRate4) {
        this.couponRate4 = couponRate4;
    }

    public Integer getCouponRate5() {
        return couponRate5;
    }

    public void setCouponRate5(Integer couponRate5) {
        this.couponRate5 = couponRate5;
    }

    public String getCouponModelsVal1() {
        return couponModelsVal1;
    }

    public void setCouponModelsVal1(String couponModelsVal1) {
        this.couponModelsVal1 = couponModelsVal1;
    }

    public String getCouponModelsVal2() {
        return couponModelsVal2;
    }

    public void setCouponModelsVal2(String couponModelsVal2) {
        this.couponModelsVal2 = couponModelsVal2;
    }

    public String getCouponModelsVal3() {
        return couponModelsVal3;
    }

    public void setCouponModelsVal3(String couponModelsVal3) {
        this.couponModelsVal3 = couponModelsVal3;
    }

    public String getCouponModelsVal4() {
        return couponModelsVal4;
    }

    public void setCouponModelsVal4(String couponModelsVal4) {
        this.couponModelsVal4 = couponModelsVal4;
    }

    public String getCouponModelsVal5() {
        return couponModelsVal5;
    }

    public void setCouponModelsVal5(String couponModelsVal5) {
        this.couponModelsVal5 = couponModelsVal5;
    }

    public List<ThirdCouponModelDTO> getCouponModels() {
        return couponModels;
    }

    public void setCouponModels(List<ThirdCouponModelDTO> couponModels) {
        this.couponModels = couponModels;
    }

    public String getUpdateCouponSeqListVal() {
        return updateCouponSeqListVal;
    }

    public void setUpdateCouponSeqListVal(String updateCouponSeqListVal) {
        this.updateCouponSeqListVal = updateCouponSeqListVal;
    }

    public List<Long> getUpdateCouponSeqList() {
        return updateCouponSeqList;
    }

    public void setUpdateCouponSeqList(List<Long> updateCouponSeqList) {
        this.updateCouponSeqList = updateCouponSeqList;
    }

    public Long getShareActivityId() {
        return shareActivityId;
    }

    public void setShareActivityId(Long shareActivityId) {
        this.shareActivityId = shareActivityId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrderShareLimit() {
        return orderShareLimit;
    }

    public void setOrderShareLimit(Integer orderShareLimit) {
        this.orderShareLimit = orderShareLimit;
    }

    public Integer getUserLinkLimit() {
        return userLinkLimit;
    }

    public void setUserLinkLimit(Integer userLinkLimit) {
        this.userLinkLimit = userLinkLimit;
    }

    public Integer getUserDayLimit() {
        return userDayLimit;
    }

    public void setUserDayLimit(Integer userDayLimit) {
        this.userDayLimit = userDayLimit;
    }

    public Integer getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(Integer backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public String getOfficialWebAddress() {
        return officialWebAddress;
    }

    public void setOfficialWebAddress(String officialWebAddress) {
        this.officialWebAddress = officialWebAddress;
    }

    public String getServiceTelephone() {
        return serviceTelephone;
    }

    public void setServiceTelephone(String serviceTelephone) {
        this.serviceTelephone = serviceTelephone;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}