package com.extracme.evcard.mmp.vo;

/**
 * 
 * 项目名称：evcard-mmp-rest
 * 类名称：QueryUserInfoListVO
 * 类描述：管理员获取用户列表
 * 创建人：chennian-谌年
 * 创建时间：2017年3月23日 上午10:24:08
 * 修改备注：
 * @version1.0
 *
 */
public class QueryUserInfoListVO {
	
    /** 账户名称 */
    private String username;
    
    /** 真实姓名 */
    private String name;
    
    /** 联系电话 */
    private String mobilePhone;
    
    /** 所属机构的编号 */
    private String orgCode;
    
    /** 关联角色的编号 **/
	private Long roleId;

	/** 页数 **/
	private int pageNum = 1;
	
	/** 每页的记录数 **/
	private int pageSize = 10;
	
	public int getPageNum() {
		return pageNum;
	}

	public void setPageNum(int pageNum) {
		this.pageNum = pageNum;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getMobilePhone() {
		return mobilePhone;
	}

	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public Long getRoleId() {
		return roleId;
	}

	public void setRoleId(Long roleId) {
		this.roleId = roleId;
	}

}
