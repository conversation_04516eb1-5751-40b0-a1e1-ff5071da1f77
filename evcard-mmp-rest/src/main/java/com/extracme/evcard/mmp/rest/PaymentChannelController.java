package com.extracme.evcard.mmp.rest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.evcard.activity.dto.PaymentChannelDTO;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.service.IPayChannelService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;

@RestController
@RequestMapping("api/paymentChannel")
public class PaymentChannelController {

	private final Logger log = LoggerFactory.getLogger(this.getClass());
	@Resource
	IPayChannelService payChannelServiceImpl;
	
	/**
	 * 新增支付文案通道配置
	 * @param paymentChannelDTO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "addPaymentChannel", method = RequestMethod.POST)
	public DefaultWebRespVO addPaymentChannel(@RequestBody PaymentChannelDTO paymentChannelDTO,
			HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			DefaultServiceRespDTO respDTO = payChannelServiceImpl.addPaymentChannel(paymentChannelDTO, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (Exception e) {
			log.error("addPaymentChannel异常",e);
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "新增失败");
		}
		vo.setMessage("添加成功");
		return vo;
	}
	
	/**
	 * 修改支付文案通道配置
	 * @param paymentChannelDTO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "updatePaymentChannel", method = RequestMethod.PUT)
	public DefaultWebRespVO updatePaymentChannel(@RequestBody PaymentChannelDTO paymentChannelDTO,
			HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			DefaultServiceRespDTO respDTO = payChannelServiceImpl.updatePaymentChannel(paymentChannelDTO, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (Exception e) {
			log.error("updatePaymentChannel异常",e);
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "修改失败");
		}
		vo.setMessage("修改成功");
		return vo;
	}
	
	/**
	 * 立即显示支付文案通道配置
	 * @param id 文案ID
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "startPaymentChannel/{id}", method = RequestMethod.PUT)
	public DefaultWebRespVO startPaymentChannel(@PathVariable("id") Long id, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			DefaultServiceRespDTO respDTO = payChannelServiceImpl.startPaymentChannel(id, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (Exception e) {
			log.error("startPaymentChannel异常",e);
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "显示失败");
		}
		vo.setMessage("已显示");
		return vo;
	}
	
	/**
	 * 立即结束支付文案通道配置
	 * @param id 文案ID
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "suspendPaymentChannel/{id}", method = RequestMethod.PUT)
	public DefaultWebRespVO suspendPaymentChannel(@PathVariable("id") Long id, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			DefaultServiceRespDTO respDTO = payChannelServiceImpl.suspendPaymentChannel(id, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (Exception e) {
			log.error("suspendPaymentChannel异常",e);
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "结束失败");
		}
		vo.setMessage("已结束");
		return vo;
	}
	
	/**
	 * 删除支付文案通道配置
	 * @param id 文案ID
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "delPaymentChannel/{id}", method = RequestMethod.PUT)
	public DefaultWebRespVO delPaymentChannel(@PathVariable("id") Long id, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			DefaultServiceRespDTO respDTO = payChannelServiceImpl.delPaymentChannel(id, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (Exception e) {
			log.error("delPaymentChannel异常",e);
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "删除失败");
		}
		vo.setMessage("删除成功");
		return vo;
	}
	
	/**
	 * 支付文案通道配置详情
	 * @param id 文案ID
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getPaymentChannelDetail/{id}", method = RequestMethod.GET)
	public DefaultWebRespVO getPaymentChannelDetail(@PathVariable("id") Long id, HttpServletRequest request) {
		PaymentChannelDTO dto = payChannelServiceImpl.getPaymentChannelDetail(id);
		return DefaultWebRespVO.getSuccessVO(dto);
	}
	
	/**
	 * 查询支付文案通道配置列表
	 * @param paymentChannelDTO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "queryPaymentChannelList", method = RequestMethod.POST)
	public DefaultWebRespVO queryPaymentChannelList(@RequestBody PaymentChannelDTO paymentChannelDTO, HttpServletRequest request) {
		PageBeanBO<PaymentChannelDTO> pageBean = payChannelServiceImpl.queryPaymentChannelList(paymentChannelDTO);
		return DefaultWebRespVO.getSuccessVO(pageBean);
	}
}
