package com.extracme.evcard.mmp.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.dto.ProvisionUserTagDto;
import com.extracme.evcard.membership.core.service.IMemberProvisionService;
import com.extracme.evcard.mmp.bo.ProvisionBO;
import com.extracme.evcard.mmp.bo.ProvisionFeedbackQueryBO;
import com.extracme.evcard.mmp.bo.ProvisionOperateLogBO;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.HttpURLConnHelper;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.service.IProvisionService;
import com.extracme.evcard.mmp.vo.ProvisionListInVO;
import com.extracme.evcard.mmp.vo.ProvisionOperateLogVO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.tools.zip.ZipEntry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;
import sun.misc.BASE64Encoder;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.zip.ZipOutputStream;

/**
 * 条款接口类.<br>
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "api")
public class ProvisionController {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    IProvisionService provisionServiceImpl;

    @Resource
    IMemberProvisionService memberProvisionService;

    /**
     * 获取条款列表
     *
     * @param provisionListInVO 查询条件
     * @param request
     * @return
     */
    @RequestMapping(value = "provisionList", method = RequestMethod.POST)
    public DefaultWebRespVO getProvisionList(@RequestBody ProvisionListInVO provisionListInVO, HttpServletRequest request) {
        ProvisionBO provisionBO = new ProvisionBO();
        BeanCopyUtils.copyProperties(provisionListInVO, provisionBO);
        if(StringUtils.isNotBlank(provisionListInVO.getSupplier())) {
            provisionBO.setSupplier(ComUtil.getIntValue(provisionListInVO.getSupplier()));
        }
        PageBeanBO<ProvisionBO> pageBeanBO = provisionServiceImpl.getProvisionList(provisionBO);
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    /**
     * 获取条款详细信息
     *
     * @param provisionId 条款ID
     * @param requset
     * @return
     */
    @RequestMapping(value = "provision/{provisionId}/{flg}", method = RequestMethod.GET)
    public DefaultWebRespVO getProvisionDetail(@PathVariable Long provisionId, @PathVariable Integer flg, HttpServletRequest requset) {
        ProvisionBO provisionBO = provisionServiceImpl.getProvisionDetail(provisionId, flg, requset.getRemoteUser());
        //加密返回
        if (StringUtils.isNotBlank(provisionBO.getContent())) {
            BASE64Encoder base64Decoder = new BASE64Encoder();
            String content = provisionBO.getContent();
            try {
                content = base64Decoder.encode(content.getBytes("UTF-8"));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            provisionBO.setContent(content);
        }
        return DefaultWebRespVO.getSuccessVO(provisionBO);
    }

    /**
     * 修改条款信息
     *
     * @param provisionId
     * @param provisionBO
     * @param requset
     * @return
     */
    @RequestMapping(value = "provision/{provisionId}", method = RequestMethod.PUT)
    public DefaultWebRespVO modifyProvision(@PathVariable Long provisionId, @RequestBody ProvisionBO provisionBO, HttpServletRequest requset) {
        DefaultServiceRespDTO defaultServiceRespDTO = provisionServiceImpl.modifyProvision(provisionId, provisionBO.getContent(), requset.getRemoteUser(),
                provisionBO.getUpdateTime());
        if (defaultServiceRespDTO.getCode() == 1) {
            return new DefaultWebRespVO("0000001", defaultServiceRespDTO.getMessage());
        }
        return DefaultWebRespVO.SUCCESS;
    }

    /**
     * 删除待发布条款
     *
     * @param provisionId 条款ID
     * @param requset
     * @return
     */
    @RequestMapping(value = "provision/{provisionId}", method = RequestMethod.DELETE)
    public DefaultWebRespVO deleteProvision(@PathVariable Long provisionId, HttpServletRequest requset) {
        DefaultServiceRespDTO retDTO = provisionServiceImpl.deleteProvision(provisionId, requset.getRemoteUser());
        if (retDTO.getCode() == 1) {
            return new DefaultWebRespVO("0000001", "该条款已经发布，无法删除");
        }
        return DefaultWebRespVO.SUCCESS;
    }

    /**
     * 发布
     *
     * @param provisionId 条款ID
     * @param request
     * @return
     */
    @RequestMapping(value = "provision/publish/{provisionId}", method = RequestMethod.PUT)
    public DefaultWebRespVO publishProvision(@PathVariable Long provisionId,
                                             @RequestBody ProvisionNodesDTO provisionNodes,
                                             HttpServletRequest request) {
        DefaultServiceRespDTO retDTO = provisionServiceImpl.publishProvision(provisionId, provisionNodes, request.getRemoteUser());
        if (retDTO.getCode() == 1) {
            return new DefaultWebRespVO("0000001", "该常见问题已经发布，无法再次发布");
        }
        return DefaultWebRespVO.SUCCESS;
    }

    /**
     * 获取常见问题-用户tag列表
     *
     * @param queryDto 查询条件
     * @param request
     * @return
     */
    @RequestMapping(value = "question/userTags", method = RequestMethod.POST)
    public DefaultWebRespVO userTags(@RequestBody ProvisionUserQueryDto queryDto, HttpServletRequest request) {
        ProvisionUserTagDto result = memberProvisionService.queryUserTags(queryDto.getProvisionId(), queryDto.getNodeId());
        if(result == null) {
            result = new ProvisionUserTagDto();
            result.setUsefulCounts(0L);
            result.setUselessCounts(0L);
        }
        return DefaultWebRespVO.getSuccessVO(result);
    }

    /**
     * 获取常见问题-用户建议列表
     *
     * @param queryDto 查询条件
     * @param request
     * @return
     */
    @RequestMapping(value = "question/userFeedbackList", method = RequestMethod.POST)
    public DefaultWebRespVO userFeedbackList(@RequestBody ProvisionFeedbackQueryBO queryDto, HttpServletRequest request) {
        PageBeanBO<ProvisionFeedbackDTO> pageBeanBO = provisionServiceImpl.queryUserFeedbacks(queryDto);
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    @RequestMapping(value = "question/exportFeedbacks", method = RequestMethod.GET)
    public DefaultWebRespVO exportFeedbacks(@RequestBody ProvisionFeedbackQueryBO queryBo,
                                            HttpServletRequest request,
                                            HttpServletResponse response) {
        DefaultServiceRespDTO respDTO = provisionServiceImpl.exportFeedbacks(queryBo, request, response);
        return DefaultWebRespVO.getSuccessVO(respDTO);
    }

    @RequestMapping(value = "question/exportFeedback", method = RequestMethod.GET)
    public DefaultWebRespVO exportFeedback(@RequestParam(value = "provisionId", defaultValue = "0") Long provisionId,
                                            @RequestParam(value = "nodeId") String nodeId,
                                            @RequestParam(value = "startDate") String startDate,
                                            @RequestParam(value = "endDate") String endDate,
                                            HttpServletRequest request,
                                            HttpServletResponse response) {
        ProvisionFeedbackQueryBO queryBo = new ProvisionFeedbackQueryBO();
        queryBo.setProvisionId(provisionId);
        queryBo.setNodeId(nodeId);
        queryBo.setStartDate(startDate);
        queryBo.setEndDate(endDate);
        DefaultServiceRespDTO respDTO = provisionServiceImpl.exportFeedbacks(queryBo, request, response);
        return DefaultWebRespVO.getSuccessVO(respDTO);
    }


    /**
     * 获取用户历史同意条款
     *
     * @param authId   用户ID
     * @param pageNum  页码
     * @param pageSize 每页显示条数
     * @param isAll    1-统计总数
     * @return
     */
    @RequestMapping(value = "historyProvisionList", method = RequestMethod.GET)
    public DefaultWebRespVO getHistoryProvision(@RequestParam(value = "authId") String authId,
                                                @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                @RequestParam(value = "isAll", defaultValue = "0") Integer isAll) {
        PageBeanBO<HiProvisionDTO> pageBeanBO = provisionServiceImpl.getHistoryProvision(authId, pageNum, pageSize, isAll);
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    /**
     * 批量下载条款
     *
     * @param ids      条款ID数组
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "provision/download/{ids}", method = RequestMethod.GET)
    public DefaultWebRespVO downloadProvisions(@PathVariable String ids, HttpServletRequest request, HttpServletResponse response) {
        String[] strArr = ids.split("-");
        Long[] longArr = new Long[strArr.length];
        for (int i = 0; i < strArr.length; i++) {
            longArr[i] = Long.valueOf(strArr[i]);
        }
        String[] provisionAddrs = provisionServiceImpl.getFileAddress(longArr);
        ZipOutputStream zos = null;
        try {
            String filename = URLEncoder.encode("条款文件", "UTF-8");
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment;fileName=" + filename + ".zip");// 设置文件名
            zos = new ZipOutputStream(response.getOutputStream());
            for (int i = 0; i < provisionAddrs.length; i++) {
                String pAddress = provisionAddrs[i];
                for (String address : pAddress.split(",")) {
                    int index = address.lastIndexOf("/");
                    zos.putNextEntry(new ZipEntry(address.substring(index + 1)));
                    byte[] bytes = HttpURLConnHelper.getImageFromURL(address);
                    zos.write(bytes, 0, bytes.length);
                }
            }
        } catch (FileNotFoundException ex) {
            logger.error("FileNotFoundException", ex);
        } catch (Exception ex) {
            logger.error("Exception", ex);
        } finally {
            try {
                if (zos != null) {
                    zos.flush();
                    zos.close();
                }
            } catch (IOException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        return DefaultWebRespVO.SUCCESS;
    }

    /**
     * 根据条款ID获取操作日志
     *
     * @param provisionId 条款ID
     * @return
     */
    @RequestMapping(value = "operateLogList/{provisionId}", method = RequestMethod.POST)
    public DefaultWebRespVO getHiProvision(@PathVariable Long provisionId, @RequestBody ProvisionOperateLogVO provisionOperateLogVO) {
        ProvisionOperateLogBO bo = new ProvisionOperateLogBO();
        BeanCopyUtils.copyProperties(provisionOperateLogVO, bo);
        bo.setProvisionId(provisionId);
        PageBeanBO<ProvisionOperateLogBO> pageBeanBO = provisionServiceImpl.getOperateLogList(bo);
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    /**
     * 修改条款信息(会员守则、隐私条款)
     *
     * @param provisionId 条款编号
     * @param updateTime  上次更新时间
     * @param pdfFile     pdf文件
     * @param wordFile    word文件
     * @param requset
     * @return
     */
    @RequestMapping(value = "memberProvision/modifyProvision", method = RequestMethod.POST)
    public DefaultWebRespVO modifyMemberProvision(@RequestParam("provisionId") Long provisionId,
                                                  @RequestParam("updateTime") Long updateTime,
                                                  @RequestParam("pdfFile") CommonsMultipartFile pdfFile,
                                                  @RequestParam("wordFile") CommonsMultipartFile wordFile,
                                                  @RequestParam("content") String content,
                                                  HttpServletRequest requset) {
        DefaultServiceRespDTO defaultServiceRespDTO = null;
        try {
            defaultServiceRespDTO = provisionServiceImpl.modifyMemberProvision(provisionId, updateTime,
                    pdfFile.getInputStream(), pdfFile.getOriginalFilename(),
                    wordFile.getInputStream(), wordFile.getOriginalFilename(), content, requset.getRemoteUser());
            if (defaultServiceRespDTO.getCode() == 1) {
                return new DefaultWebRespVO("0000001", defaultServiceRespDTO.getMessage());
            }
            if (defaultServiceRespDTO.getCode() == -1) {
                return new DefaultWebRespVO("-1", defaultServiceRespDTO.getMessage());
            }
        } catch (IOException e) {
            e.printStackTrace();
            return new DefaultWebRespVO("-1", "提交失败");
        }
        return DefaultWebRespVO.SUCCESS;
    }

    /**
     * 获取条款详细信息(会员守则、隐私条款)
     *
     * @param provisionId 条款ID
     * @param requset
     * @return
     */
    @RequestMapping(value = "memberProvision/{provisionId}/{flg}", method = RequestMethod.GET)
    public DefaultWebRespVO getMemberProvisionDetail(@PathVariable Long provisionId, @PathVariable Integer flg, HttpServletRequest requset) {
        ProvisionBO provisionBO = provisionServiceImpl.getMemberProvisionDetail(provisionId, flg, requset.getRemoteUser());
        return DefaultWebRespVO.getSuccessVO(provisionBO);
    }

    /**
     * 发布(会员守则、隐私条款)
     *
     * @param provisionId 条款ID
     * @param request
     * @return
     */
    @RequestMapping(value = "memberProvision/publish/{provisionId}", method = RequestMethod.PUT)
    public DefaultWebRespVO memberPublishProvision(@PathVariable Long provisionId, HttpServletRequest request) {
        try {
            DefaultServiceRespDTO retDTO = provisionServiceImpl.memberPublishProvision(provisionId, request.getRemoteUser());
            if (retDTO.getCode() == 1) {
                return new DefaultWebRespVO("0000001", "该条款已经发布，无法再次发布");
            }
        }catch (Exception e) {
            return new DefaultWebRespVO("-1", "发布失败");
        }
        return DefaultWebRespVO.SUCCESS;
    }

    @RequestMapping(value = "userContract/getUrl", method = RequestMethod.GET)
    public DefaultWebRespVO getContractUrl(@RequestParam("authId") String authId,
                                           @RequestParam("templateId") String templateId,
                                           HttpServletRequest request,
                                           HttpServletResponse response) {
        DefaultServiceRespDTO respDTO = provisionServiceImpl.getContractUrl(authId, templateId);
        if (respDTO.getCode() != 0) {
            return new DefaultWebRespVO("0000001", "获取文件路径失败");
        }
        DefaultWebRespVO resp = new DefaultWebRespVO();
        resp.setData(respDTO.getData());
        return resp;
    }

    @RequestMapping(value = "userContract/download", method = RequestMethod.GET)
    public void downloadUserContract(@RequestParam("authId") String authId,
                                           @RequestParam("templateId") String templateId,
                                           HttpServletRequest request,
                                           HttpServletResponse response) {
        DefaultServiceRespDTO respDTO = provisionServiceImpl.downloadUserContract(authId, templateId, request, response);
        if (respDTO.getCode() != 0) {
            logger.error("下载协议文件失败, resp={}", JSON.toJSON(respDTO));
            //return new DefaultWebRespVO("0000001", "下载文件失败");
        }
        //return DefaultWebRespVO.getSuccessVO(respDTO);
    }
}
