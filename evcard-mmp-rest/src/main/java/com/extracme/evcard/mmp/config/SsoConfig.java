package com.extracme.evcard.mmp.config;


import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;


//@PropertySource("classpath:sso-client.properties")
@Configuration
@Data
public class SsoConfig {
    @Value("${filter.sso.excludedPages}")
    private String excludedPages;
    @Value("${excludedKeys}")
    private String excludedKeys;
    @Value("${ssoAddr}")
    private String ssoAddr;
    @Value("${sso.domainName}")
    private String 	ssoDomainName;
    @Value("${sso.innerHost}")
    private String 	ssoInnerHost;
}
