package com.extracme.evcard.mmp.rest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.evcard.mmp.service.IQRCodeManagementService;

@RestController
@RequestMapping("inner/qrcode")
public class QRCodeController {

	@Resource
	private IQRCodeManagementService qrCodeManagementServiceImpl;
	
	@RequestMapping(value = "getQRCodeUrl", method = RequestMethod.POST)
	public String getQRCodeUrl(Long id, HttpServletRequest request) {
		String qrUrl = qrCodeManagementServiceImpl.getQRCodeUrl(id, request);
		return qrUrl;
	}
}
