package com.extracme.evcard.mmp.activity.rest;

import java.io.IOException;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.extracme.evcard.mmp.dto.activity.ActivityCouponModelPageDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.evcard.mmp.bo.InviteRewardBO;
import com.extracme.evcard.mmp.bo.ThirdCouponModelParamsBO;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.InviteActivityCouponRecordDTO;
import com.extracme.evcard.mmp.dto.activity.InviteActivityDetailDTO;
import com.extracme.evcard.mmp.dto.InviteActivityFullDTO;
import com.extracme.evcard.mmp.dto.InviteRewardDTO;
import com.extracme.evcard.mmp.service.IInvitationActivityService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;

/**
 * InviteActivityController class
 *
 * <AUTHOR>
 * @date 2018/2/11 14:32
 * Description 邀请好友活动接口
 */
@RestController
@RequestMapping("api/inviteCoupon")
public class InviteActivityController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IInvitationActivityService invitationActivityServiceImpl;

    /**
     * 新增邀请好友活动配置
     *
     * @param inviteActivityFullDTO 新增对象dto
     * @param request
     * @return
     */
    @RequestMapping(value = "addInviteActivity", method = RequestMethod.POST)
    public DefaultWebRespVO add(
            @RequestBody InviteActivityFullDTO inviteActivityFullDTO,
            HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = invitationActivityServiceImpl.add(inviteActivityFullDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            log.error("新增活动失败", e);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("提交成功");
        return vo;
    }

    /**
     * 修改邀请好友活动配置
     *
     * @param inviteActivityFullDTO 修改对象dto
     * @param request
     * @return
     */
    @RequestMapping(value = "updateInviteActivity", method = RequestMethod.PUT)
    public DefaultWebRespVO update(
            @RequestBody InviteActivityFullDTO inviteActivityFullDTO,
            HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = invitationActivityServiceImpl.update(inviteActivityFullDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            log.error("更新活动失败", e);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("提交成功");
        return vo;
    }

    /**
     * 获取邀请好友活动优惠券模板列表
     *
     * @param paramsBO
     * @return
     */
    @RequestMapping(value = "getInviteCouponModelPage", method = RequestMethod.POST)
    public DefaultWebRespVO getCouponModelPage(@RequestBody ThirdCouponModelParamsBO paramsBO) {
        ActivityCouponModelPageDTO inviteActivityCouponModelDTO =
                invitationActivityServiceImpl.getCouponModelPage(paramsBO);
        return DefaultWebRespVO.getSuccessVO(inviteActivityCouponModelDTO);
    }

    /**
     * 获取邀请好友活动详情信息
     *
     * @param id 活动id
     * @return
     */
    @RequestMapping(value = "inviteActivityDetail/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO query(@PathVariable("id") Long id) {
        InviteActivityDetailDTO inviteActivityDetailDTO = invitationActivityServiceImpl.queryDetails(id);
        if (null == inviteActivityDetailDTO) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动信息不存在");
        }
        return DefaultWebRespVO.getSuccessVO(inviteActivityDetailDTO);
    }

    /**
     * 立即开始邀请好友活动
     *
     * @param id      活动id
     * @param request
     * @return
     */
    @RequestMapping(value = "immediateStartInviteActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO start(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = invitationActivityServiceImpl.start(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("立即开始邀请好友活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已开始");
    }


    /**
     * 删除邀请好友活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "deleteActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO delete(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = invitationActivityServiceImpl.delete(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("删除活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已删除");
    }

    /**
     * 停止邀请好友活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "suspendInviteActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO stop(@PathVariable("id") Long id, HttpServletRequest request) {

        DefaultServiceRespDTO respDTO = invitationActivityServiceImpl.stop(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("停止邀请好友活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已停止");

    }

    /**
     * 活动发布
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "publish/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO publish(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = invitationActivityServiceImpl.publish(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            log.error("发布活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "发布失败");
        }
        vo.setMessage("发布活动成功");
        return vo;
    }


    /**
     * 邀请好友邀请奖励列表
     *
     * @param inviteRewardBO
     * @return
     */
    @RequestMapping(value = "getInviteRewardRecords", method = RequestMethod.POST)
    public DefaultWebRespVO getInviteRewardRecords(@RequestBody InviteRewardBO inviteRewardBO) {
        PageBeanBO<InviteRewardDTO> pageBeanBO =
                invitationActivityServiceImpl.getInviteRewardRecords(inviteRewardBO);
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    /**
     * 邀请好友记录列表，根据活动id获取配置的优惠券模板信息
     *
     * @param actionIds 活动id
     * @param authId
     * @param couponTarget 1老用户， 2新用户
     * @return
     */
    @RequestMapping(value = "getInviteActivityCoupons", method = RequestMethod.GET)
    public DefaultWebRespVO getInviteActivityCoupons(
                                                     @RequestParam(value = "inviterRewardSeq", required = false) Long inviterRewardSeq,
                                                     @RequestParam(value = "inviteeRewardSeq", required = false) Long inviteeRewardSeq,
                                                     @RequestParam(value = "actionIds") String actionIds,
                                                     @RequestParam(value = "authId") String authId,
                                                     @RequestParam(value = "couponTarget", defaultValue = "1") Integer couponTarget,
                                                     @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                     @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                     @RequestParam(value = "isAll", defaultValue = "0") Integer isAll) {
        InviteActivityCouponRecordDTO inviteActivityCouponRecordDTO =
                invitationActivityServiceImpl.getInviteActivityCoupons(inviterRewardSeq, inviteeRewardSeq,
                        actionIds, authId, couponTarget, pageNum, pageSize, isAll);
        return DefaultWebRespVO.getSuccessVO(inviteActivityCouponRecordDTO);
    }

    /**
     * 导出邀请好友记录
     * @param bo
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "exportInviteRewardRecords", method = RequestMethod.GET)
	public DefaultWebRespVO exportInviteRewardRecords(InviteRewardBO bo, HttpServletRequest request,
			HttpServletResponse response) {
		try {
			response.setHeader("Content-Type", "application/vnd.ms-excel");
			response.setHeader("content-disposition", "attachment;filename=exportInviteRewardRecords.xlsx");
			invitationActivityServiceImpl.exportInviteRewardRecords(bo, response.getOutputStream());
		} catch (IOException e) {
			log.error("exportInviteRewardRecords Exception", e);
			return new DefaultWebRespVO("-1", "邀请好友记录导出失败");
		}
		return DefaultWebRespVO.SUCCESS;
	}

    /**
     * 邀请好友邀请奖励列表
     *
     * @param inviteRewardBO
     * @return
     */
    @RequestMapping(value = "insertInvitationRecords", method = RequestMethod.POST)
    public DefaultWebRespVO insertInvitationRecords(@RequestBody InviteRewardBO inviteRewardBO) {
        String startDate = "20190904130000";
        if(inviteRewardBO != null && StringUtils.isNotBlank(inviteRewardBO.getRewardStartTime())) {
            startDate = inviteRewardBO.getRewardStartTime();
        }
        String endDate = null;
        if(inviteRewardBO != null && StringUtils.isNotBlank(inviteRewardBO.getRewardEndTime())) {
            endDate = inviteRewardBO.getRewardEndTime();
        }
        invitationActivityServiceImpl.insertInviteRecords(startDate, endDate);
        return DefaultWebRespVO.getSuccessVO("ok");
    }

    /**
     * 邀请好友邀请奖励列表
     *
     * @param inviteRewardBO
     * @return
     */
    @RequestMapping(value = "insertRewordRecords", method = RequestMethod.POST)
    public DefaultWebRespVO insertInviteRewardRecords(@RequestBody InviteRewardBO inviteRewardBO) {
        String startDate = "20190910130000";
        if(inviteRewardBO != null && StringUtils.isNotBlank(inviteRewardBO.getRewardStartTime())) {
            startDate = inviteRewardBO.getRewardStartTime();
        }
        invitationActivityServiceImpl.insertInviteRewardRecords(startDate);
        return DefaultWebRespVO.getSuccessVO("ok");
    }
}