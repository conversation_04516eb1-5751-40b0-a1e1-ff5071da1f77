package com.extracme.evcard.mmp.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class MmpUserBonusReceiveRecordVO {

    //当前页
    @ApiModelProperty(value = "当前页")
    private Integer pageNum = 1;

    //页面数量
    @ApiModelProperty(value = "页面数量")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "提现记录表的主键id")
    private Long pkId;

    @ApiModelProperty(value = "会员id")
    private Long userId;

    @ApiModelProperty(value = "邀请人authid")
    private String originAuthId;

    //邀请人所属公司id
    @ApiModelProperty(value = "邀请人所属公司id")
    private String originOrgId;

    @ApiModelProperty(value = "公司名称")
    private String originOrgName;

    @ApiModelProperty(value = "会员姓名")
    private String originName;

    @ApiModelProperty(value = "会员手机号")
    private String originMobilePhone;

    @ApiModelProperty(value = "订单总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "被邀请人的订单列表")
    private Integer rewardCount;

    @ApiModelProperty(value = "提现时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date applyTime;

    @ApiModelProperty(value = "提现开始时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date applyStartTime;

    @ApiModelProperty(value = "提现结束时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date applyShutDownTime;

    @ApiModelProperty(value = "发放方式 0用户提现 1后台补发")
     private Byte sendMode;

    @ApiModelProperty(value = "奖励领取时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date receiveTime;;

    @ApiModelProperty(value = "状态0发放中 1已发放待领取 2提现失败 3提现成功 4退还中 5已退还")
    private Integer receiveStatus;

    @ApiModelProperty(value = "失败原因")
    private String remark;

    @ApiModelProperty(value = "被邀请人的信息")
    private List<Map<String,Object>> mapList;
}
