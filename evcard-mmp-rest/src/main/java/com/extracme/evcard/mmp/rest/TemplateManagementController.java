package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.TemplateManagementDTO;
import com.extracme.evcard.mmp.dto.TemplateManagementDetailDTO;
import com.extracme.evcard.mmp.dto.TemplateManagementLogDTO;
import com.extracme.evcard.mmp.service.ITemplateManagementService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;

/**
 * \* Created with IntelliJ IDEA. \* User: Elin \* Date: 2018/5/16 \* Time:
 * 13:56 \* To change this template use File | Settings | File Templates. \*
 * Description: 模板文件管理 \
 */
@RestController
@RequestMapping("api/templateManage")
public class TemplateManagementController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ITemplateManagementService templateManagementServiceImpl;

    private DefaultWebRespVO checkUploadFile(CommonsMultipartFile templateFile, DefaultWebRespVO vo, int flag) {
        // 仅新增的时候判断
        if (0 == flag && templateFile == null) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("模板文件不能为空");
            return vo;
        }
        // 允许上传的文件类型
        String suffixList = "xlsx,xls";
        if (null != templateFile) {
            String fileName = templateFile.getOriginalFilename();
            String suffix = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
            if (!suffixList.contains(suffix.trim().toLowerCase())) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("上传失败:模板限制格式是xlsx,xls型");
                return vo;
            }
        }
        return vo;
    }

    /**
     * 新增模板文件
     *
     * @param templateFile
     * @param request
     * @return
     */
    @RequestMapping(value = "addTemplate", method = RequestMethod.POST)
    public DefaultWebRespVO addTemplate(@RequestParam(value = "templateFile", required = false) CommonsMultipartFile templateFile,
                                        @RequestParam(value = "templateName", required = false) String templateName,
                                        HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            checkUploadFile(templateFile, vo, 0);
            if (Contants.RETURN_ERROR_CODE.equals(vo.getCode())) {
                return vo;
            }
            DefaultServiceRespDTO respDTO  = templateManagementServiceImpl.addTemplate(templateFile.getInputStream(), templateFile.getOriginalFilename(), templateName, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (IOException e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("新增模板失败");
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("新增模板文件...");
        return vo;
    }

    /**
     * 修改模板文件
     *
     * @param templateFile
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "updateTemplate", method = RequestMethod.POST)
    public DefaultWebRespVO updateTemplate(
            @RequestParam(value = "templateFile", required = false) CommonsMultipartFile templateFile,
            @RequestParam(value = "templateName", required = false) String templateName,
            @RequestParam(value = "id", required = false) Long id, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            InputStream fileInputStream = null;
            String fileImage = null;
            if (null != templateFile) {
                fileInputStream = templateFile.getInputStream();
                fileImage = templateFile.getOriginalFilename();
            }
            checkUploadFile(templateFile, vo, 1);
            if (Contants.RETURN_ERROR_CODE.equals(vo.getCode())) {
                return vo;
            }
            DefaultServiceRespDTO respDTO = templateManagementServiceImpl.updateTemplate(fileInputStream, fileImage, id, templateName, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (IOException e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("修改模板失败");
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("修改模板文件...");
        return vo;
    }

    /**
     * 获取模板文件详情
     *
     * @param id
     * @return
     */
    @RequestMapping(value = "queryTemplateDetail/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO queryTemplateDetail(@PathVariable("id") Long id) {
        TemplateManagementDetailDTO templateDetail = templateManagementServiceImpl.queryTemplateDetail(id);
        log.debug("获取模板文件详情...");
        return DefaultWebRespVO.getSuccessVO(templateDetail);
    }

    /**
     * 获取模板文件列表
     *
     * @param pageNum
     * @param pageSize
     * @param isAll
     * @return
     */
    @RequestMapping(value = "queryTemplateList", method = RequestMethod.GET)
    public DefaultWebRespVO queryTemplateList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                              @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                              @RequestParam(value = "isAll", defaultValue = "0") Integer isAll) {

        PageBeanBO<TemplateManagementDTO> pageBeanBO = templateManagementServiceImpl.queryTemplateList(pageNum, pageSize, isAll);
        log.debug("获取模板文件列表...");
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    /**
     * 删除模板文件
     *
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "deleteTemplate/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO deleteTemplate(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = templateManagementServiceImpl.deleteTemplate(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.error("删除模板文件");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "模板已删除");
    }

    /**
     * 查询模板文件日志列表
     * @param templateId 模板id
     * @param pageNum
     * @param pageSize
     * @param isAll
     * @param request
     * @return
     */
    @RequestMapping(value = "queryTemplateLog", method = RequestMethod.GET)
    public DefaultWebRespVO queryTemplateLog(@RequestParam("templateId") String templateId,
                                             @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                             @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                             @RequestParam(value = "isAll", defaultValue = "0") Integer isAll, HttpServletRequest request) {
        PageBeanBO<TemplateManagementLogDTO> pageBeanBO =
                templateManagementServiceImpl.queryTemplateLog(templateId, pageNum, pageSize, isAll, request);
        log.debug("查询模板文件日志列表...");
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }
}