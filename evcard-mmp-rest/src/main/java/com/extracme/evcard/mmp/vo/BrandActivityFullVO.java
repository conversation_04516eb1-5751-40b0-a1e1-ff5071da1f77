package com.extracme.evcard.mmp.vo;

import com.extracme.evcard.mmp.dto.ThirdCouponModelDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2018/7/2
 * \* Time: 14:42
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 品牌活动VO
 * \
 */
@ApiModel(value="品牌活动VO对象")
public class BrandActivityFullVO implements Serializable {
    private static final long serialVersionUID = -3190867642347869644L;

    /**
     * 活动名称
     */
    @ApiModelProperty(value="活动名称", required = true)
    private String activityName;
    @ApiModelProperty(value="活动说明", required = true)
    private String remark;
    /**
     * 组织机构ID
     */
    @ApiModelProperty(value="组织机构ID", required = true)
    private String orgId;
    /**
     * 活动开始日期
     */
    @ApiModelProperty(value="活动开始日期", required = true)
    private String activityStartDate;
    /**
     * 活动结束日期
     */
    @ApiModelProperty(value="活动结束日期", required = true)
    private String activityEndDate;
    /**
     * 参与周期
     */
    @ApiModelProperty(value="参与周期", required = true)
    private Integer period;
    /**
     * 参与次数限制
     */
    @ApiModelProperty(value="参与次数限制", required = true)
    private Integer countLimit;
    /**
     * 活动总发券次数限制
     */
    @ApiModelProperty(value="活动总发券次数限制", required = true)
    private Integer totalOfferLimit;
    /**
     * 背景图片
     */
    @ApiModelProperty(value="背景图片", required = true)
    private String imgUrl;
    /**
     * 活动链接地址
     */
    @ApiModelProperty(value="活动链接地址", required = true)
    private String activityUrl;
    /**
     * 需要传入优惠券模板 因form-data提交时包含文件，则上传list无法映射
     */
    @ApiModelProperty(value="需要传入优惠券模板 因form-data提交时包含文件，则上传list无法映射", required = true)
    private String couponModelsVal;
    /**
     * 不需要传入优惠券模板
     */
    @ApiModelProperty(value="不需要传入优惠券模板", required = true)
    private List<ThirdCouponModelDTO> couponModels;
    /**
     * 修改时 传入修改过的优惠券模板id 因form-data提交时包含文件，则上传list无法映射
     */
    @ApiModelProperty(value="修改时 传入修改过的优惠券模板id 因form-data提交时包含文件，则上传list无法映射", required = true)
    private String updateCouponSeqListVal;
    /**
     * 修改时 传入修改过的优惠券模板id 不需要传入
     */
    @ApiModelProperty(value="修改时 传入修改过的优惠券模板id 不需要传入", required = true)
    private List<Long> updateCouponSeqList;
    /**
     * 修改时 传入活动配置id
     */
    @ApiModelProperty(value="修改时 传入活动配置id", required = true)
    private Long thirdActivityId;
    /**
     * 修改时 传入活动id
     */
    @ApiModelProperty(value="修改时 传入活动id", required = true)
    private Long id;
    /**
     * 品牌名称
     */
    @ApiModelProperty(value="品牌名称", required = true)
    private String brandName;
    /**
     * 官网地址
     */
    @ApiModelProperty(value="官网地址", required = true)
    private String officialWebAddress;
    /**
     * 客服电话
     */
    @ApiModelProperty(value="客服电话", required = true)
    private String serviceTelephone;

    /** 活动渠道key */
    @ApiModelProperty(value="活动渠道key", required = true)
    private String activityChannelKey ;
    /** 活动渠道 */
    @ApiModelProperty(value="活动渠道", required = true)
    private String activityChannel;
    /** 活动规则*/
    @ApiModelProperty(value="活动规则", required = true)
    private String activityRules;


    public String getActivityName() {
        return activityName;
    }

    public void setActivityName(String activityName) {
        this.activityName = activityName;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public String getActivityStartDate() {
        return activityStartDate;
    }

    public void setActivityStartDate(String activityStartDate) {
        this.activityStartDate = activityStartDate;
    }

    public String getActivityEndDate() {
        return activityEndDate;
    }

    public void setActivityEndDate(String activityEndDate) {
        this.activityEndDate = activityEndDate;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public Integer getCountLimit() {
        return countLimit;
    }

    public void setCountLimit(Integer countLimit) {
        this.countLimit = countLimit;
    }

    public Integer getTotalOfferLimit() {
        return totalOfferLimit;
    }

    public void setTotalOfferLimit(Integer totalOfferLimit) {
        this.totalOfferLimit = totalOfferLimit;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getActivityUrl() {
        return activityUrl;
    }

    public void setActivityUrl(String activityUrl) {
        this.activityUrl = activityUrl;
    }

    public String getCouponModelsVal() {
        return couponModelsVal;
    }

    public void setCouponModelsVal(String couponModelsVal) {
        this.couponModelsVal = couponModelsVal;
    }

    public List<ThirdCouponModelDTO> getCouponModels() {
        return couponModels;
    }

    public void setCouponModels(List<ThirdCouponModelDTO> couponModels) {
        this.couponModels = couponModels;
    }

    public String getUpdateCouponSeqListVal() {
        return updateCouponSeqListVal;
    }

    public void setUpdateCouponSeqListVal(String updateCouponSeqListVal) {
        this.updateCouponSeqListVal = updateCouponSeqListVal;
    }

    public List<Long> getUpdateCouponSeqList() {
        return updateCouponSeqList;
    }

    public void setUpdateCouponSeqList(List<Long> updateCouponSeqList) {
        this.updateCouponSeqList = updateCouponSeqList;
    }

    public Long getThirdActivityId() {
        return thirdActivityId;
    }

    public void setThirdActivityId(Long thirdActivityId) {
        this.thirdActivityId = thirdActivityId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getOfficialWebAddress() {
        return officialWebAddress;
    }

    public void setOfficialWebAddress(String officialWebAddress) {
        this.officialWebAddress = officialWebAddress;
    }

    public String getServiceTelephone() {
        return serviceTelephone;
    }

    public void setServiceTelephone(String serviceTelephone) {
        this.serviceTelephone = serviceTelephone;
    }

    public String getActivityChannelKey() {
        return activityChannelKey;
    }

    public void setActivityChannelKey(String activityChannelKey) {
        this.activityChannelKey = activityChannelKey;
    }

    public String getActivityChannel() {
        return activityChannel;
    }

    public void setActivityChannel(String activityChannel) {
        this.activityChannel = activityChannel;
    }

    public String getActivityRules() {
        return activityRules;
    }

    public void setActivityRules(String activityRules) {
        this.activityRules = activityRules;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
}