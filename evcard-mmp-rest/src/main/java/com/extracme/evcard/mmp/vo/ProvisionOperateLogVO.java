package com.extracme.evcard.mmp.vo;

public class ProvisionOperateLogVO {
    /**
     * 条款ID
     */
    private Long provisionId;
    /**
     * 页码
     */
    private Integer pageNum = 1;
    /**
     * 一页显示条数
     */
    private Integer pageSize = 10;

    /**
     * 是否显示记录总条数（0：否；1是）
     */
    private Integer isAll = 0;

    public Long getProvisionId() {
        return provisionId;
    }

    public void setProvisionId(Long provisionId) {
        this.provisionId = provisionId;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getIsAll() {
        return isAll;
    }

    public void setIsAll(Integer isAll) {
        this.isAll = isAll;
    }

}
