package com.extracme.evcard.mmp.vo;

import java.util.List;

/**
 * 项目名称：evcard-mmp-rest
 * 类名称：ChannelManagementVO
 * 类描述：新增渠道VO
 * 创建人：沈建华
 * 创建时间：2018年1月17日上午10:20:15
 * 修改备注
 * @version1.0
 */
public class ChannelVO {
	/** evcard分配给其他平台的APP KEY */
    private String appKey;
    /** 平台名称 */
    private String platName;
    /** 组织机构id */
    private String orgId;    
    /** 备注 */
    private String remark;
    /**
     * 渠道所属平台ID
     */
    private Long platformId;
    /**
     * 渠道用途, 多个逗号分隔
     */
    private String channelPurpose;
    /**
     * 渠道状态
     */
    private Integer status;

    /**
     * 税务主体公司id
     */
    private String taxMainCompany;

    /**
     * 渠道类型 0：一级渠道  1：二级渠道
     */
    private int channelType = 0;

    /**
     * 一级渠道appkey
     * channelType 为1 时，这个值才有值
     */
    private String firstAppKey;

    public int getChannelType() {
        return channelType;
    }

    public void setChannelType(int channelType) {
        this.channelType = channelType;
    }

    public String getFirstAppKey() {
        return firstAppKey;
    }

    public void setFirstAppKey(String firstAppKey) {
        this.firstAppKey = firstAppKey;
    }

    public String getTaxMainCompany() {
        return taxMainCompany;
    }

    public void setTaxMainCompany(String taxMainCompany) {
        this.taxMainCompany = taxMainCompany;
    }

    /** evcard分配给其他平台的APP KEY */
    public String getAppKey(){ 
        return appKey;
    } 
    /** evcard分配给其他平台的APP KEY */
    public void setAppKey(String appKey){ 
        this.appKey = appKey;
    }
     /** 平台名称 */
    public String getPlatName(){ 
        return platName;
    } 
    /** 平台名称 */
    public void setPlatName(String platName){ 
        this.platName = platName;
    }
    /** 组织机构Id */
    public String getOrgId(){ 
        return orgId;
    }
    /** 组织机构Id */
    public void setOrgId(String orgId){ 
        this.orgId = orgId;
    }
    /** 备注 */
    public String getRemark(){ 
        return remark;
    }
    /** 备注 */
    public void setRemark(String remark){ 
        this.remark = remark;
    }
	public Long getPlatformId() {
		return platformId;
	}
	public void setPlatformId(Long platformId) {
		this.platformId = platformId;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}

    public String getChannelPurpose() {
        return channelPurpose;
    }

    public void setChannelPurpose(String channelPurpose) {
        this.channelPurpose = channelPurpose;
    }
}
