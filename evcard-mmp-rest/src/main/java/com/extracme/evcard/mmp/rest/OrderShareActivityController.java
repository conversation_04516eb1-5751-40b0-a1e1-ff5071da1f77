package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.bo.OrderShareCouponModelParamsBO;
import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.OrderShareActivityCouponModelDTO;
import com.extracme.evcard.mmp.dto.OrderShareActivityDetailDTO;
import com.extracme.evcard.mmp.dto.OrderShareActivityFullDTO;
import com.extracme.evcard.mmp.dto.OrderShareBrandCodeErrorMsgDTO;
import com.extracme.evcard.mmp.service.IOrderShareActivityService;
import com.extracme.evcard.mmp.vo.OrderShareActivityFullVO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2018/7/9
 * \* Time: 17:18
 * \* To change this template use File | Settings | File Templates.
 * \* Description: 订单分享活动发券
 * \
 */
@RestController
@RequestMapping("api/orderShareActivity")
public class OrderShareActivityController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IOrderShareActivityService orderShareActivityServiceImpl;


    private DefaultWebRespVO checkUploadImageFile(String fileName, DefaultWebRespVO vo) {
        String suffix = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
        // 允许上传的文件类型
        String suffixList = "jpg,png,jpeg";
        if (!suffixList.contains(suffix.trim().toLowerCase())) {
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("上传失败:图片限制格式是jpg,jpeg,png型");
            return vo;
        }
        return vo;
    }


    private DefaultWebRespVO checkUploadFile(String fileName, DefaultWebRespVO vo) {
        // 允许上传的文件类型
        String suffixList = "xls,xlsx";
        if (null != fileName) {
            String suffix = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
            if (!suffixList.contains(suffix.trim().toLowerCase())) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("文件上传失败:文件限制格式应该为xls,xlsx型！");
                return vo;
            }
        }
        return vo;
    }

    /**
     * 新增订单分享活动
     *
     * @param adPictureFile1
     * @param adPictureFile2
     * @param codeFile
     * @param orderShareActivityFullVO
     * @param request
     * @return
     */
    @RequestMapping(value = "addOrderShareActivity", method = RequestMethod.POST)
    public DefaultWebRespVO addOrderShareActivity(@RequestParam(value = "adPictureFile1", required = false) CommonsMultipartFile adPictureFile1,
                                                  @RequestParam(value = "adPictureFile2") CommonsMultipartFile adPictureFile2,
                                                  @RequestParam(value = "codeFile", required = false) CommonsMultipartFile codeFile,
                                                  @RequestParam(value = "coverPictureFile") CommonsMultipartFile coverPictureFile,
                                                  OrderShareActivityFullVO orderShareActivityFullVO,
                                                  HttpServletRequest request) {
        log.debug("新增订单分享活动信息...");
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            String adPictureName1 = StringUtils.EMPTY;
            InputStream adPictureFileInputStream1 = null;
            if (null != adPictureFile1) {
                adPictureName1 = adPictureFile1.getOriginalFilename();
                adPictureFileInputStream1 = adPictureFile1.getInputStream();
                checkUploadImageFile(adPictureName1, vo);
                if (vo.getCode().equals(Contants.RETURN_ERROR_CODE)) {
                    return vo;
                }
            }
            String adPictureName2 = adPictureFile2.getOriginalFilename();
            InputStream adPictureFileInputStream2 = adPictureFile2.getInputStream();
            checkUploadImageFile(adPictureName2, vo);
            if (vo.getCode().equals(Contants.RETURN_ERROR_CODE)) {
                return vo;
            }
            String codeFileName = StringUtils.EMPTY;
            InputStream codeFileInputStream = null;
            Boolean isCodeFile = false;
            if (null != codeFile) {
                codeFileName = codeFile.getOriginalFilename();
                codeFileInputStream = codeFile.getInputStream();
                checkUploadFile(codeFileName, vo);
                if (vo.getCode().equals(Contants.RETURN_ERROR_CODE)) {
                    return vo;
                }
                isCodeFile = true;
            }
            String coverPictureName = coverPictureFile.getOriginalFilename();
            InputStream coverPictureInputStream = coverPictureFile.getInputStream();
            checkUploadImageFile(coverPictureName, vo);
            if (vo.getCode().equals(Contants.RETURN_ERROR_CODE)) {
                return vo;
            }
            OrderShareActivityFullDTO orderShareActivityFullDTO = new OrderShareActivityFullDTO();
            BeanCopyUtils.copyProperties(orderShareActivityFullVO, orderShareActivityFullDTO);
            orderShareActivityFullDTO.setIsCodeFile(isCodeFile);
            DefaultServiceRespDTO respDTO = orderShareActivityServiceImpl.addOrderShareActivity(
                    adPictureFileInputStream1, adPictureName1, adPictureFileInputStream2, adPictureName2,
                    codeFileInputStream, codeFileName, coverPictureInputStream, coverPictureName,
                    orderShareActivityFullDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            } else if (respDTO.getCode() == -10) {
                return new DefaultWebRespVO("-10", respDTO.getMessage());
            }
            vo.setMessage(respDTO.getMessage());
            return vo;
        } catch (IOException e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("新增订单分享活动失败");
            return vo;
        }
    }


    /**
     * 修改订单分享活动
     *
     * @param orderShareActivityFullVO
     * @param request
     * @return
     */
    @RequestMapping(value = "updateOrderShareActivity", method = RequestMethod.POST)
    public DefaultWebRespVO updateOrderShareActivity(@RequestParam(value = "adPictureFile1", required = false) CommonsMultipartFile adPictureFile1,
                                                     @RequestParam(value = "adPictureFile2", required = false) CommonsMultipartFile adPictureFile2,
                                                     @RequestParam(value = "codeFile", required = false) CommonsMultipartFile codeFile,
                                                     @RequestParam(value = "coverPictureFile", required = false) CommonsMultipartFile coverPictureFile,
                                                     @RequestParam(value = "delFlag", defaultValue = "0") Integer delFlag,
                                                     OrderShareActivityFullVO orderShareActivityFullVO,
                                                     HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            String adPictureName1 = StringUtils.EMPTY;
            InputStream adPictureFileInputStream1 = null;
            if (null != adPictureFile1) {
                adPictureName1 = adPictureFile1.getOriginalFilename();
                adPictureFileInputStream1 = adPictureFile1.getInputStream();
                checkUploadImageFile(adPictureName1, vo);
                if (vo.getCode().equals(Contants.RETURN_ERROR_CODE)) {
                    return vo;
                }
            }
            String adPictureName2 = StringUtils.EMPTY;
            InputStream adPictureFileInputStream2 = null;
            if (null != adPictureFile2) {
                adPictureName2 = adPictureFile2.getOriginalFilename();
                adPictureFileInputStream2 = adPictureFile2.getInputStream();
                checkUploadImageFile(adPictureName2, vo);
                if (vo.getCode().equals(Contants.RETURN_ERROR_CODE)) {
                    return vo;
                }
            }
            String codeFileName = StringUtils.EMPTY;
            InputStream codeFileInputStream = null;
            Boolean isCodeFile = false;
            if (null != codeFile) {
                codeFileName = codeFile.getOriginalFilename();
                codeFileInputStream = codeFile.getInputStream();
                checkUploadFile(codeFileName, vo);
                if (vo.getCode().equals(Contants.RETURN_ERROR_CODE)) {
                    return vo;
                }
                isCodeFile = true;
            }
            String coverPictureName = StringUtils.EMPTY;
            InputStream coverPictureInputStream = null;
            if (null != coverPictureFile) {
                coverPictureName = coverPictureFile.getOriginalFilename();
                coverPictureInputStream = coverPictureFile.getInputStream();
                checkUploadImageFile(coverPictureName, vo);
                if (vo.getCode().equals(Contants.RETURN_ERROR_CODE)) {
                    return vo;
                }
            }
            OrderShareActivityFullDTO orderShareActivityFullDTO = new OrderShareActivityFullDTO();
            BeanCopyUtils.copyProperties(orderShareActivityFullVO, orderShareActivityFullDTO);
            orderShareActivityFullDTO.setIsCodeFile(isCodeFile);
            DefaultServiceRespDTO respDTO = orderShareActivityServiceImpl.updateOrderShareActivity(
                    adPictureFileInputStream1, adPictureName1, adPictureFileInputStream2, adPictureName2,
                    codeFileInputStream, codeFileName, coverPictureInputStream, coverPictureName, delFlag,
                    orderShareActivityFullDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            } else if (respDTO.getCode() == -10) {
                return new DefaultWebRespVO("-10", respDTO.getMessage());
            }
        } catch (IOException e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("修改订单分享活动失败");
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("修改订单分享活动...");
        return vo;
    }

    /**
     * 获取订单分享发券活动详情信息
     *
     * @param id 活动id
     * @return
     */
    @RequestMapping(value = "orderShareActivityDetail/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO orderShareActivityDetail(@PathVariable("id") Long id) {
        OrderShareActivityDetailDTO orderShareActivityDetail = orderShareActivityServiceImpl.orderShareActivityDetail(id);
        if (null == orderShareActivityDetail) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动信息不存在");
        }
        log.debug("获取订单分享发券活动详情信息...");
        return DefaultWebRespVO.getSuccessVO(orderShareActivityDetail);
    }

    /**
     * 获取订单分享发券活动模板列表
     *
     * @param paramsBO
     * @return
     */
    @RequestMapping(value = "getOrderShareCouponModelPage", method = RequestMethod.POST)
    public DefaultWebRespVO getOrderShareCouponModelPage(@RequestBody OrderShareCouponModelParamsBO paramsBO) {
        OrderShareActivityCouponModelDTO orderShareActivityCouponModelDTO =
                orderShareActivityServiceImpl.getOrderShareCouponModelPage(paramsBO);
        log.debug("获取订单分享发券活动模板列表...");
        return DefaultWebRespVO.getSuccessVO(orderShareActivityCouponModelDTO);
    }


    /**
     * 下载已导入的订单分享券码
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "exportBrandCouponCode", method = RequestMethod.GET)
    public DefaultWebRespVO exportBrandCouponCode(@RequestParam("id") long activityId,
                                                  HttpServletRequest request, HttpServletResponse response) {
        DefaultServiceRespDTO respDTO = orderShareActivityServiceImpl.exportBrandCouponCode(activityId, request, response);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "订单分享券码导出成功");
    }

    /**
     * 立即开始订单分享活动
     *
     * @param id      活动id
     * @param request
     * @return
     */
    @RequestMapping(value = "immediateStartOrderShareActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO immediateStartOrderShareActivity(@PathVariable("id") Long id,
                                                             HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = orderShareActivityServiceImpl.immediateStartOrderShareActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("立即开始订单分享活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已开始");
    }

    /**
     * 立即开始渠道注册奖励活动
     *
     * @param id      活动id
     * @param request
     * @return
     */
    @RequestMapping(value = "publish/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO publish(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = orderShareActivityServiceImpl.publish(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已发布");
    }


    /**
     * 停止订单分享活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "suspendOrderShareActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO suspendOrderShareActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        ComModel comModel = ComUtil.getUserInfo(request);
        String createOperName = comModel.getCreateOperName();
        Long createOperId = comModel.getCreateOperId();
        String operatorContent = "停止";
        DefaultServiceRespDTO respDTO = orderShareActivityServiceImpl.suspendOrderShareActivity(id, createOperName, createOperId, operatorContent);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("停止订单分享活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已停止");
    }


    /**
     * 删除订单分享活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @RequestMapping(value = "deleteOrderShareActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO deleteOrderShareActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = orderShareActivityServiceImpl.deleteOrderShareActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("删除订单分享活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已删除");
    }

    /**
     * 品牌兑换码错误记录导出
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "exportOrderShareBrandCodeErrorMsg", method = RequestMethod.GET)
    public DefaultWebRespVO exportOrderShareBrandCodeErrorMsg(HttpServletRequest request, HttpServletResponse response) {
        DefaultServiceRespDTO respDTO = orderShareActivityServiceImpl.exportOrderShareBrandCodeErrorMsg(request, response);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("品牌兑换码错误记录导出");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "品牌兑换码错误记录导出成功");
    }

    /**
     * 获取品牌兑换码错误记录列表
     *
     * @return
     */
    @RequestMapping(value = "getOrderShareBrandCodeErrorMsgList", method = RequestMethod.GET)
    public DefaultWebRespVO getOrderShareBrandCodeErrorMsgList(HttpServletRequest request, HttpServletResponse response) {
        List<OrderShareBrandCodeErrorMsgDTO> errorMsgDTOList =
                orderShareActivityServiceImpl.getOrderShareBrandCodeErrorMsgList(request, response);
        log.debug("获取品牌兑换码错误记录列表");
        return DefaultWebRespVO.getSuccessVO(errorMsgDTOList);
    }

}