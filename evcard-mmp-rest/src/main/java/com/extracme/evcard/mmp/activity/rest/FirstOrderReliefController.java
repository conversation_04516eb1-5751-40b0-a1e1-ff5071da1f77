package com.extracme.evcard.mmp.activity.rest;

import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.bo.ThirdCouponModelParamsBO;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.activity.ActivityCouponModelPageDTO;
import com.extracme.evcard.mmp.dto.activity.FirstOrderActivityDetailDTO;
import com.extracme.evcard.mmp.dto.activity.FirstOrderActivityFullDTO;
import com.extracme.evcard.mmp.service.IFirstOrderReliefService;
import com.extracme.evcard.mmp.service.IMarketActivityService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Api(value="firstOrderActivity", tags = "首单减免[14]")
@RestController
@RequestMapping("api/firstOrderActivity")
public class FirstOrderReliefController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    IFirstOrderReliefService firstOrderReliefService;

    @Resource
    protected IMarketActivityService marketActivityServiceImpl;

    /**
     * 活动新增
     *
     * @param firstOrderFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="活动创建", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "add", method = RequestMethod.POST)
    public DefaultWebRespVO add(@RequestBody FirstOrderActivityFullDTO firstOrderFullDTO,
                                HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = firstOrderReliefService.add(firstOrderFullDTO, request);
            if (respDTO.getCode() != 0) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            logger.error("新增活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "新增失败");
        }
        vo.setMessage("新增活动成功");
        return vo;
    }

    /**
     * 活动修改
     *
     * @param firstOrderFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="活动修改", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "update", method = RequestMethod.POST)
    DefaultWebRespVO update(@RequestBody FirstOrderActivityFullDTO firstOrderFullDTO,
                            HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = firstOrderReliefService.update(firstOrderFullDTO, request);
            if (respDTO.getCode() != 0) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            logger.error("更新活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "修改失败");
        }
        vo.setMessage("修改活动成功");
        return vo;
    }

    /**
     * 活动删除
     *
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动删除", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "delete/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO delete(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = firstOrderReliefService.delete(id, request);
            if (respDTO.getCode() != 0) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            logger.error("删除活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "删除失败");
        }
        vo.setMessage("删除活动成功");
        return vo;
    }

    /**
     * 活动暂停
     *
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动暂停", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "pause/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO pause(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = firstOrderReliefService.suspend(id, request);
            if (respDTO.getCode() != 0) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            logger.error("暂停活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "暂停失败");
        }
        vo.setMessage("暂停活动成功");
        return vo;
    }

    /**
     * 活动停止
     *
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动停止", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "stop/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO stop(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = firstOrderReliefService.stop(id, request);
            if (respDTO.getCode() != 0) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            logger.error("停止活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "停止失败");
        }
        vo.setMessage("停止活动成功");
        return vo;
    }

    /**
     * 活动开始
     *
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动开始", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "start/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO startFirstOrderRelief(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = firstOrderReliefService.start(id, request);
            if (respDTO.getCode() != 0) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            logger.error("启动活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "启动活动失败");
        }
        vo.setMessage("启动活动成功");
        return vo;
    }

    /**
     * 活动启动
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动发布", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "publish/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO publish(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = firstOrderReliefService.publish(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("发布活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "发布活动失败");
        }
        vo.setMessage("发布活动成功");
        return vo;
    }

    /**
     * 活动恢复
     *
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动恢复", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "resume/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO resume(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = firstOrderReliefService.resume(id, request);
            if (respDTO.getCode() != 0) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            logger.error("恢复活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "恢复失败");
        }
        vo.setMessage("恢复活动成功");
        return vo;
    }

    /**
     * 活动详情获取
     *
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动详情", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "query/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO queryFirstOrderRelief(@PathVariable("id") Long id,
                                                  HttpServletRequest request) {

        FirstOrderActivityDetailDTO activityDetailDTO = firstOrderReliefService.queryDetails(id);
        if (null == activityDetailDTO) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动信息不存在");
        }
        logger.debug("获取首单减免活动发券信息...");
        return DefaultWebRespVO.getSuccessVO(activityDetailDTO);
    }

    /**
     * 查询活动发放优惠券统计
     *
     * @param id
     * @return
     */
    @ApiOperation(value="活动发放优惠券统计", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "queryCouponStatistics/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO queryFirstOrderCouponStatistics(@PathVariable("id") Long id) {
        return marketActivityServiceImpl.queryFirstOrderCouponStatistics(id);
    }

    /**
     * 券模板列表
     *
     * @param paramsBO
     * @param request
     * @return
     */
    @ApiOperation(value="券模板列表", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "getCouponModelPage", method = RequestMethod.POST)
    public DefaultWebRespVO getCouponModelPage(@RequestBody ThirdCouponModelParamsBO paramsBO,
                                               HttpServletRequest request) {
        ActivityCouponModelPageDTO couponModelPage = firstOrderReliefService.getCouponModelPage(paramsBO);
        if (null == couponModelPage) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动券模板信息不存在");
        }
        logger.debug("获取活动发券信息...");
        return DefaultWebRespVO.getSuccessVO(couponModelPage);
    }
}
