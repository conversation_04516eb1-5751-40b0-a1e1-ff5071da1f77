package com.extracme.evcard.mmp.activity.rest;

import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.CouponBatchImportActivityDetailDTO;
import com.extracme.evcard.mmp.dto.CouponBatchImportDTO;
import com.extracme.evcard.mmp.service.IBatchImportActivityService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;

/**
 * 优惠券批量导入
 * 长模板活动接口 及 批量导入活动通用接口
 * <AUTHOR> @Discription
 * @date 2019/12/17
 */
@Api(value="batchImportActivity", tags = "长模板批量导入活动[11]")
@Slf4j
@RestController
@RequestMapping("api/batchImportActivity")
public class BatchImportActivityController {
    @Autowired
    private IBatchImportActivityService batchImportActivityService;

    /**
     * 活动新增
     * @param couponBatchImportDTO
     * @param request
     * @return
     */
    @ApiOperation(value="活动创建", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "add", method = RequestMethod.POST)
    public DefaultWebRespVO add(@RequestBody CouponBatchImportDTO couponBatchImportDTO,
                                HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = batchImportActivityService.add(couponBatchImportDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            log.error("新增活动失败...", e);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("新增活动失败");
            return vo;
        }
        vo.setMessage("提交成功");
        return vo;
    }


    /**
     * 活动修改
     * @param couponBatchImportDTO
     * @param request
     * @return
     */
    @ApiOperation(value="活动修改", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "update", method = RequestMethod.POST)
    public DefaultWebRespVO update(@RequestBody CouponBatchImportDTO couponBatchImportDTO,
                                   HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = batchImportActivityService.update(couponBatchImportDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            log.error("修改优惠券批量导入活动失败...，活动id=" + couponBatchImportDTO.getId(), e);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("修改活动失败");
            return vo;
        }
        vo.setMessage("提交成功");
        return vo;
    }

    /**
     * 活动删除
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动删除", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "delete/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO delete(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = batchImportActivityService.delete(id, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            log.error("删除优惠券批量导入活动失败...，活动id=" + id, e);
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("删除活动失败");
            return vo;
        }
        vo.setMessage("删除成功");
        return vo;
    }


    /**
     * 优惠券批量导入活动详情
     *
     * @param id
     *            活动id
     * @return
     */
    @ApiOperation(value="优惠券批量导入活动详情", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "detail/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO getDetail(@PathVariable("id") Long id) {
        CouponBatchImportActivityDetailDTO detailDTO = batchImportActivityService.getDetail(id);
        return DefaultWebRespVO.getSuccessVO(detailDTO);
    }

    /**
     * 开始导入优惠券
     * @param file
     * @param id
     * @param authority
     * @param request
     * @param response
     * @return
     */
    @ApiOperation(value="开始导入优惠券（文件）", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "优惠券文件", required = true, paramType = "query"),
            @ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "query"),
            @ApiImplicitParam(name = "authority", value = "权限", required = true, paramType = "query")
    })
    @RequestMapping(value = "import", method = RequestMethod.POST)
    public DefaultWebRespVO startCouponImport(@RequestParam(value = "file1", required = true) CommonsMultipartFile file,
                                                             @RequestParam(value = "id", required = true) Long id,
                                                             @RequestParam(value = "authority", defaultValue = "000" ) String authority, HttpServletRequest request,
                                                             HttpServletResponse response){
        DiskFileItem fi = (DiskFileItem) file.getFileItem();
        File file1 = fi.getStoreLocation();
        String fileName = file.getOriginalFilename();
        try {
            DefaultServiceRespDTO respDTO = batchImportActivityService.startCouponImport(id, file1, fileName,authority,
                    request, response);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
            if (respDTO.getCode() == 1){
                return new DefaultWebRespVO("1", respDTO.getMessage());
            }
            return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动结束");
        } catch (Exception e) {
            log.warn("导入优惠券失败, id=" + id, e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "发券失败");
        }
    }

    /**
     * 开始导入优惠券
     * @param file
     * @param id
     * @param request
     * @param response
     * @return
     */
    @ApiOperation(value="检查优惠券模板", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file1", value = "优惠券文件", required = true, paramType = "query"),
            @ApiImplicitParam(name = "templateFileName", value = "模板文件名称", required = true, paramType = "query"),
            @ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "query")
    })
    @RequestMapping(value = "checkTemplate", method = RequestMethod.POST)
    public DefaultWebRespVO checkTemplate(@RequestParam(value = "file1", required = false) CommonsMultipartFile file,
                                          @RequestParam(value = "templateFileName", required = false) String templateFileName,
                                          @RequestParam(value = "id", required = true) Long id,
                                          HttpServletRequest request,
                                          HttpServletResponse response) {
        File file1 = null;
        String fileName = StringUtils.EMPTY;
        try {
            if(file != null) {
                DiskFileItem fi = (DiskFileItem) file.getFileItem();
                file1 = fi.getStoreLocation();
                fileName = file.getOriginalFilename();
            }
            DefaultServiceRespDTO respDTO = batchImportActivityService.checkTemplate(id, file1, fileName, templateFileName, request, response);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
            if (respDTO.getCode() == 1){
                return new DefaultWebRespVO("1", respDTO.getMessage());
            }
            return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "检查通过");
        } catch (Exception e) {
            log.warn("检查优惠券模板失败, id=" + id, e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "检查优惠券模板失败");
        }
    }

    /**
     * 开始导入优惠券
     * @param id
     * @param request
     * @param response
     * @return
     */
    @ApiOperation(value="开始导入优惠券（地址）", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "query"),
            @ApiImplicitParam(name = "templateFileName", value = "优惠选模板名称", required = true, paramType = "query")
    })
    @RequestMapping(value = "importCoupons", method = RequestMethod.POST)
    public DefaultWebRespVO importCoupons(@RequestParam(value = "id", required = true) Long id,
                                          @RequestParam(value = "templateFileName", required = true) String templateFileName,
                                          HttpServletRequest request, HttpServletResponse response){
        try {
            DefaultServiceRespDTO respDTO = batchImportActivityService.importCoupons(id, templateFileName, request, response);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
            if (respDTO.getCode() == 1){
                return new DefaultWebRespVO("1", respDTO.getMessage());
            }
            return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "发券完成，活动结束");
        } catch (Exception e) {
            log.warn("导入优惠券失败, id=" + id, e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "发券失败");
        }
    }


    /**
     * 下载导入失败错误报告
     * @return
     */
    @ApiOperation(value="下载导入失败错误报告", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "query")
    })
    @RequestMapping(value = "downloadErrorTemplate", method = RequestMethod.GET)
    public DefaultWebRespVO downloadErrorTemplate(
            @RequestParam(value = "id", required = true) Long activityId,
            HttpServletRequest request, HttpServletResponse response){
        DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
        try {
            batchImportActivityService.exportImportErrorReport(activityId, request, response);
        }catch (Exception e){
            log.error("下载错误报告出错, activityId=" + activityId, e);
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("下载失败");
            return defaultWebRespVO;
        }
        return defaultWebRespVO;
    }

}
