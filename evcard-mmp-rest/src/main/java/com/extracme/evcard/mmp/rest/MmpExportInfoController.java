package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.bo.QueryExportInfoBO;
import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.MmpExportInfoDTO;
import com.extracme.evcard.mmp.dto.OperatorDTO;
import com.extracme.evcard.mmp.service.IMmpExportInfoService;
import com.extracme.evcard.mmp.service.IMmpExportService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.util.List;

/**
 * 项目名称：evcard-mmp-rest
 * 类名称：MmpExportInfoController
 * 类描述：会员导出业务接口控制层
 * 修改备注
 * @version1.0
 */
@RestController
@RequestMapping("api")
/**
 * 会员导出相关业务
 * TODO rest api重新定义
 */
public class MmpExportInfoController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    IMmpExportInfoService mmpExportInfoServiceImpl;

    /**
     * 获取导出文件列表
     * @param bo
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "queryAgencyExportInfo", method = RequestMethod.POST)
    //@RequestMapping(value = "exportFile/list", method = RequestMethod.POST)
    public DefaultWebRespVO queryExportInfoList(@RequestBody QueryExportInfoBO bo,
                                                  HttpServletRequest request , HttpServletResponse response){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            //获取登录账号所属组织机构
            ComModel comModel = ComUtil.getUserInfo(request);
            String curOrgId = comModel.getOrgId();
            bo.setCurrentOrgId(curOrgId);
            //查询
            PageBeanBO<MmpExportInfoDTO> pageBeanBO = mmpExportInfoServiceImpl.queryExportInfoList(bo);
            vo = DefaultWebRespVO.getSuccessVO(pageBeanBO);
        }
        catch (Exception ex) {
            logger.error("文件列表获取失败", ex);
            vo = new DefaultWebRespVO();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage("查询失败");
        }
        return vo;
    }


    /**
     * 文件下载
     * @param id
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "getEReportInfo", method = RequestMethod.GET)
    //@RequestMapping(value = "getExportFile", method = RequestMethod.GET)
    public DefaultServiceRespDTO getExportFile(@RequestParam(value = "Id", required = true) Long id,
                                               @RequestParam(value = "fileName", required = false) String fileName,
                                                HttpServletRequest request , HttpServletResponse response){
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        try {
            vo = mmpExportInfoServiceImpl.getExportFile(id, fileName , request, response);
        }catch (Exception e){
            logger.error("下载文件失败, file id=" + id, e);
            vo.setCode(-1);
            vo.setMessage("下载失败");
        }
        return vo;
    }


    /**
     * 文件批量下载
     * @param ids
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "getAllERamainReport" , method = RequestMethod.GET)
    //@RequestMapping(value = "batchGetFiles", method = RequestMethod.GET)
    public DefaultServiceRespDTO batchGetFiles(@RequestParam(value = "Id") List<Long> ids,
                                               @RequestParam(value = "fileName", required = false) String fileName,
                                               HttpServletRequest request,HttpServletResponse response){
        DefaultServiceRespDTO vo= new DefaultServiceRespDTO();
        //Timestamp timestamp =new Timestamp(date.getTime());
        try {
            vo = mmpExportInfoServiceImpl.batchGetFiles(ids, fileName, request, response);
        }catch (Exception e){
            logger.error("批量下载失败", e);
            vo.setCode(-1);
            vo.setMessage("下载失败");
        }
        return vo;
    }


    /**
     * 获取导出文件列表
     * @param bo
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "exportFile/list", method = RequestMethod.POST)
    public DefaultWebRespVO exportInfoList(@RequestBody QueryExportInfoBO bo,
                                                HttpServletRequest request , HttpServletResponse response){
        bo.setQueryByOrg(false);
        if(bo.getIgnoreStatus() == null) {
            //默认查询全部记录
            bo.setIgnoreStatus(1);
        }
        return queryExportInfoList(bo, request, response);
    }

    @RequestMapping(value = "exportMemberById", method = RequestMethod.POST)
    public DefaultWebRespVO exportMemberById(
            @RequestParam(value = "file") CommonsMultipartFile file, HttpServletRequest request) {
        DiskFileItem fi = (DiskFileItem) file.getFileItem();
        File file1 = fi.getStoreLocation();
        String fileName = file.getOriginalFilename();
        DefaultWebRespVO respVO = new DefaultWebRespVO();
        try {
            //操作人信息
            ComModel comModel = ComUtil.getUserInfo(request);
            OperatorDTO operatorDTO = new OperatorDTO();
            operatorDTO.setOperatorId(comModel.getCreateOperId());
            operatorDTO.setOperatorName(comModel.getCreateOperName());
            DefaultServiceRespDTO respDTO = mmpExportInfoServiceImpl.exportMemberById(file1, fileName, operatorDTO);
            respVO.setCode(String.valueOf(respDTO.getCode()));
            respVO.setMessage(respDTO.getMessage());
        } catch (Exception e) {
            logger.warn("添加会员id转换任务失败, fileName=" + fileName, e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "开始导出失败");
        }
        return respVO;
    }

    /**
     * 文件下载
     * @param id
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "exportFile/download", method = RequestMethod.GET)
    public DefaultServiceRespDTO download(@RequestParam(value = "id", required = true) Long id,
                                               @RequestParam(value = "fileName", required = false) String fileName,
                                               HttpServletRequest request , HttpServletResponse response){
        return getExportFile(id, fileName, request, response);
    }
}
