package com.extracme.evcard.mmp.rest.vipcard;

import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.service.vipcard.ICardActivityConfigService;
import com.extracme.evcard.rpc.vipcard.dto.*;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Discription
 * @date 2020/12/28
 */
@Slf4j
@RestController
@Api(value="vipcard", tags = "用户权益-售卡活动管理")
@RequestMapping("api/vipcard/activity")
public class CardActivityConfigController {
    @Resource
    ICardActivityConfigService cardActivityConfigService;

    /**
     * 活动新增
     * @param configDTO
     * @param request
     * @return
     */
    @RequestMapping(value = "add" ,method = RequestMethod.POST)
    public DefaultWebRespVO add(@RequestBody CardActivityConfigDto configDTO,
                                HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = cardActivityConfigService.add(configDTO, request);
            if (respDTO.getCode() != 0) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            log.error("新增活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "新增失败");
        }
        vo.setMessage("新增活动成功");
        return vo;
    }

    /**
     * 卡片更新
     * @param configDTO
     * @param request
     * @return
     */
    @RequestMapping(value = "update" ,method = RequestMethod.POST)
    DefaultWebRespVO update(@RequestBody CardActivityConfigDto configDTO,
                            HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = cardActivityConfigService.update(configDTO, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            log.error("更新活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "修改失败");
        }
        vo.setMessage("修改活动成功");
        return vo;
    }

    /**
     * 删除
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "delete/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO delete(@PathVariable("id") Long id,
                             HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = cardActivityConfigService.delete(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            log.error("删除活动异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "删除失败");
        }
        vo.setMessage("删除成功");
        return vo;
    }

    /**
     * 活动审核
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "publish/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO publish(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = cardActivityConfigService.publish(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            log.error("审核异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "审核失败");
        }
        vo.setMessage("审核成功");
        return vo;
    }

    @RequestMapping(value = "start/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO start(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = cardActivityConfigService.start(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            log.error("上架异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "上架失败");
        }
        vo.setMessage("上架成功");
        return vo;
    }

    @RequestMapping(value = "stop/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO stop(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = cardActivityConfigService.stop(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            log.error("下架异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "下架失败");
        }
        vo.setMessage("下架成功");
        return vo;
    }

    /**
     * 卡片详情获取
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "info/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO queryDetails(@PathVariable("id") Long id, HttpServletRequest request){
        CardActivityConfigDetailDto result = cardActivityConfigService.queryDetail(id);
        if (null == result) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动信息不存在");
        }
        return DefaultWebRespVO.getSuccessVO(result);
    }

    /**
     * 查询卡片配置列表
     * @param queryDto
     * @param request
     * @return
     */
    @RequestMapping(value = "list", method = RequestMethod.POST)
    public DefaultWebRespVO queryList(@RequestBody CardActivityQueryDto queryDto, HttpServletRequest request) {
        PageBeanBO<CardActivityListViewDto> pageBean = cardActivityConfigService.queryPage(queryDto);
        return DefaultWebRespVO.getSuccessVO(pageBean);
    }

    /**
     * 查询任务操作日志
     * @param id
     * @param pageNum
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "logs", method = RequestMethod.GET)
    public DefaultWebRespVO showLog(@RequestParam("id") Long id,
                                    @RequestParam(value = "pageNum", defaultValue = "1")Integer pageNum,
                                    @RequestParam(value = "pageSize", defaultValue = "10")Integer pageSize,
                                    @RequestParam(value = "isAll", defaultValue = "1")Integer isAll) {
        PageBeanBO<CardActivityConfigLogDto> pageBean = cardActivityConfigService.queryLogs(id, pageNum, pageSize, isAll);
        return DefaultWebRespVO.getSuccessVO(pageBean);
    }
}
