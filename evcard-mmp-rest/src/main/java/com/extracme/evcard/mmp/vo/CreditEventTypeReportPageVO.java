package com.extracme.evcard.mmp.vo;

/**
 * Created by Elin on 2017/11/22.
 * 事件类型报表分析显示字段 自定义页面形式显示
 */
public class CreditEventTypeReportPageVO {

    /**
     * 事件类型
     */
    private String eventName;

    /**
     * 1-12月数量
     */
    private Integer JanTotal;
    private Integer FebTotal;
    private Integer MarTotal;
    private Integer AprTotal;
    private Integer MayTotal;
    private Integer JuneTotal;
    private Integer JulyTotal;
    private Integer AugTotal;
    private Integer SeptTotal;
    private Integer OctTotal;
    private Integer NovTotal;
    private Integer DecTotal;

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public Integer getJanTotal() {
        return JanTotal;
    }

    public void setJanTotal(Integer janTotal) {
        JanTotal = janTotal;
    }

    public Integer getFebTotal() {
        return FebTotal;
    }

    public void setFebTotal(Integer febTotal) {
        FebTotal = febTotal;
    }

    public Integer getMarTotal() {
        return MarTotal;
    }

    public void setMarTotal(Integer marTotal) {
        MarTotal = marTotal;
    }

    public Integer getAprTotal() {
        return AprTotal;
    }

    public void setAprTotal(Integer aprTotal) {
        AprTotal = aprTotal;
    }

    public Integer getMayTotal() {
        return MayTotal;
    }

    public void setMayTotal(Integer mayTotal) {
        MayTotal = mayTotal;
    }

    public Integer getJuneTotal() {
        return JuneTotal;
    }

    public void setJuneTotal(Integer juneTotal) {
        JuneTotal = juneTotal;
    }

    public Integer getJulyTotal() {
        return JulyTotal;
    }

    public void setJulyTotal(Integer julyTotal) {
        JulyTotal = julyTotal;
    }

    public Integer getAugTotal() {
        return AugTotal;
    }

    public void setAugTotal(Integer augTotal) {
        AugTotal = augTotal;
    }

    public Integer getSeptTotal() {
        return SeptTotal;
    }

    public void setSeptTotal(Integer septTotal) {
        SeptTotal = septTotal;
    }

    public Integer getOctTotal() {
        return OctTotal;
    }

    public void setOctTotal(Integer octTotal) {
        OctTotal = octTotal;
    }

    public Integer getNovTotal() {
        return NovTotal;
    }

    public void setNovTotal(Integer novTotal) {
        NovTotal = novTotal;
    }

    public Integer getDecTotal() {
        return DecTotal;
    }

    public void setDecTotal(Integer decTotal) {
        DecTotal = decTotal;
    }
}