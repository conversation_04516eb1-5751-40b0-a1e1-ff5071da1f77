package com.extracme.evcard.mmp.activity.rest;

import com.extracme.evcard.activity.dto.MmpUserBonusReceiveRecordDTO;
import com.extracme.evcard.activity.dto.PageQueryDTO;
import com.extracme.evcard.activity.dto.UserBonusInfoViewDto;
import com.extracme.evcard.activity.dto.UserBonusReceiveRecordQueryDTO;
import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.service.IMmpUserBonusReceiveService;
import com.extracme.evcard.mmp.vo.BaseResultVo;
import com.extracme.evcard.mmp.vo.MmpUserBonusReceiveRecordVO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@Api(value="inviteBonus", tags = "邀请现金奖励")
@RestController
@RequestMapping("/api/mmpUserBonusReceive")
@Slf4j
public class MmpUserBonusReceiveController {

    @Resource
    private IMmpUserBonusReceiveService mmpUserBonusReceiveService;

    @ApiOperation(value="现金提现记录查询 ", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "pageNum",value = "分页查询的起始页",dataType = "int",required = true),
            @ApiImplicitParam(name = "pageSize",value = "分页查询每页展示的数量",dataType = "int",required = true)
    })
    @PostMapping("/queryPage")
    public BaseResultVo<PageBeanBO<MmpUserBonusReceiveRecordDTO>> queryPage(@RequestBody(required = false) MmpUserBonusReceiveRecordVO vo){
        if(ObjectUtils.isEmpty(vo)){
            vo =new MmpUserBonusReceiveRecordVO();
        }
        UserBonusReceiveRecordQueryDTO queryDto = new UserBonusReceiveRecordQueryDTO();
        BeanCopyUtils.copyProperties(vo, queryDto);
        PageQueryDTO pageQueryDTO = new PageQueryDTO();
        pageQueryDTO.setPageNum(vo.getPageNum());
        pageQueryDTO.setPageSize(vo.getPageSize());
        log.info("提交对象：" + vo +"\n提交分页:"+pageQueryDTO);
        BaseResultVo<PageBeanBO<MmpUserBonusReceiveRecordDTO>> result = new BaseResultVo<>();
        try {
            PageBeanBO<MmpUserBonusReceiveRecordDTO> pageBeanBO= mmpUserBonusReceiveService.pageQuery(queryDto, pageQueryDTO);
            result.setCode(Contants.RETURN_SUCCESS_CODE);
            result.setMessage("现金提现记录查询成功");
            result.setData(pageBeanBO);
        } catch (Exception e) {
            log.error("现金提现记录查询失败", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "现金提现记录查询失败");
        }
        return result;
    }

    @ApiOperation(value="提现记录详情", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "paymentSeq",value = "订单提现流水号",dataType = "String",required = true),
    })
    @GetMapping("/getReceiveRecordInfo/{paymentSeq}")
    public DefaultWebRespVO getReceiveRecordInfo(@PathVariable String paymentSeq){
        log.info("paymentSeq：" + paymentSeq);
        DefaultWebRespVO result = new DefaultWebRespVO();
        try {
            List<UserBonusInfoViewDto> list = mmpUserBonusReceiveService.getUserBonusList(paymentSeq);
            result.setCode(Contants.RETURN_SUCCESS_CODE);
            result.setMessage("提现查询成功");
            result.setData(list);
        } catch (Exception e) {
            log.error("提现查询异常", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "详情查询失败");
        }
        return result;
    }

    @ApiOperation(value="导出个税报表", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "applyStartTime", value = "申请提现时间开始",dataType = "String", paramType = "query", required = true),
            @ApiImplicitParam(name = "applyShutDownTime", value = "申请提现时间结束",dataType = "String",paramType = "query", required = true),
    })
    @GetMapping("/export")
    public DefaultWebRespVO export(@RequestParam(value = "applyStartTime", required = false)String applyStartTime,
                                   @RequestParam(value = "applyShutDownTime", required = false)String applyShutDownTime,
                                   HttpServletRequest request, HttpServletResponse response) {
        DefaultServiceRespDTO respDTO = mmpUserBonusReceiveService.export(applyStartTime, applyShutDownTime,
                request,response);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("导出提现信息...");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "会员提现记录导出成功");
    }

    @ApiOperation(value="补发奖励", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "id",value = "提现记录的id",dataType = "String",required = true),
    })
    @GetMapping("/manualSendReward/{id}")
    public DefaultWebRespVO manualSendReward(@PathVariable Long id,HttpServletRequest request){
        log.info("controller层manualSendReward id={}",id);
        try {
            DefaultServiceRespDTO defaultServiceRespDTO = mmpUserBonusReceiveService.manualSendReward(id, request);
            return new DefaultWebRespVO(defaultServiceRespDTO.getCode().toString(), defaultServiceRespDTO.getMessage(), defaultServiceRespDTO.getData());
        }catch (Exception e){
            log.error("补发奖励失败", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "补发奖励失败",id);
        }
    }
}
