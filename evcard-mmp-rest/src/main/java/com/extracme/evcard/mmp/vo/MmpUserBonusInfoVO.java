package com.extracme.evcard.mmp.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="MmpUserBonusInfoVO对象")
public class MmpUserBonusInfoVO implements Serializable {

    //当前页
    @ApiModelProperty(value = "当前页")
    private Integer pageNum = 1;

    //页面数量
    @ApiModelProperty(value = "页面数量")
    private Integer pageSize = 10;

    //邀请人的用户编号
    @ApiModelProperty(value = "邀请人的用户编号")
    private Long originId;

    //邀请人的会员ID
    @ApiModelProperty(value = "邀请人的会员ID")
    private String originAuthId;

    //邀请人所属公司id
    @ApiModelProperty(value = "邀请人所属公司id")
    private String originOrgId;

    //邀请人的公司的名字
    @ApiModelProperty(value = "邀请人的公司的名字")
    private String originOrgName;

    //邀请人的姓名
    @ApiModelProperty(value = "邀请人的姓名")
    private String originUserName;

    //邀请人的手机号
    @ApiModelProperty(value = "邀请人的手机号")
    private String originMobilePhone;

    //被邀请人的用户编号
    @ApiModelProperty(value = "被邀请人的用户编号")
    private Long id;

    //被邀请人的会员ID
    @ApiModelProperty(value = "被邀请人的会员ID")
    private String authId;

    //被邀请人的姓名
    @ApiModelProperty(value = "被邀请人的姓名")
    private String userName;

    //被邀请人的手机号
    @ApiModelProperty(value = "被邀请人的手机号")
    private String mobilePhone;

    //被邀请人所属公司id
    @ApiModelProperty(value = "被邀请人所属公司id")
    private String orgId;

    //被邀请人邀请人的公司的名字
    @ApiModelProperty(value = "被邀请人邀请人的公司的名字")
    private String orgName;

    //订单编号
    @ApiModelProperty(value = "订单编号")
    private String orderSeq;

    //订单支付金额
    @ApiModelProperty(value = "订单支付金额")
    private BigDecimal orderRealAmount;

    //订单支付日期
    @ApiModelProperty(value = "订单支付日期")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date orderPayTime;

    //订单支付开始日期
    @ApiModelProperty(value = "订单支付开始日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date orderPayStartTime;

    //订单支付结束日期
    @ApiModelProperty(value = "订单支付结束日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date orderPayShutDownTime;

    //奖励结算金额
    @ApiModelProperty(value = "奖励结算金额")
    private BigDecimal settleAmount;

    //个人所得税
    @ApiModelProperty(value = "个人所得税")
    private BigDecimal taxAmount;

    //实际奖励金额
    @ApiModelProperty(value = "实际奖励金额")
    private BigDecimal amountPaid;

    //奖励结算时间
    @ApiModelProperty(value = "奖励结算时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date settleTime;

    //奖励结算开始时间
    @ApiModelProperty(value = "奖励结算开始时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date settleStartTime;

    //奖励结算结束时间
    @ApiModelProperty(value = "奖励结算结束时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date settleShutDownTime;

    //审核日期
    @ApiModelProperty(value = "审核日期")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date reviewTime;

    //审核开始日期
    @ApiModelProperty(value = "审核开始日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date reviewStartTime;
    //审核结束日期
    @ApiModelProperty(value = "审核结束日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date reviewShutDownTime;


    //提现日期
    @ApiModelProperty(value = "提现日期")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date applyTime;

    //提现开始日期
    @ApiModelProperty(value = "提现开始日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date applyStartTime;
    //提现结束日期
    @ApiModelProperty(value = "提现结束日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date applyShutDownTime;

    //提现方式
    @ApiModelProperty(value = "提现方式")
    private String sendMode;

    //用户领取红包的日期
    @ApiModelProperty(value = "用户领取红包的日期")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date receiveTime;

    //邀请时间
    @ApiModelProperty(value = "邀请时间")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String shareTime;

    //邀请开始时间
    @ApiModelProperty(value = "邀请开始时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date shareStartTime;
    //邀请结束时间
    @ApiModelProperty(value = "邀请结束时间")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone = "GMT+8")
    private Date shareShutDownTime;

    //状态
    @ApiModelProperty(value = "奖励状态，0待结算 1已结算(待审核)  2结算失败 3审核通过，4审核拒绝，5提现中，6已提现，7提现失败 8已领取 9已退回")
    private Integer rewardStatus;

    //审核的id
    @ApiModelProperty(value = "审核id")
    private Long auditId;

    //原因
    @ApiModelProperty(value = "原因")
    private String miscDesc;

}