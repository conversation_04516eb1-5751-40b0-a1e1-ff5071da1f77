package com.extracme.evcard.mmp.consts;


import com.extracme.evcard.mmp.common.SpringContextUtil;

import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.springframework.core.env.Environment;

public class WarnConsts {

    //未知异常
    public static final DefaultWebRespVO UNKNOWN_ERROR;

    //未知运行时异常
    public static final DefaultWebRespVO UNKNOWN_RUNTIME_ERROR;

    //参数不满足要求
    public static final DefaultWebRespVO PARAM_ERROR;
    
    //上传文件数量有误
    public static final DefaultWebRespVO UPLOAD_FILE_NUM_ERROR;
    
    //角色权限不足
    public static final DefaultWebRespVO USER_ROLE_LACK_AUTHORITY;
    
    //没有改权限
    public static final DefaultWebRespVO NO_AUTHORITY;
    
    //传入值为空
    public static final DefaultWebRespVO NO_VALUE;

    static {
        Environment environment = SpringContextUtil.getBean(Environment.class);

        UNKNOWN_ERROR = getDefaultWebRespVO("00000000", environment);
        UNKNOWN_RUNTIME_ERROR = getDefaultWebRespVO("00000001", environment);
        PARAM_ERROR = getDefaultWebRespVO("00000003", environment);
        UPLOAD_FILE_NUM_ERROR = getDefaultWebRespVO("10020003", environment);
        USER_ROLE_LACK_AUTHORITY = getDefaultWebRespVO("10020006", environment);
        NO_AUTHORITY = getDefaultWebRespVO("00000006", environment);
        NO_VALUE = getDefaultWebRespVO("10070001", environment);
    }

    public static DefaultWebRespVO getDefaultWebRespVO(String code, Environment environment) {
        return new DefaultWebRespVO(code, environment.getProperty(code));
    }

}
