package com.extracme.evcard.mmp.rest;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.ActivityNameDTO;
import com.extracme.evcard.mmp.dto.MmpQrCodeManagementDTO;
import com.extracme.evcard.mmp.dto.MmpQrCodeUrlHistoryDTO;
import com.extracme.evcard.mmp.dto.NightCarInfoDTO;
import com.extracme.evcard.mmp.service.IQRCodeManagementService;
import com.extracme.evcard.mmp.vo.MmpQrCodeManagementVO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;

/**
 * <AUTHOR> 二维码管理
 */
@RestController
@RequestMapping("api/qrcode")
public class QRCodeManagementController {
	private final Logger log = LoggerFactory.getLogger(this.getClass());

	@Resource
	private IQRCodeManagementService qrCodeManagementServiceImpl;

	private DefaultWebRespVO checkUploadImageFile(String fileName, DefaultWebRespVO vo) {
		String suffix = fileName.substring(fileName.lastIndexOf(".") + 1, fileName.length());
		// 允许上传的文件类型
		String suffixList = "jpg,png,jpeg";
		if (!suffixList.contains(suffix.trim().toLowerCase())) {
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("上传失败:图片限制格式是jpg,jpeg,png型");
			return vo;
		}
		return vo;
	}

	/**
	 * 新增二维码
	 * 
	 * @param qrIcon
	 *            二维码图标
	 * @param mmpQrCodeManagementVO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "addQRCode", method = RequestMethod.POST)
	public DefaultWebRespVO addQRCode(@RequestParam(value = "qrIcon", required = false) CommonsMultipartFile qrIcon,
			MmpQrCodeManagementVO mmpQrCodeManagementVO, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			String icon = StringUtils.EMPTY;
			InputStream iconInputStream = null;
			if (null != qrIcon) {
				icon = qrIcon.getOriginalFilename();
				iconInputStream = qrIcon.getInputStream();
				checkUploadImageFile(icon, vo);
				if (vo.getCode().equals(Contants.RETURN_ERROR_CODE)) {
					return vo;
				}
			}
			MmpQrCodeManagementDTO mmpQrCodeManagementDTO = new MmpQrCodeManagementDTO();
			BeanCopyUtils.copyProperties(mmpQrCodeManagementVO, mmpQrCodeManagementDTO);
			DefaultServiceRespDTO respDTO = qrCodeManagementServiceImpl.addQRCode(iconInputStream, icon,
					mmpQrCodeManagementDTO, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (IOException e) {
			e.printStackTrace();
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("新增二维码失败");
			return vo;
		}
		vo.setMessage("提交成功");
		log.debug("新增二维码信息...");
		return vo;
	}

	/**
	 * 修改二维码信息
	 * 
	 * @param mmpQrCodeManagementVO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "updateQRCode", method = RequestMethod.POST)
	public DefaultWebRespVO updateQRCode(MmpQrCodeManagementVO mmpQrCodeManagementVO, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		MmpQrCodeManagementDTO mmpQrCodeManagementDTO = new MmpQrCodeManagementDTO();
		BeanCopyUtils.copyProperties(mmpQrCodeManagementVO, mmpQrCodeManagementDTO);
		DefaultServiceRespDTO respDTO = qrCodeManagementServiceImpl.updateQRCode(mmpQrCodeManagementDTO, request);
		if (respDTO.getCode() == -1) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		vo.setMessage("提交成功");
		log.debug("修改二维码信息...");
		return vo;
	}

	/**
	 * 获取二维码详情
	 * 
	 * @param id
	 *            二维码ID
	 * @return
	 */
	@RequestMapping(value = "getQRCode/{id}", method = RequestMethod.GET)
	public DefaultWebRespVO getQRCode(@PathVariable("id") Long id) {
		MmpQrCodeManagementDTO qrCodeDeatil = qrCodeManagementServiceImpl.getQRCode(id);
		if (null == qrCodeDeatil) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "二维码信息不存在");
		}
		log.debug("获取二维码详情信息...");
		return DefaultWebRespVO.getSuccessVO(qrCodeDeatil);
	}


	/**
	 * 获取二维码列表
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param isAll
	 * @param qrName
	 *            二维码名称
	 * @param id
	 *            二维码ID
	 * @param orgId
	 *            运营公司
	 * @param qrType
	 *            二维码类型
	 * @return
	 */
	@RequestMapping(value = "queryQRCodeList", method = RequestMethod.GET)
	public DefaultWebRespVO queryQRCodeList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
			@RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
			@RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
			@RequestParam(value = "qrName", required = false) String qrName,
			@RequestParam(value = "id", required = false) Long id,
			@RequestParam(value = "orgId", required = false) String orgId,
			@RequestParam(value = "qrType", required = false) Integer qrType) {
		PageBeanBO<MmpQrCodeManagementDTO> pageBeanBO = qrCodeManagementServiceImpl.queryQRCodeList(pageNum, pageSize,
				isAll, qrName, id, orgId, qrType);
		log.debug("获取二维码列表...");
		return DefaultWebRespVO.getSuccessVO(pageBeanBO);
	}

	/**
	 * 删除二维码
	 * 
	 * @param id
	 *            二维码ID
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "deleteQRCode/{id}", method = RequestMethod.PUT)
	public DefaultWebRespVO deleteQRCode(@PathVariable("id") Long id, HttpServletRequest request) {
		DefaultServiceRespDTO respDTO = qrCodeManagementServiceImpl.deleteQRCode(id, request);
		if (respDTO.getCode() == -1) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("删除二维码");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "二维码已删除");
	}

	/**
	 * 生成二维码
	 * 
	 * @param id
	 *            二维码ID
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "createQRCode/{id}", method = RequestMethod.PUT)
	public DefaultWebRespVO createQRCode(@PathVariable("id") Long id, HttpServletRequest request) {
		try {
			DefaultServiceRespDTO respDTO = qrCodeManagementServiceImpl.createQRCode(id, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (Exception e) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, e.getMessage());
		}
		log.debug("生成二维码");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "二维码已生成");
	}

	/**
	 * 变更二维码指向
	 * 
	 * @param mmpQrCodeManagementVO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "updateQRCodeUrl", method = RequestMethod.PUT)
	public DefaultWebRespVO updateQRCodeUrl(@RequestBody MmpQrCodeManagementVO mmpQrCodeManagementVO,
			HttpServletRequest request) {
		MmpQrCodeManagementDTO mmpQrCodeManagementDTO = new MmpQrCodeManagementDTO();
		BeanCopyUtils.copyProperties(mmpQrCodeManagementVO, mmpQrCodeManagementDTO);
		DefaultServiceRespDTO respDTO = qrCodeManagementServiceImpl.updateQRCodeUrl(mmpQrCodeManagementDTO, request);
		if (respDTO.getCode() == -1) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
		}
		log.debug("变更二维码指向");
		return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "变更二维码指向");
	}

	/**
	 * 获取二维码变更历史
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param isAll
	 * @param qrId
	 *            二维码ID
	 * @return
	 */
	@RequestMapping(value = "queryQrCodeUrlHistory", method = RequestMethod.GET)
	public DefaultWebRespVO queryQrCodeUrlHistory(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
			@RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
			@RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
			@RequestParam(value = "qrId", required = false) Long qrId) {
		PageBeanBO<MmpQrCodeUrlHistoryDTO> pageBeanBO = qrCodeManagementServiceImpl.queryQrCodeUrlHistory(pageNum,
				pageSize, isAll, qrId);
		log.debug("获取二维码变更历史...");
		return DefaultWebRespVO.getSuccessVO(pageBeanBO);
	}

	/**
	 * 系统活动列表
	 * 
	 * @param pageNum
	 * @param pageSize
	 * @param isAll
	 * @param name
	 * @param id
	 * @param activityStatus
	 * @param orgId
	 * @param type
	 * @return
	 */
	@RequestMapping(value = "getActivityInfoList", method = RequestMethod.GET)
	public DefaultWebRespVO getActivityInfoList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
			@RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
			@RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
			@RequestParam(value = "name", required = false) String name,
			@RequestParam(value = "id", required = false) String id,
			@RequestParam(value = "activityStatus", required = false) String activityStatus,
			@RequestParam(value = "orgId", required = false) String orgId,
			@RequestParam(value = "type", required = false) Integer type) {
		PageBeanBO<NightCarInfoDTO> pageBeanBO = qrCodeManagementServiceImpl.getActivityInfoList(pageNum, pageSize,
				isAll, name, id, activityStatus, orgId, type);
		log.debug("活动列表...");
		return DefaultWebRespVO.getSuccessVO(pageBeanBO);

	}

	/**
	 * 获取活动名称
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getActivityNameList", method = RequestMethod.GET)
	public DefaultWebRespVO getActivityNameList(HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			List<ActivityNameDTO> activityNameList = qrCodeManagementServiceImpl.getActivityNameList(request);
			return DefaultWebRespVO.getSuccessVO(activityNameList);
		} catch (Exception e) {
			log.error(ComUtil.getExceptionMsg(e));
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage(e.toString());
		}
		log.debug("获取活动名称");
		return vo;
	}

}
