package com.extracme.evcard.mmp.config;

import com.extracme.evcard.sso.client.SsoFilter;
import com.extracme.evcard.sso.client.entity.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.LegacyCookieProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;


/**
 * 动态修改Constants中oss参数（使用ssoConfig中加载的properties）
 * 由于Constants类初始化是默认读取本地sso-client,properties文件
 * 故需保留此文件。
 *
 * 可考虑升级此sso.client包->不再读取本地resource文件
 */

@Configuration
@Slf4j
public class SsoFilterConfig {
    @Autowired
    private SsoConfig ssoConfig;

    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> cookieProcessorCustomizer() {
        return (factory) -> factory.addContextCustomizers(
                (context) -> context.setCookieProcessor(new LegacyCookieProcessor()));
    }

    @Bean
    public FilterRegistrationBean initSsoFilter() throws NoSuchFieldException, IllegalAccessException {
        // 注册SsoFilter
        log.info("-=-=-=-initSsoFilter=-=-=");

        refreshSsoProp();

        FilterRegistrationBean<SsoFilter> filterRegistrationBean = new FilterRegistrationBean<>();
        filterRegistrationBean.setFilter(new SsoFilter());
        filterRegistrationBean.addUrlPatterns("/*");
        filterRegistrationBean.addInitParameter("excludedPages",ssoConfig.getExcludedPages());
        filterRegistrationBean.addInitParameter("excludedKeys",ssoConfig.getExcludedKeys());
        filterRegistrationBean.setName("ssoFilter");

        return filterRegistrationBean;
    }

    private void refreshSsoProp() throws NoSuchFieldException, IllegalAccessException {
        Constants p = new Constants();
        log.info("start->{}",Constants.SSO_ADDR);
        Field ssoAddrField = p.getClass().getDeclaredField("SSO_ADDR");
        ssoAddrField.setAccessible(true);


        Field ssoAddrModifiers = ssoAddrField.getClass().getDeclaredField("modifiers");
        ssoAddrModifiers.setAccessible(true);
        ssoAddrModifiers.setInt(ssoAddrField, ssoAddrField.getModifiers() & ~Modifier.FINAL);

        ssoAddrField.set(p, ssoConfig.getSsoAddr());
        ssoAddrModifiers.setInt(ssoAddrField, ssoAddrField.getModifiers() & ~Modifier.FINAL);
        log.info("end->{}",Constants.SSO_ADDR);

        Field domainNameField = p.getClass().getDeclaredField("DOMAIN_NAME");
        domainNameField.setAccessible(true);


        Field domainNameModifiers = domainNameField.getClass().getDeclaredField("modifiers");
        domainNameModifiers.setAccessible(true);
        domainNameModifiers.setInt(domainNameField, domainNameField.getModifiers() & ~Modifier.FINAL);

        domainNameField.set(p, ssoConfig.getSsoDomainName());
        domainNameModifiers.setInt(domainNameField, domainNameField.getModifiers() & ~Modifier.FINAL);


        Field innerHostField = p.getClass().getDeclaredField("SSO_INNER_HOST");
        innerHostField.setAccessible(true);


        Field innerHostModifiers = innerHostField.getClass().getDeclaredField("modifiers");
        innerHostModifiers.setAccessible(true);
        innerHostModifiers.setInt(innerHostField, innerHostField.getModifiers() & ~Modifier.FINAL);

        innerHostField.set(p, ssoConfig.getSsoInnerHost());
        innerHostModifiers.setInt(innerHostField, innerHostField.getModifiers() & ~Modifier.FINAL);
    }

}
