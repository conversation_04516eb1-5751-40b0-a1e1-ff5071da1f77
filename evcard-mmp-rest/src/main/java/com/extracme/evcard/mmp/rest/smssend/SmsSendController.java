package com.extracme.evcard.mmp.rest.smssend;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.bo.sms.AddSmsSendBO;
import com.extracme.evcard.mmp.bo.sms.ModifySmsSendBo;
import com.extracme.evcard.mmp.bo.sms.QuerySmsSendBO;
import com.extracme.evcard.mmp.bo.sms.SmsIdBo;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.sms.QuerySmsSendDTO;
import com.extracme.evcard.mmp.dto.sms.QuerySmsSendListDTO;
import com.extracme.evcard.mmp.service.sms.ISmsSendService;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2023/4/13 14:50
 * * 类描述：短信发送控制层
 */

@RestController
@RequestMapping("api/actSmsSendAudit")
public class SmsSendController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private ISmsSendService smsSendServiceImpl;

    /**
     * 人数查询
     */
    @RequestMapping(value = "queryImportCount", method = RequestMethod.POST)
    public DefaultWebRespVO queryCount(@RequestParam(value = "dataSource", required = true) int dataSource,
                                       @RequestParam(value = "file", required = false) CommonsMultipartFile file,
                                       @RequestParam(value = "textContent", required = false) String textContent) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        try {
            vo = smsSendServiceImpl.queryCount(dataSource, file, textContent);
        } catch (Exception e) {
            DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("人数查询失败！");
            return defaultWebRespVO;
        }
        if (!Contants.CODE_SUCCESS.equals(vo.getCode())) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, vo.getMessage());
        }

        return DefaultWebRespVO.getSuccessVO(vo.getData());
    }

    /**
     * 新增短信发送
     */
    @RequestMapping(value = "add", method = RequestMethod.POST)
    public DefaultWebRespVO addSmsSend(AddSmsSendBO addSmsSendBo, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        try {
            vo = smsSendServiceImpl.addSmsSend(addSmsSendBo, request);
        } catch (Exception e) {
            DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("新增短信发放失败！");
            return defaultWebRespVO;
        }
        if (!Contants.CODE_SUCCESS.equals(vo.getCode())) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, vo.getMessage());
        }
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "提交成功");
    }

    /**
     * 修改短信发送
     */
    @RequestMapping(value = "modify", method = RequestMethod.POST)
    public DefaultWebRespVO modifySmsSend(ModifySmsSendBo modifySmsSendBo, HttpServletRequest request) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        try {
            vo = smsSendServiceImpl.modifySmsSendDetail(modifySmsSendBo, request);
        } catch (Exception e) {
            DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("修改短信发放失败！");
            return defaultWebRespVO;
        }
        if (!Contants.CODE_SUCCESS.equals(vo.getCode())) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, vo.getMessage());
        }
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "修改成功");
    }


    /**
     * 查询短信列表
     */
    @RequestMapping(value = "queryList", method = RequestMethod.POST)
    public DefaultWebRespVO querySmsSendList(@RequestBody QuerySmsSendBO querySmsSendBO) {
        DefaultWebRespVO vo = new DefaultWebRespVO();

        // 校验必填参数
        if (querySmsSendBO.getPageNum() <= 0) {
            vo.setCode("-1");
            vo.setMessage("请送正确的pageNum");
            log.info("查询短信发放列表，应答参数：{}", JSON.toJSONString(vo));
            return vo;
        }
        if (querySmsSendBO.getPageSize() <= 0) {
            vo.setCode("-1");
            vo.setMessage("请送正确的pageSize");
            log.info("查询短信发放列表，应答参数：{}", JSON.toJSONString(vo));
            return vo;
        }

        QuerySmsSendListDTO listDTO = new QuerySmsSendListDTO();
        try {
            listDTO = smsSendServiceImpl.querySmsSendList(querySmsSendBO);
        } catch (Exception e) {
            log.debug("查询异常" + e);
            DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("查询短信发放列表失败！");
            return defaultWebRespVO;
        }
        return DefaultWebRespVO.getSuccessVO(listDTO);
    }

    /**
     * 查询短信详情
     *
     * @param smsIdBo
     * @return
     */
    @RequestMapping(value = "/queryDetail", method = RequestMethod.POST)
    public DefaultWebRespVO querySmsSendDetail(@RequestBody SmsIdBo smsIdBo) {
        QuerySmsSendDTO querySmsSendDTO = new QuerySmsSendDTO();

        if(smsIdBo.getId() < 0){
            DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("请送正确的id！");
            return defaultWebRespVO;
        }

        try {
            querySmsSendDTO = smsSendServiceImpl.querySmsSendDetail(smsIdBo.getId());
        } catch (Exception e) {
            DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("查询短信发放详情失败！");
            return defaultWebRespVO;
        }

        return DefaultWebRespVO.getSuccessVO(querySmsSendDTO);
    }

    /**
     * 取消短信
     *
     * @param smsIdBo
     * @return
     */
    @RequestMapping(value = "/cancel", method = RequestMethod.POST)
    public DefaultWebRespVO cancelSmsSend(@RequestBody SmsIdBo smsIdBo, HttpServletRequest request) {

        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();

        if(smsIdBo.getId() < 0){
            DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("请送正确的id！");
            return defaultWebRespVO;
        }

        try {
            vo = smsSendServiceImpl.cancelSmsSendDetail(smsIdBo.getId(), request);
        } catch (Exception e) {
            DefaultWebRespVO defaultWebRespVO = new DefaultWebRespVO();
            defaultWebRespVO.setCode("-1");
            defaultWebRespVO.setMessage("查询短信发放详情失败！");
            return defaultWebRespVO;
        }

        if (!Contants.CODE_SUCCESS.equals(vo.getCode())) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, vo.getMessage());
        }
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "取消成功");
    }
}
