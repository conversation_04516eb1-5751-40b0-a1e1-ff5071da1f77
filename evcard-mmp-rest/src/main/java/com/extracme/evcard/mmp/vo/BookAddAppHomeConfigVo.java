package com.extracme.evcard.mmp.vo;

import com.extracme.evcard.mmp.dto.CityDTO;
import com.extracme.evcard.mmp.dto.activity.TopCity;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @ClassName: BookAddAppHomeConfigVo
 * @Author: wudi
 * @Date: 2021/4/23 10:55
 */
@Data
public class BookAddAppHomeConfigVo {

    private Long id;

    /**
     * 活动名称
     */
    @NotBlank(message = "请输入活动名称")
    private String activityName;

    /**
     * 开始时间
     *  格式：yyyy-MM-dd HH:mm:ss
     */
    @NotBlank(message = "请选择生效开始时间")
    private String startTime;

    /**
     * 结束时间
     *  格式：yyyy-MM-dd HH:mm:ss
     */
    @NotBlank(message = "请选择生效结束时间")
    private String endTime;

    /**
     * 链接方式 0：外部链接 1：内部页面
     */
    private Integer linkType;

    /**
     * 内部页面类型
     * 未登录:下拉选项：1邀请好友 2注册登录页;
     * 已登录:下拉选项：3会员任务中心 4车型宝典 5积分商城 6邀请好友 7充值E币;
     *  * 8每日签到 9：特惠购卡 10短租页面
     */
    private Integer internalPageType;

    /**
     * APP/享道URL链接
     */
    private String appUrl;

    /**
     * 首页banner图片
     */
  /*  @NotBlank(message = "请上传未登录时显示图片")
    private String homeBannerPic;*/

    /**
     * 首页banner图片
     */
    @NotBlank(message = "请上传未登录时显示图片(兼容版本)")
    private String homeBannerPicCompatible;

    private Integer adIcon;


    /**
     *  首页banner图片排序字段， 0-9，数字越小显示越靠前
     */
    private Integer homeBannerPicSort;

    /**
     * 展示平台
     * 0:EVCARD 1:享道APP 2:H5 3:支付宝小程序 4:微信小程序
     */
    @NotBlank(message = "请选择平台")
    private String displayPlatforms;

    /**
     * 城市列表
     */
    private List<TopCity> cityList;

}
