package com.extracme.evcard.mmp.vo;

/**
 * 项目名称：evcard-mmp-rest
 * 类名称：MembershipInfoVO
 * 类描述：新增修改会员VO
 * 创建人：sunb-孙彬
 * 创建时间：2017年9月21日上午9:43:15
 * 修改备注
 * @version2.0
 */
public class MembershipVO {
    /** 会员id */
    private String authId;
    /** 姓名 */
    private String name;
    /** 手机号 */
    private String mobilePhone;
    /** 用户类型（0：外籍用户，1：内地用户，2：港澳台用户） */
    private String userType;
    /** 驾驶证号 */
    private String drivingLicense;
    /** 驾照扫描图片URL */
    private String drivingLicenseImgUrl;
    /** 身份证扫描图片URL */
    private String idcardPicUrl;
    /** 手持身份证照 */
    private String holdIdcardPicUrl;
    /** 省 */
    private String provinceOfOrigin;
    /** 市 */
    private String cityOfOrigin;
    /** 区 */
    private String areaOfOrigin;
    /** 邮寄地址 */
    private String address;
    /** 备注来源 */
    private String infoOrigin;
    /** 操作原因 */
    private String subReason;
    /** 领取驾照时间 */
    private String obtainDriverTimer;
    /** 驾照到期时间 */
    private String licenseExpirationTime;
    /** 邮箱 */
    private String mail;
    /** 驾照类型 */
    private String drivingLicenseType;
    /** 是否勾选人脸识别（0：是，1：否） */
    private Integer isFlag;
    /**人脸识别*/
    private String faceRecognitionImgUrl;

    /**驾驶证档案编号*/
    private String fileNo;
    /**驾驶证副页照片显示*/
    private String fileNoImgUrl;
    /**证件类型*/
    private String idType;
    /**证件编号*/
        private String passportNo;

    /**
     * 身份证副页
     */
    private String idCardcBackPicUrl;

    /**
     * 证件到期日期
     */
    private String idCardExpirationTime;

    /**
     *  身份证 证件 有效期 是否长期  1：非长期 2：长期
     */
    private Integer idCardExpirationType;

    public String getIdCardcBackPicUrl() {
        return idCardcBackPicUrl;
    }

    public void setIdCardcBackPicUrl(String idCardcBackPicUrl) {
        this.idCardcBackPicUrl = idCardcBackPicUrl;
    }

    public String getIdCardExpirationTime() {
        return idCardExpirationTime;
    }

    public void setIdCardExpirationTime(String idCardExpirationTime) {
        this.idCardExpirationTime = idCardExpirationTime;
    }

    public Integer getIdCardExpirationType() {
        return idCardExpirationType;
    }

    public void setIdCardExpirationType(Integer idCardExpirationType) {
        this.idCardExpirationType = idCardExpirationType;
    }

    public String getFileNo() {
        return fileNo;
    }

    public void setFileNo(String fileNo) {
        this.fileNo = fileNo;
    }

    public String getFileNoImgUrl() {
        return fileNoImgUrl;
    }

    public void setFileNoImgUrl(String fileNoImgUrl) {
        this.fileNoImgUrl = fileNoImgUrl;
    }


    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getPassportNo() {
        return passportNo;
    }

    public void setPassportNo(String passportNo) {
        this.passportNo = passportNo;
    }

    public Integer getIsFlag() {
        return isFlag;
    }

    public void setIsFlag(Integer isFlag) {
        this.isFlag = isFlag;
    }


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getUserType() {
        return userType;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public String getDrivingLicense() {
        return drivingLicense;
    }

    public void setDrivingLicense(String drivingLicense) {
        this.drivingLicense = drivingLicense;
    }

    public String getDrivingLicenseImgUrl() {
        return drivingLicenseImgUrl;
    }

    public void setDrivingLicenseImgUrl(String drivingLicenseImgUrl) {
        this.drivingLicenseImgUrl = drivingLicenseImgUrl;
    }

    public String getIdcardPicUrl() {
        return idcardPicUrl;
    }

    public void setIdcardPicUrl(String idcardPicUrl) {
        this.idcardPicUrl = idcardPicUrl;
    }

    public String getHoldIdcardPicUrl() {
        return holdIdcardPicUrl;
    }

    public void setHoldIdcardPicUrl(String holdIdcardPicUrl) {
        this.holdIdcardPicUrl = holdIdcardPicUrl;
    }

    public String getProvinceOfOrigin() {
        return provinceOfOrigin;
    }

    public void setProvinceOfOrigin(String provinceOfOrigin) {
        this.provinceOfOrigin = provinceOfOrigin;
    }

    public String getCityOfOrigin() {
        return cityOfOrigin;
    }

    public void setCityOfOrigin(String cityOfOrigin) {
        this.cityOfOrigin = cityOfOrigin;
    }

    public String getAreaOfOrigin() {
        return areaOfOrigin;
    }

    public void setAreaOfOrigin(String areaOfOrigin) {
        this.areaOfOrigin = areaOfOrigin;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getInfoOrigin() {
        return infoOrigin;
    }

    public void setInfoOrigin(String infoOrigin) {
        this.infoOrigin = infoOrigin;
    }

    public String getSubReason() {
        return subReason;
    }

    public void setSubReason(String subReason) {
        this.subReason = subReason;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public String getObtainDriverTimer() {
        return obtainDriverTimer;
    }

    public void setObtainDriverTimer(String obtainDriverTimer) {
        this.obtainDriverTimer = obtainDriverTimer;
    }

    public String getLicenseExpirationTime() {
        return licenseExpirationTime;
    }

    public void setLicenseExpirationTime(String licenseExpirationTime) {
        this.licenseExpirationTime = licenseExpirationTime;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getDrivingLicenseType() {
        return drivingLicenseType;
    }

    public void setDrivingLicenseType(String drivingLicenseType) {
        this.drivingLicenseType = drivingLicenseType;
    }

    public String getFaceRecognitionImgUrl() {
        return faceRecognitionImgUrl;
    }

    public void setFaceRecognitionImgUrl(String faceRecognitionImgUrl) {
        this.faceRecognitionImgUrl = faceRecognitionImgUrl;
    }
}
