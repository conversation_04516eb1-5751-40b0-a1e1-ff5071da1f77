package com.extracme.evcard.mmp.common.rest;

import java.util.List;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.evcard.mmp.dto.AreaDTO;
import com.extracme.evcard.mmp.dto.CityDTO;
import com.extracme.evcard.mmp.dto.ProvinceDTO;
import com.extracme.evcard.mmp.service.IAdministrativeRegionsService;
import com.extracme.framework.core.vo.DefaultWebRespVO;

/**
 * 获取行政区域的接口.<br>
 * <AUTHOR>
 */
@Api(value = "region", tags = "U1-行政区域获取接口")
@RestController
@RequestMapping(value = "api")
public class AdministrativeRegionsController {

    @Resource
    IAdministrativeRegionsService administrativeRegionsServiceImpl;

    /**
     * 获取所有的省份信息.<br>
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "province", method = RequestMethod.GET)
    public DefaultWebRespVO allProvince() {
        // 查询出所有的省份信息
        List<ProvinceDTO> provinceDTOs = administrativeRegionsServiceImpl.getAllProvince();
        return DefaultWebRespVO.getSuccessVO(provinceDTOs);
    }

    /**
     * 根据省份获取城市信息.<br>
     * @param provinceId
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "city/province/{provinceId}", method = RequestMethod.GET)
    public DefaultWebRespVO cityByProvince(@PathVariable long provinceId) {
        // 查询出所有的省份信息
        List<CityDTO> cityDTOs = administrativeRegionsServiceImpl.getCityByProvince(provinceId, 2);
        return DefaultWebRespVO.getSuccessVO(cityDTOs);
    }

    /**
     * 根据城市获取区域信息.<br>
     * @param cityId
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "area/city/{cityId}", method = RequestMethod.GET)
    public DefaultWebRespVO areaByCity(@PathVariable long cityId) {
        // 查询出所有的区域信息
        List<AreaDTO> areaDTOs = administrativeRegionsServiceImpl.getAreaByCity(cityId);
        return DefaultWebRespVO.getSuccessVO(areaDTOs);
    }

    /**
     * 根据城市获取区域信息.<br>
     * @param cityId
     * @return
     * <AUTHOR>
     */
    @RequestMapping(value = "area/cityInfo/{cityId}", method = RequestMethod.GET)
    public DefaultWebRespVO getAreaByCity(@PathVariable long cityId) {
        // 查询出所有的区域信息
        List<AreaDTO> areaDTOs = administrativeRegionsServiceImpl.queryAreaByCity(cityId);
        return DefaultWebRespVO.getSuccessVO(areaDTOs);
    }

}
