package com.extracme.evcard.mmp.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.bo.ThirdCouponModelParamsBO;
import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.SweepActivityDetailDTO;
import com.extracme.evcard.mmp.dto.SweepActivityFullDTO;
import com.extracme.evcard.mmp.dto.activity.ActivityCouponModelPageDTO;
import com.extracme.evcard.mmp.dto.activity.ActivitySmsLogDto;
import com.extracme.evcard.mmp.service.ISweepActivityService;
import com.extracme.evcard.mmp.vo.BaseResultVo;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

/**
 * SweepAcvitityController class
 *
 * <AUTHOR>
 * @date 2018/3/19 14:31
 * Description 扫码送券活动
 */
@Api(value="sweepCoupon", tags = "扫码送券活动[7]")
@RestController
@RequestMapping("api/sweepCoupon")
public class SweepAcvitityController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ISweepActivityService sweepActivityServiceImpl;

    /**
     * 新增扫码发券活动
     *
     * @param sweepActivityFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="新增扫码发券活动", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "addSweepCouponActivity", method = RequestMethod.POST)
    public DefaultWebRespVO addSweepCouponActivity(@RequestParam(value = "imageFile")
                                                           CommonsMultipartFile file,
                                                   SweepActivityFullDTO sweepActivityFullDTO,
                                                   HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO = sweepActivityServiceImpl.addSweepCouponActivity(file.getInputStream(),
                    file.getOriginalFilename(), sweepActivityFullDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (IOException e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("新增扫码发券活动信息...");
        return vo;
    }

    /**
     * 修改扫码发券活动
     *
     * @param sweepActivityFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="修改扫码发券活动", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "updateSweepCouponActivity", method = RequestMethod.POST)
    public DefaultWebRespVO updateSweepCouponActivity(@RequestParam(value = "imageFile", required = false)
                                                              CommonsMultipartFile file,
                                                      SweepActivityFullDTO sweepActivityFullDTO,
                                                      HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            InputStream imageInputStream = null;
            String imageFileName = null;
            if (null != file) {
                imageInputStream = file.getInputStream();
                imageFileName = file.getOriginalFilename();
            }
            DefaultServiceRespDTO respDTO  = sweepActivityServiceImpl.updateSweepCouponActivity(imageInputStream,
                    imageFileName, sweepActivityFullDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (IOException e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("修改扫码发券活动信息...");
        return vo;
    }


    /**
     * 删除扫码发券活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @ApiOperation(value="删除扫码发券活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "deleteSweepActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO deleteSweepActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = sweepActivityServiceImpl.deleteSweepActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("删除扫码发券活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已删除");
    }

    /**
     * 停止扫码发券活动
     *
     * @param id      活动id
     * @param request 请求
     * @return vo 返回值
     */
    @ApiOperation(value="停止扫码发券活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "suspendSweepActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO suspendSweepActivity(@PathVariable("id") Long id, HttpServletRequest request) {
        ComModel comModel = ComUtil.getUserInfo(request);
        String createOperName = comModel.getCreateOperName();
        Long createOperId = comModel.getCreateOperId();
        String operatorContent = "停止";
        DefaultServiceRespDTO respDTO = sweepActivityServiceImpl.suspendSweepActivity(id, createOperName, createOperId, operatorContent);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("停止扫码发券活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已停止");
    }


    /**
     * 立即开始扫码发券活动
     *
     * @param id      活动id
     * @param request
     * @return
     */
    @ApiOperation(value="立即开始扫码发券活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "immediateStartSweepActivity/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO immediateStartSweepActivity(@PathVariable("id") Long id,
                                                        HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = sweepActivityServiceImpl.immediateStartSweepActivity(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("立即开始扫码发券活动");
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已开始");
    }

    /**
     * 发布扫码发券活动
     *
     * @param id 活动id
     * @param request
     * @return
     */
    @ApiOperation(value="发布扫码发券活动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "publish/{id}", method = RequestMethod.PUT)
    public DefaultWebRespVO publish(@PathVariable("id") Long id,
                                                        HttpServletRequest request) {
        DefaultServiceRespDTO respDTO = sweepActivityServiceImpl.publish(id, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("发布扫码发券活动, id={}", id);
        return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "活动已发布");
    }

    /**
     * 获取扫码发券活动详情信息
     *
     * @param id 活动id
     * @return
     */
    @ApiOperation(value="获取扫码发券活动详情信息", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "sweepActivityDetail/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO sweepActivityDetail(@PathVariable("id") Long id) {
        SweepActivityDetailDTO sweepActivityDetailDTO = sweepActivityServiceImpl.sweepActivityDetail(id);
        if (null == sweepActivityDetailDTO) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动信息不存在");
        }
        log.debug("获取扫码发券活动详情信息...");
        return DefaultWebRespVO.getSuccessVO(sweepActivityDetailDTO);
    }

    /**
     * 获取扫码发券活动优惠券模板列表
     *
     * @param paramsBO
     * @return
     */
    @ApiOperation(value="获取扫码发券活动优惠券模板列表", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "getSweepCouponModelPage", method = RequestMethod.POST)
    public DefaultWebRespVO getSweepCouponModelPage(@RequestBody ThirdCouponModelParamsBO paramsBO) {
        ActivityCouponModelPageDTO activityCouponModelDTO = sweepActivityServiceImpl.getCouponModelPage(paramsBO);
        log.debug("获取扫码发券活动优惠券模板列表...");
        return DefaultWebRespVO.getSuccessVO(activityCouponModelDTO);
    }

    @ApiOperation(value="活动短信日志", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @RequestMapping(value = "getActivitySmsLog/{activityId}", method = RequestMethod.GET)
    public DefaultWebRespVO getActivitySmsLog(@PathVariable("activityId") Long activityId){
        log.info("导出扫码送券活动短信发送日志，活动编号activityId:{}", activityId);
        try {
            DefaultServiceRespDTO respDTO = sweepActivityServiceImpl.getActivitySmsLog(activityId);
            Map<String, Object> map = JSON.parseObject(respDTO.getData(), Map.class);
            return DefaultWebRespVO.getSuccessVO(map);
        } catch (Exception e) {
            log.error("导出渠扫码送券活动短信发送日志失败，activityId:{}", activityId, e);
            return new DefaultWebRespVO("-1", "导出失败");
        }
    }
}