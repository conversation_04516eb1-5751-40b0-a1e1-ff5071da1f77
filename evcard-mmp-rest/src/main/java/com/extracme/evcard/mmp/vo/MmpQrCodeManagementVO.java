package com.extracme.evcard.mmp.vo;

import java.io.Serializable;

/**
 * 二维码管理VO
 * 
 * <AUTHOR>
 *
 */
public class MmpQrCodeManagementVO implements Serializable {

	private static final long serialVersionUID = 8277127578459998552L;
	
	/**
	 * 二维码ID
	 */
	private Long id;

	/**
	 * 二维码名称
	 */
	private String qrName;

	/**
	 * 组织机构ID
	 */
	private String orgId;

	/**
	 * 二维码类型 0-H5 1-小程序
	 */
	private Integer qrType;

	/**
	 * 二维码来源0-系统活动 1-手动输入URL地址
	 */
	private Integer qrSource;

	/**
	 * 活动id
	 */
	private Long activityId;

	/**
	 * 二维码链接
	 */
	private String qrUrl;

	/**
	 * 二维码图片
	 */
	private String qrPicture;

	/**
	 * 备注说明
	 */
	private String miscDesc;

	public String getQrName() {
		return qrName;
	}

	public void setQrName(String qrName) {
		this.qrName = qrName;
	}

	public String getOrgId() {
		return orgId;
	}

	public void setOrgId(String orgId) {
		this.orgId = orgId;
	}

	public Integer getQrType() {
		return qrType;
	}

	public void setQrType(Integer qrType) {
		this.qrType = qrType;
	}

	public Integer getQrSource() {
		return qrSource;
	}

	public void setQrSource(Integer qrSource) {
		this.qrSource = qrSource;
	}

	public Long getActivityId() {
		return activityId;
	}

	public void setActivityId(Long activityId) {
		this.activityId = activityId;
	}

	public String getQrUrl() {
		return qrUrl;
	}

	public void setQrUrl(String qrUrl) {
		this.qrUrl = qrUrl;
	}

	public String getQrPicture() {
		return qrPicture;
	}

	public void setQrPicture(String qrPicture) {
		this.qrPicture = qrPicture;
	}

	public String getMiscDesc() {
		return miscDesc;
	}

	public void setMiscDesc(String miscDesc) {
		this.miscDesc = miscDesc;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

}