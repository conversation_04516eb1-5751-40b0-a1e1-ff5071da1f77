package com.extracme.evcard.mmp.rest.smssend;

import com.extracme.evcard.mmp.bo.sms.SmsSendOperatorLogBO;
import com.extracme.evcard.mmp.bo.sms.SmsSendPendingReviewBO;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.sms.SmsOperatorLogDTO;
import com.extracme.evcard.mmp.service.sms.ISmsSendService;
import com.extracme.evcard.mmp.vo.ActSmsSendAuditVO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @title ActSmsSendAuditController
 * @projectName evcard-mmp
 * @author：孙世玉
 * @Created by SunShiYu on 2023/04/13
 */

@RestController
@RequestMapping("api/actSmsSendAudit")
public class ActSmsSendAuditController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    ISmsSendService smsSendService;


    /**
     * 查询是否有待审核的记录
     *
     * @param req 入参model
     * @return VO
     */
    @RequestMapping(value = "queryIfHavePendingReview", method = RequestMethod.POST)
    public DefaultWebRespVO queryIfHavePendingReview(@RequestBody SmsSendPendingReviewBO req) {

        try {
            DefaultServiceRespDTO dto = smsSendService.queryIfHavePendingReview(req.getOrgCode());
            // 详细应答参数，内容为 1-是、2-否
            if (dto.getCode() == 0) {
                return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "成功", Integer.parseInt(dto.getData()));
            } else {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, dto.getMessage());
            }
        } catch (Exception e) {
            log.error("queryIfHavePendingReview error",e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, e.getMessage());
        }
    }

    /**
     * 审批接口
     *
     * @param req 入参vo
     * @return VO
     */
    @RequestMapping(value = "batchAudit", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public DefaultWebRespVO actSmsSendAudit(@RequestBody ActSmsSendAuditVO req, HttpServletRequest request) {
        // 判断参数合法性
        if (CollectionUtils.isEmpty(req.getIdList())) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "选择需要审批的短信");
        }

        if (req.getAction() == null) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "选择审批模式");
        }

        if (req.getAction() != 1 && req.getAction() != 2) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "选择审批模式");
        }

        try {
            // 批量审批
            DefaultServiceRespDTO dto = smsSendService.batchSmsAudit(req.getIdList(), req.getAction(), request);
            if (dto.getCode() == 0) {
                return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "成功");
            } else {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, dto.getMessage());
            }
        } catch (Exception e) {
            log.error("batchAudit error",e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, e.getMessage());
        }

    }

    /**
     * 查询短信操作日志
     *
     * @param req  入参
     * @return VO
     */
    @RequestMapping(value = "operatorLog", method = RequestMethod.POST)
    public DefaultWebRespVO querySmsOperatorLogList(@RequestBody SmsSendOperatorLogBO req) {

        //短信id字段判空
        if (StringUtils.isBlank(req.getLogId())) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "缺少logId参数");
        }

        try {
            PageBeanBO<SmsOperatorLogDTO> pageBeanBO = smsSendService.querySmsOperatorAllLog(req.getPageNum(), req.getPageSize(), req.getLogId());
            return new DefaultWebRespVO(Contants.RETURN_SUCCESS_CODE, "成功", pageBeanBO);
        } catch (Exception e) {
            log.error("operatorLog error",e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, e.getMessage());
        }
    }


}
