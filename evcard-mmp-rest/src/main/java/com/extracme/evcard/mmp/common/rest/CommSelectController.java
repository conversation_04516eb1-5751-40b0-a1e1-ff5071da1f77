package com.extracme.evcard.mmp.common.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.bvm.bo.ServiceResult;
import com.extracme.evcard.bvm.dto.ComboDTO;
import com.extracme.evcard.bvm.service.IAgencyMemberService;
import com.extracme.evcard.mmp.rest.MdRestClient;
import com.extracme.evcard.mmp.rest.entity.QuerySecondChannelListRequest;
import com.extracme.evcard.mmp.rest.entity.QuerySecondChannelListResponse;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("api/commom/")
public class CommSelectController {
    @Autowired
    private MdRestClient mdRestClient;

    @Autowired
    private IAgencyMemberService agencyMemberService;

    @RequestMapping(value = "querySecondAppKeyList", method = {RequestMethod.GET})
    public DefaultWebRespVO querySecondAppKeyList(HttpServletRequest request,  @RequestParam(required = false) String secondAppName) {
        QuerySecondChannelListRequest querySecondChannelListRequest = new QuerySecondChannelListRequest();
        if (StringUtils.isNotBlank(secondAppName)) {
            querySecondChannelListRequest.setSecondAppName(secondAppName);
        }
        QuerySecondChannelListResponse querySecondChannelListResponse = mdRestClient.querySecondChannelListForMmp(querySecondChannelListRequest);
        if (querySecondChannelListResponse == null) {
            return new DefaultWebRespVO("-1", "查询失败");
        }
        if (querySecondChannelListResponse.getCode() == 0) {
            return DefaultWebRespVO.getSuccessVO(querySecondChannelListResponse.getData() != null ? querySecondChannelListResponse.getData().getSecondChannelInfoList() : null);
        } else {
            log.error("querySecondAppKeyList 失败，querySecondAppKeyListResponse={}", JSON.toJSONString(querySecondChannelListResponse));
            return new DefaultWebRespVO("-1", querySecondChannelListResponse.getMessage());
        }
    }


    @RequestMapping(value = "queryAgencyRoleList/{agencyId}", method = {RequestMethod.GET})
    public DefaultWebRespVO queryAgencyRoleList(HttpServletRequest request, @PathVariable("agencyId")String agencyId) {
        if (StringUtils.isBlank(agencyId)) {
            return new DefaultWebRespVO("-1", "入参不能为空");
        }
        ServiceResult serviceResult = agencyMemberService.comboAgencyRole(agencyId);
        log.info("queryAgencyRoleList serviceResult={},resut={}", JSON.toJSONString(serviceResult),JSON.toJSONString(serviceResult.getResult()));
        if (serviceResult.isSuccess()) {
            return DefaultWebRespVO.getSuccessVO(serviceResult.getResult());
        } else {
            log.error("queryAgencyRoleList 失败，serviceResult={}", JSON.toJSONString(serviceResult));
            return new DefaultWebRespVO("-1", serviceResult.getMessage());
        }
    }

}
