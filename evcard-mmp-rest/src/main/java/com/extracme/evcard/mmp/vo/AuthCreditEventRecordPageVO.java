package com.extracme.evcard.mmp.vo;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

/**
 * Created by Elin on 2017/11/22.
 * 会员事件记录列表显示字段
 */
public class AuthCreditEventRecordPageVO {


    /**
     * 事件编号
     */
    private Long eventId;

    /**
     * 会员id
     */
    private String authId;

    /**
     * 事件类型id
     */
    private Long eventTypeId;

    /**
     *事件类型名称
     */
    private String eventName;

    /**
     *是否直接加入黑名单
     */
    private Boolean blackList;

    /**
     * 操作来源
     */
    private String eventSource;

    /**
     *事件状态（0-撤销 1-正常）
     */
    private Integer eventStatus;

    /**
     * 申诉状态（0-未申诉 1 已申诉）
     */
    private Integer appealStatus;

    /**
     *创建时间
     */
    private Date createTime;

    /**
     *创建人id
     */
    private Long createOperId;

    /**
     *创建人姓名
     */
    private String createOperName;

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public String getAuthId() {
        return authId;
    }

    public void setAuthId(String authId) {
        this.authId = authId;
    }

    public Long getEventTypeId() {
        return eventTypeId;
    }

    public void setEventTypeId(Long eventTypeId) {
        this.eventTypeId = eventTypeId;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public Boolean getBlackList() {
        return blackList;
    }

    public void setBlackList(Boolean blackList) {
        this.blackList = blackList;
    }

    public Integer getEventStatus() {
        return eventStatus;
    }

    public void setEventStatus(Integer eventStatus) {
        this.eventStatus = eventStatus;
    }

    public Integer getAppealStatus() {
        return appealStatus;
    }

    public void setAppealStatus(Integer appealStatus) {
        this.appealStatus = appealStatus;
    }

    @JsonFormat(timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public String getEventSource() {
        return eventSource;
    }

    public void setEventSource(String eventSource) {
        this.eventSource = eventSource;
    }
}
