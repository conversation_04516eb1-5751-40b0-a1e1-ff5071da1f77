package com.extracme.evcard.mmp.vo;

import java.io.Serializable;

public class MemberShipAccountVO implements Serializable {

	private static final long serialVersionUID = -6062959093337993967L;
	/**
	 * E币赠送记录唯一标识
	 */
	private String amountInfoSeq;
	/**
	 * 会员ID
	 *//*
		 * private String authId;
		 */
	/**
	 * 赠送/扣除数量
	 */
	private Integer amount;
	/**
	 * 到期日（赠送时传）
	 */
	private String expireDate;
	/**
	 * 扣除原因
	 */
	private String remark;

	/**
	 * 时间类型（0：自定义 1：3个月 2：6个月 3：一年 4：三年）
	 */
	private Integer timeType;

	public Integer getAmount() {
		return amount;
	}

	public void setAmount(Integer amount) {
		this.amount = amount;
	}

	public String getExpireDate() {
		return expireDate;
	}

	public void setExpireDate(String expireDate) {
		this.expireDate = expireDate;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getAmountInfoSeq() {
		return amountInfoSeq;
	}

	public void setAmountInfoSeq(String amountInfoSeq) {
		this.amountInfoSeq = amountInfoSeq;
	}

	public Integer getTimeType() {
		return timeType;
	}

	public void setTimeType(Integer timeType) {
		this.timeType = timeType;
	}
}
