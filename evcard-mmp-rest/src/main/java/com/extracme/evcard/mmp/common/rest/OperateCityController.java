package com.extracme.evcard.mmp.common.rest;

import com.extracme.evcard.mmp.bo.OperateCityBO;
import com.extracme.evcard.mmp.consts.WarnConsts;
import com.extracme.evcard.mmp.dto.OperateCityDTO;
import com.extracme.evcard.mmp.rest.MdRestClient;
import com.extracme.evcard.mmp.rest.entity.GetAllTopCityInfoData;
import com.extracme.evcard.mmp.rest.entity.GetAllTopCityInfoRequest;
import com.extracme.evcard.mmp.rest.entity.GetAllTopCityInfoResponse;
import com.extracme.evcard.mmp.service.IOperateCityService;
import com.extracme.framework.core.bo.PageBO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import com.github.pagehelper.Page;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;


@Slf4j
@Api(value = "operateCity", tags = "U2-城市相关接口")
@RequestMapping("api")
@RestController
@ResponseBody
public class OperateCityController {

	@Resource
	IOperateCityService operateCityService;
	
	/**
	 * 会员归属配置列表
	 * @param operateCityBO
	 * @return
	 */
	@RequestMapping(value="getOperateCityList", method={RequestMethod.POST})
	public DefaultWebRespVO getOperateCityList(@RequestBody  OperateCityBO operateCityBO, HttpServletRequest request){
		String userName = request.getRemoteUser();
		List<OperateCityDTO> list = operateCityService.getOperateCityList(operateCityBO,userName);
		Page<OperateCityDTO> page = (Page<OperateCityDTO>) list;
		PageBO pageBO = new PageBO();
		pageBO.setPageNum(operateCityBO.getPageNum());
		pageBO.setPageSize(operateCityBO.getPageSize());
        BeanCopyUtils.copyProperties(page, pageBO);
        PageBeanBO<OperateCityDTO> pageBeanBO = new PageBeanBO<OperateCityDTO>(pageBO, list);
		return DefaultWebRespVO.getSuccessVO(pageBeanBO);
	}
	
	@RequestMapping(value="updateOperateCity", method={RequestMethod.PUT})
	public DefaultWebRespVO updateOperateCity(@RequestBody OperateCityDTO OperateCityDTO,HttpServletRequest request){
		String userName = request.getRemoteUser();
		DefaultServiceRespDTO result = operateCityService.updateOperateCity(OperateCityDTO,userName);
		if(result.getCode() == 1) {
	      return WarnConsts.NO_VALUE;
	    }else if(result.getCode()<0){
			return WarnConsts.UNKNOWN_ERROR;
		} 
		return DefaultWebRespVO.SUCCESS;
	}

	@Resource
	private MdRestClient mdRestClient;

	/**
	 * 获取顶部所有城市
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "getAllTopCity", method = {RequestMethod.GET})
	public DefaultWebRespVO getAllTopCity(HttpServletRequest request) {
		try {
			GetAllTopCityInfoRequest req = new GetAllTopCityInfoRequest();
			GetAllTopCityInfoResponse response = mdRestClient.getGetAllTopCityInfo(req);
			if (response == null) {
				return new DefaultWebRespVO("-1", "获取城市列表失败");
			}
			if (response.getCode() == 0) {
				GetAllTopCityInfoData getAllTopCityInfoData = response.getData();
				if (getAllTopCityInfoData != null) {
					return DefaultWebRespVO.getSuccessVO(getAllTopCityInfoData);
				}
			} else {
				return new DefaultWebRespVO(String.valueOf(response.getCode()), "获取城市列表失败");
			}
		} catch (Exception e) {
			log.error("getAllTopCity-获取城市列表失败", e);
		}
		return new DefaultWebRespVO("-1", "获取城市列表失败");
	}

	
}
