package com.extracme.evcard.mmp.activity.rest;

import com.extracme.evcard.activity.dto.*;
import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.service.IMmpUserBonusAuditService;
import com.extracme.evcard.mmp.vo.BaseResultVo;
import com.extracme.evcard.mmp.vo.MmpUserBonusAuditRecordVO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(value="inviteBonus", tags = "邀请现金奖励")
@RestController
@RequestMapping("/api/mmpUserAudit")
@Slf4j
public class MmpUserBonusAuditController {

    @Resource
    private IMmpUserBonusAuditService mmpUserBonusAuditService;

    @ApiOperation(value="现金奖励审核记录查询", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @PostMapping("/queryPage")
    public BaseResultVo<PageBeanBO<MmpUserBonusAuditRecordDTO>> queryPage(@RequestBody(required = false) MmpUserBonusAuditRecordVO queryVo){
        if (ObjectUtils.isEmpty(queryVo)){
            queryVo = new MmpUserBonusAuditRecordVO();
        }
        UserBonusAuditRecordQueryDto queryDto =new UserBonusAuditRecordQueryDto();
        BeanCopyUtils.copyProperties(queryVo, queryDto);
        PageQueryDTO pageQueryDTO=new PageQueryDTO();
        pageQueryDTO.setPageNum(queryVo.getPageNum());
        pageQueryDTO.setPageSize(queryVo.getPageSize());
        log.info("提交对象：" + queryVo +"\n提交分页:"+pageQueryDTO);
        BaseResultVo<PageBeanBO<MmpUserBonusAuditRecordDTO>> result = new BaseResultVo<>();
        try {
            PageBeanBO<MmpUserBonusAuditRecordDTO> pageBeanBO= mmpUserBonusAuditService.queryPage(queryDto, pageQueryDTO);
            result.setCode(Contants.RETURN_SUCCESS_CODE);
            result.setMessage("查询成功");
            result.setData(pageBeanBO);
        } catch (Exception e) {
            log.error("审核查询失败", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "查询失败");
        }
        return result;
    }

    @ApiOperation(value="审核记录详情", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "rewardStatus",value = "审核状态 0待审核 1审核通过 2审核拒绝",dataType = "int",required = true),
            @ApiImplicitParam(name = "userId",value = "邀请人id",dataType = "Long",required = true),
            @ApiImplicitParam(name = "auditId",value = "传auditId, 待审核0 其他状态下为实际auditId",dataType = "int",required = true),
    })
    @PostMapping("/getBonusReocords")
    public DefaultWebRespVO getShare(@RequestBody UserBonusOrderQueryDto input){
        log.info("paramMap：" + input);
        DefaultWebRespVO result = new DefaultWebRespVO();
        try {
            List<UserBonusInfoViewDto> list = mmpUserBonusAuditService.queryUserBonusList(input);
            result.setCode(Contants.RETURN_SUCCESS_CODE);
            result.setMessage("查询成功");
            result.setData(list);
        } catch (Exception e) {
            log.error("审核查询失败", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "查询失败");
        }
        return result;
    }

    @ApiOperation(value="现金奖励审核通过", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "list",value = "userId集合", dataType = "List",required = true),
    })
    @PostMapping("/success")
    public DefaultWebRespVO success(@RequestBody List<Long> list, HttpServletRequest request){
        DefaultWebRespVO result = new DefaultWebRespVO();
        try {
            if(ObjectUtils.isEmpty(list)){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "请选择记录数");
            }
            if(list.size()>50){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "全选操作不能超过50个。");
            }
            mmpUserBonusAuditService.bachAuditSuccess(list, request);
            result.setCode(Contants.RETURN_SUCCESS_CODE);
            result.setMessage("审核提交通过成功");
        } catch (Exception e) {
            log.error("审核提交通过失败", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "审核提交通过失败");
        }
        return result;
    }

    @ApiOperation(value="现金奖励审核批量拒绝", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "list",value = "userId集合",dataType = "List", required = true),
            //@ApiImplicitParam(name = "remark",value = "拒绝原因", dataType = "String", required = false),
    })
    @PostMapping("/bathRefuse")
    public DefaultWebRespVO bathRefuse(@RequestBody List<Long> list, HttpServletRequest request){
        DefaultWebRespVO result = new DefaultWebRespVO();
        if(ObjectUtils.isEmpty(list)){
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "请选择记录数");
        }
        if(list.size() > 50){
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "全选操作不能超过50个。");
        }
        try {
            mmpUserBonusAuditService.bathRefuse(list, StringUtils.EMPTY, request);
            result.setCode(Contants.RETURN_SUCCESS_CODE);
            result.setMessage("审核提交拒绝成功");
        } catch (Exception e) {
            log.error("审核提交拒绝失败", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "审核提交拒绝失败");
        }
        return result;
    }

    @ApiOperation(value="因退款而审核拒绝", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "orderSeq",value = "拒绝的订单编号",dataType = "List",required = true),
    })
    @PutMapping("/refuse/{id}")
    public DefaultWebRespVO refuse(@PathVariable Long id, HttpServletRequest request){
        DefaultWebRespVO result = new DefaultWebRespVO();
        if(id == null){
            result.setCode(Contants.RETURN_ERROR_CODE);
            result.setMessage("请选择记录");
        }
        try {
            mmpUserBonusAuditService.refuse(id, "订单退款", request);
            result.setCode(Contants.RETURN_SUCCESS_CODE);
            result.setMessage("审核提交拒绝成功");
        } catch (Exception e) {
            log.error("审核提交拒绝失败", e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "审核提交拒绝失败");
        }
        return result;
    }

}
