package com.extracme.evcard.mmp.rest.vipcard;

import com.extracme.evcard.mmp.common.ComModel;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.DateUtils;
import com.extracme.evcard.rpc.dto.BaseResponse;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.QuerySuixiangCardRefundLogInput;
import com.extracme.evcard.rpc.vipcard.dto.suixiang.SuixiangCardRefundLogDto;
import com.extracme.evcard.rpc.vipcard.service.ISuixiangCardRefundService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

@Slf4j
@RestController
@Api(value="vipcard", tags = "用户权益-卡片管理")
@RequestMapping("api/vipcard/card/refund")
public class CardRefundController {

    @Autowired
    private ISuixiangCardRefundService suixiangCardRefundService;

    @RequestMapping(value = "/{cardUseId}" ,method = RequestMethod.POST)
    public DefaultWebRespVO queryCardRefundLog(@PathVariable Long cardUseId, HttpServletRequest httpServletRequest) {
        ComModel comModel = ComUtil.getUserInfo(httpServletRequest);
        OperatorDto operatorDto = new OperatorDto();
        if (comModel != null) {
            operatorDto.setOperatorId(comModel.getCreateOperId());
            operatorDto.setOperatorName(comModel.getCreateOperName());
        }
        BaseResponse baseResponse = suixiangCardRefundService.manualRefundSuixiangCard(cardUseId, operatorDto);
        return new DefaultWebRespVO(String.valueOf(baseResponse.getCode()), baseResponse.getMessage());
    }
}
