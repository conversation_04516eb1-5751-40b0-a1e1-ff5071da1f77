package com.extracme.evcard.mmp.config;


import org.springframework.aop.Advisor;
import org.springframework.aop.aspectj.AspectJExpressionPointcut;
import org.springframework.aop.support.DefaultPointcutAdvisor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.interceptor.NameMatchTransactionAttributeSource;
import org.springframework.transaction.interceptor.RuleBasedTransactionAttribute;
import org.springframework.transaction.interceptor.TransactionAttribute;
import org.springframework.transaction.interceptor.TransactionInterceptor;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * 此处aop 自定义事务切面配置，只对com.extracme.evcard.**.service.*.*(..)层进行事务控制
 * 回滚策略：是默认的，即 遇到回滚事务 (ex instanceof RuntimeException || ex instanceof Error)
 *
 * 注意：
 * 程序员再到业务方法上增加@Transactional，会和当前配置事务类一起生效。
 * 防止出现在业务方法上增加 @Transactional(rollbackFor = Exception.class)
 *
 *      @Transactional(rollbackFor = Exception.class)
 *      public void test(){
 *          ...业务代码...
 *           操作db
 *          throw new Exception("test");
 *      }
 *      这种情况，会报抛出其它异常。程序员自己添加的 Transactional会回滚，本类的事务会commit，不一致会报错
 *
 *
 */
@Configuration
//springboot aop stater默认是enbale+spring.aop.proxy-target-class=true
//此处也可以不指定
@EnableAspectJAutoProxy(proxyTargetClass = true)
public class TxConfig {

    @Autowired
    private DataSourceTransactionManager transactionManager;

    // 创建事务通知
    @Bean(name = "txAdvice")
    public TransactionInterceptor txAdvice() {

        NameMatchTransactionAttributeSource source = new NameMatchTransactionAttributeSource();

        RuleBasedTransactionAttribute notReadOnlyTx = new RuleBasedTransactionAttribute();
        notReadOnlyTx.setReadOnly(false);
        notReadOnlyTx.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        Map<String, TransactionAttribute> txAttributesMap = new HashMap<>();
        txAttributesMap.put("insert*", notReadOnlyTx);
        txAttributesMap.put("save*", notReadOnlyTx);
        txAttributesMap.put("del*", notReadOnlyTx);
        txAttributesMap.put("update*", notReadOnlyTx);
        txAttributesMap.put("add*", notReadOnlyTx);
        txAttributesMap.put("transaction*", notReadOnlyTx);
        txAttributesMap.put("operate*", notReadOnlyTx);
        txAttributesMap.put("generate*", notReadOnlyTx);
        txAttributesMap.put("export*", notReadOnlyTx);
        txAttributesMap.put("show*", notReadOnlyTx);
        source.setNameMap(txAttributesMap);

        TransactionInterceptor tsi = new TransactionInterceptor(transactionManager, source);
        return tsi;
    }


    @Bean
    public Advisor txAdviceAdvisor() {
        AspectJExpressionPointcut servicesPointcut = new AspectJExpressionPointcut();
        servicesPointcut.setExpression("(execution(* com.extracme.evcard.**.service.*.*(..))) ");
        return new DefaultPointcutAdvisor(servicesPointcut, txAdvice());
    }
}
