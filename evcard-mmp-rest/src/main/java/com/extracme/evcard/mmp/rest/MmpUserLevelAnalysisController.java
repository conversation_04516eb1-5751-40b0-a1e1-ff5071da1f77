package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.MmpJsonDTO;
import com.extracme.evcard.mmp.dto.MmpUserLevelAnalysisRequestDTO;
import com.extracme.evcard.mmp.service.IMmpUserLevelAnalysisService;
import com.extracme.evcard.mmp.vo.MmpUserLevelAnalysisRequestVO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.util.BeanCopyUtils;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 项目名称：evcard-mmp-rest
 * 类名称：MmpUserLevelAnalysisController
 * 类描述：会员分析控制层
 * 创建人：sunb-孙彬
 * 创建时间：2017年11月6日上午10:24:43
 * 修改备注
 *
 * @version2.0
 */
@RestController
@RequestMapping("api")
public class MmpUserLevelAnalysisController {
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IMmpUserLevelAnalysisService mmpUserLevelAnalysisImpl;

    /**
     * 新增会员分析信息
     *
     * @param mmpUserLevelAnalysisRequestVO
     * @param request
     * @return
     */
    @RequestMapping(value = "mmpUserLevelAnalysisInfo", method = RequestMethod.POST)
    public DefaultWebRespVO addMmpUserLevelAnalysisInfo(@RequestBody MmpUserLevelAnalysisRequestVO mmpUserLevelAnalysisRequestVO, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            MmpUserLevelAnalysisRequestDTO mmpUserLevelAnalysisRequestDTO = new MmpUserLevelAnalysisRequestDTO();
            BeanCopyUtils.copyProperties(mmpUserLevelAnalysisRequestVO, mmpUserLevelAnalysisRequestDTO);
            DefaultServiceRespDTO respDTO = mmpUserLevelAnalysisImpl.addAnalysisInfo(mmpUserLevelAnalysisRequestDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            vo.setMessage("添加失败");
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("新增会员分析信息...");
        return vo;
    }

    /**
     * 查看会员分析信息
     *
     * @param year  年份
     * @param orgId 所属公司
     * @param type  图表出哪种类型（1：会员数，2：会员数占比，3：订单数，4：订单数占比，5：实付款，6：实付款占比）
     * @return
     */
    @RequestMapping(value = "mmpUserLevelAnalysisInfo", method = RequestMethod.GET)
    public DefaultWebRespVO getAnalysisInfo(@RequestParam(value = "year", required = true) Integer year, @RequestParam(value = "orgId", required = true) String orgId,
                                            @RequestParam(value = "type", required = true) Integer type) {
        MmpJsonDTO mmpJsonDTO = mmpUserLevelAnalysisImpl.getAnalysisInfo(year, orgId, type);
        log.debug("查看会员分析信息...");
        return DefaultWebRespVO.getSuccessVO(mmpJsonDTO);
    }

    /**
     * 导出会员分析信息
     *
     * @param year  年份
     * @param orgId 所属公司
     * @param type  图表出哪种类型（1：会员数，2：会员数占比，3：订单数，4：订单数占比，5：实付款，6：实付款占比）
     * @return
     */
    @RequestMapping(value = "exportMmpUserLevelAnalysisInfo", method = RequestMethod.GET)
    public DefaultWebRespVO exportAnalysisInfo(@RequestParam(value = "year", required = true) Integer year, @RequestParam(value = "orgId", required = true) String orgId,
                                               @RequestParam(value = "type", required = true) Integer type, HttpServletRequest request, HttpServletResponse response) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        DefaultServiceRespDTO respDTO = mmpUserLevelAnalysisImpl.exportAnalysisInfo(year, orgId, type, request, response);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        vo.setMessage("会员分析信息导出成功！");
        log.debug("导出会员分析信息...");
        return vo;
    }
}
