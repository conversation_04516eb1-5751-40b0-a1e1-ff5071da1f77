package com.extracme.evcard.mmp.vo;

import com.extracme.evcard.tcs.provider.api.dto.task.NoviceTaskConfigDTO;
import lombok.Data;

/**
 * 新手任务类：配置实体
 * <AUTHOR>
 * @Discription
 * @date 2019/11/18
 * @remark 用于处理因form-data提交时包含文件，则上传list无法映射
 */
@Data
public class NoviceTaskConfigVO extends NoviceTaskConfigDTO {
    /**优惠券模板 因form-data提交时包含文件，则上传list无法映射*/
    private String couponModelsVal;

    /**修改时 传入修改过的优惠券模板id 因form-data提交时包含文件，则上传list无法映射*/
    private String updateCouponSeqListVal;
}
