package com.extracme.evcard.mmp;


import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.extracme.evcard.elasticjob.EnableElasticJob;
import com.extracme.evcard.redis.spring.EnableRedisUtil;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.*;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;


/**
 * 指定为 com.extracme.evcard, 可以扫描到 authority
 * 排除缺省的dataSource注入
 */
@SpringBootApplication(scanBasePackages = "com.extracme.evcard", exclude = DataSourceAutoConfiguration.class)
/**
 * 多模块项目 applicationContext为null问题
 * import无效，DependsOn
 */
//@Import(SpringContextUtil.class)
@DependsOn("springContextUtil")
@EnableAsync
@EnableDubbo
@EnableElasticJob
@EnableApolloConfig
@EnableRedisUtil
@EnableTransactionManagement
@PropertySources({
		@PropertySource("classpath:config.properties"),
})
//@PropertySources({
//        @PropertySource("classpath:sso-client.properties"),
//        @PropertySource("classpath:warn.properties"),
//        @PropertySource("classpath:aliyunConfig.properties"),
//		@PropertySource("classpath:config.properties"),
//        @PropertySource("classpath:message.properties"),
//        @PropertySource("classpath:es.properties"),
//		@PropertySource("classpath:redis.properties")
//})
@MapperScan(value = {"com.extracme.evcard.**.dao"})
@ImportResource(locations = {"classpath:dubbo-consumer.xml"})
public class App {

    public static void main(String[] args) {
        SpringApplication.run(App.class, args);
    }
}

