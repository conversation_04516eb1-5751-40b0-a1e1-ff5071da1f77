package com.extracme.evcard.mmp.config;

import com.extracme.evcard.authority.dto.SystemConfig;
import com.extracme.evcard.authority.interceptor.AuthInterceptor;
import com.extracme.evcard.authority.service.IAuthorityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * 使用WebMvcConfigurer 替换 WebMvcConfigurationSupport
 */
@Slf4j
@Component
public class InterceptorConfig implements WebMvcConfigurer {
    @Resource
    private IAuthorityService authorityService;
    @Autowired
    private SystemConfig systemConfig;

    @Autowired
    private AuthInterceptor authInterceptor;

    @Autowired
    private EnhanceUserAuthInterceptor enhanceUserAuthInterceptor;


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        log.debug("添加REST拦截器");
        // 增强 权限拦截器
        registry.addInterceptor(enhanceUserAuthInterceptor).addPathPatterns("/api/queryAgencyMemberList","/api/getMemberDepositLogs","/api/rapidRefund");
        registry.addInterceptor(authInterceptor)
                .addPathPatterns(
                        "/api/userList",
                        "/api/listAllSystem",
                        "/api/user",
                        "/api/user/{id}",
                        "/api/disableUser/{id}/{status}",
                        "/api/resetPassword/{id}",
                        "/api/saveSystem",
                        "/api/modifySystem",
                        "/api/deleteSystem/{id}",
                        "/api/queryUserInfoList",
                        "/api/roles/{roleId}",
                        "/api/disableRoles/{roleId}",
                        "/api/queryUserInfoList",
                        "/api/userRole/{userId}",

                        //运营管理
                        "/api/getOperateCityList",
                        "/api/updateOperateCity",

                        //优惠券管理
                        "/api/getCouponConfigList",
                        "/api/updateSumAvailable",

                        //条款管理
                        "/api/provisionList",
                        "/api/provision/{provisionId}/{flg}",
                        "/api/provision/{provisionId}",
                        "/api/provision/publish/{provisionId}",
                        "/api/hiProvisionList/{authId}",
                        "/api/provision/download/{ids}",
                        "/api/operateLogList/{provisionId}",
                        //会员守则和隐私政策  详情
                        "/api/memberProvision/{provisionId}/{flg}",
                        //会员守则和隐私政策  发布
                        "/api/memberProvision/publish/{provisionId}",
                        //会员守则和隐私政策  修改
                        "/api/memberProvision/modifyProvision",
                        //会议法律条款
                        "/api/historyProvisionList",

                        //常见问题
                        //导出问题用户建议
                        "/api/question/exportFeedback",
                        //问题分类管理
                        "/api/questionGroup/tree",
                        //问题分类新增修改
                        "/api/questionGroup/add",
                        "/api/questionGroup/update",
                        //问题分类删除
                        "/api/questionGroup/delete/{id}",


                        //个人会员
                        //新增会员
                        "/api/membershipInfo",
                        //修改备注来源
                        "/api/membershipInfoOrginInfo/{authId}",
                        //免除租车押金
                        "/api/membershipExemptdepositInfo/{authId}",
                        //修改关联企业
                        "/api/membershipAgencyInfo/{authId}",
                        //导出会员资料
                        "/api/exportMembershipInfo/{name}/{mobilePhone}",
                        //会员信息
                        //审核
                        "/api/updateAuthIdItems",
                        //修改
                        "/api/membershipInfo/{authId}",
                        //修改手机号
                        "/api/updateUserMobile/{authId}/{mobile}",
                        //重新审核
                        "/api/againVerify",
                        //驾照冒用
                        "/api/handleCounterfeit",
                        //重置密码
                        "/api/resetPassword/{authId}",
                        //删除会员
                        "/api/deleteMember/{authId}",
                        //会员卡暂停/注销
                        "/api/cardInfo",
                        //预授权转押金
                        "/api/receiveMoney/{authId}",
                        //清空
                        "/api/cleanChangeNum/{authId}",
                        //复查通过
                        "/api/updateReexamineStatus",
                        //复查不通过
                        "/api/updateNoPassReexamineStatus",

                        //押金监管
                        "/api/memberDeposit/returnEnterpriseDeposit/{authId}",
                        "/api/memberDeposit/returnPersonalDeposit/{authId}",
                        "/api/memberDeposit/preAuthorization/{authId}",
                        "/api/memberDeposit/preAuthorizationRecord/{authId}",

                        //会员id转换
                        "/api/exportFile/list",
                        "/api/exportMemberById",

                        //普通退还押金
                        "/api/returnDeposit/{authId}",
                        //高端车退还押金
                        "/api/returnDepositLevel/{authId}",
                        //解冻普通预授权
                        "/api/preAuthThaw/{authId}",
                        //解冻高端预授权
                        "/api/preAuthThawLevel/{authId}",
                        //车辆押金预授权交易一览
                        "/api/queryDepositRecords/{authId}",
                        //高端车预授权转押金
                        "/api/depositReceiveMoney/{authId}",
                        //会员所属
                        "/api/mmpOrgId/{authId}",
                        //编辑身份证号
                        "/api/updateIdCardNumber/{authId}/{IdCardNumber}",
                        //新驾照信息审核替换驾照信息
                        "/api/synchronizeAdditionalInfo/{authId}",
                        //新驾照信息审核不通过
                        "/api/updateAdditionalInfoNotThroughReasons/{authId}",
                        //查询会员退押申请记录
                        "/api/memberDrawBackList",
                        //导出会员退押申请记录
                        "/api/exportMemberDrawBackList",
                        //显示会员敏感信息
                        "/api/showMemberSecretInfo/{authId}/{messageType}",
                        //芝麻新用授权解绑
                        "/api/memberAccount/unbindZhimaAuth/{authId}",
                        //驾照三要素重新认证
                        "/api/reCertification/{authId}",
                        //驾照三要素手动认证通过
                        "/api/licenseElementsAuthSuccess/{authId}",
                        //账号解冻
                        "/api/accountRecover/{authId}",

                        //市场活动
                        //活动管理
                        //新增活动
                        "/api/nightCarInfo",
                        //修改包夜车活动
                        "/api/updateNightCarInfo/{id}",
                        //停止包夜车活动
                        "/api/suspendNightCarInfo/{id}",
                        //删除包夜车活动
                        "/api/deleteNightCarInfo/{id}",

                        //数据分析
                        //消费等级
                        //导出分析数据
                        "/api/exportMmpUserLevelAnalysisInfo/{year}/{orgId}/{type}",

                        //信用事件
                        //新增事件类型
                        "/api/credit/addCreditEventType",
                        //修改事件类型
                        "/api/credit/updateCreditEventType",
                        //获取信用事件类型维护列表
                        "/api/credit/getCreditEventTypeList",
                        //新增信用事件记录
                        "/api/credit/addCreditEvent",
                        //获取会员负面记录
                        //"/api/credit/getAuthCreditEventList",
                        //获取申诉事件列表
                        //"/api/credit/getAppealEventList",
                        //获取事件记录列表
                        //"/api/credit/getCreditEventList",
                        //处理申诉事件
                        "/api/credit/handleAppealEventStatus",
                        //进行事件申诉
                        "/api/credit/saveAppealEvent",
                        //获取事件类型分析列表
                        "/api/credit/getCreditEventTypeReportList",
                        //导出事件类型分析数据报表
                        "/api/credit/exportEventTypeReportData",
                        //获取信用事件详情
                        "/api/credit/getCreditEventDetail",

                        //第三方发券
                        //第三方发券配置新增
                        "/api/thirdCoupon/addThirdOfferCouponActivity",
                        //复制配置新增
                        "/api/thirdCoupon/copyThirdOfferCouponActivity",
                        //第三方发券配置修改
                        "/api/thirdCoupon/updateThirdOfferCouponActivity",
                        //第三方发券配置详情
                        "/api/thirdCoupon/thirdOfferCouponActivityDetail/{id}",
                        //第三方发券配置开始
                        "/api/thirdCoupon/immediateStartThirdActivity/{id}",
                        //第三方发券配置暂停
                        "/api/thirdCoupon/pauseThirdActivity/{id}",
                        //第三方发券配置恢复
                        "/api/thirdCoupon/resumeThirdActivity/{id}",
                        //第三方发券配置停止
                        "/api/thirdCoupon/suspendThirdActivity/{id}",
                        //第三方发券配置删除
                        "/api/thirdCoupon/deleteActivity/{id}",

                        //充值e币发券
                        //充值e币发券配置新增
                        "/api/eCoupon/addEOfferCouponActivity",
                        //充值e币发券配置修改
                        "/api/eCoupon/updateEOfferCouponActivity",
                        //充值e币发券配置详情
                        "/api/eCoupon/eOfferCouponActivityDetail/{id}",
                        //充值e币发券开始
                        "/api/eCoupon/immediateStartEActivity/{id}",
                        //充值e币发券暂停
                        "/api/eCoupon/pauseEActivity/{id}",
                        //充值e币发券恢复
                        "/api/eCoupon/resumeEActivity/{id}",
                        //充值e币发券停止
                        "/api/eCoupon/suspendEActivity/{id}",
                        //充值e币发券删除
                        "/api/eCoupon/deleteActivity/{id}",

                        //渠道注册奖励送券活动
                        //渠道注册奖励送券活动新增
                        "/api/channelReward/addChannelRewardActivity",
                        //渠道注册奖励送券活动修改
                        "/api/channelReward/updateChannelRewardActivity",
                        //渠道注册奖励送券活动详情
                        "/api/channelReward/channelRewardActivityDetail/{id}",
                        //渠道注册奖励送券活动开始
                        "/api/channelReward/immediateStartChannelRewardActivity/{id}",
                        //渠道注册奖励送券活动停止
                        "/api/channelReward/suspendChannelRewardActivity/{id}",
                        //渠道注册奖励送券活动删除
                        "/api/channelReward/deleteActivity/{id}",
                        "/api/channelReward/publish/{id}",

                        //活动中心活动
                        //活动中心活动新增
                        "/api/activityCenter/addActivity",
                        //活动中心活动修改
                        "/api/activityCenter/updateActivity",
                        //活动中心活动详情
                        "/api/activityCenter/activityCenterDetail/{id}",
                        //活动中心活动开始
                        "/api/activityCenter/immediateStartActivity/{id}",
                        //活动中心活动停止
                        "/api/activityCenter/suspendActivity/{id}",
                        //活动中心活动删除
                        "/api/activityCenter/deleteActivity/{id}",
                        //活动中心活动暂停
                        "/api/activityCenter/pauseActivity/{id}",
                        //活动中心活动恢复
                        "/api/activityCenter/resumeActivity/{id}",
                        //活动中心活动发布
                        "/api/activityCenter/publishActivity/{id}",
                        //活动中心活动列表
                        "/api/activityCenter/queryActivityList",
                        //活动中心置顶
                        "/api/activityCenter/updateActivityToTop/{activitySeq}",

                        //快递单号
                        //快递单号查询
                        "/api/expressQuery",
                        //快递单号查询导入
                        "/api/addExpressImport",

                        //优惠券批量导入
                        "/api/couponBatchImportActivity",

                        //生成兑换码
                        "/api/createRedeemCode",
                        //导出兑换码
                        "/api/exportCreateRedeemCode/{id}",

                        //渠道管理
                        //渠道一览
                        //"/api/channelManagement",

                        //详情
                        "/api/channelDetail/{appKey}",
                        //显示
                        "/api/showSecretKey/{appKey}",
                        //渠道日志
                        "/api/channelOperatorLog/{appKey}",

                        //优惠券发放记录
                        //查询
                        "/api/couponGrantRecordQuery",
                        //导出
                        "/api/exportCouponGrantRecordQuery",
                        //批量作废用户优惠券
                        "/api/batchInvalidatedUserCoupon",
                        //查看详细
                        "/api/queryCouponGrantRecordDetail",
                        //获取用户优惠券操作日志
                        "/api/getUserCouponOperatorLogs",

                        //邀请好友活动
                        //新增
                        "/api/inviteCoupon/addInviteActivity",
                        //修改
                        "/api/inviteCoupon/updateInviteActivity",
                        //详情
                        "/api/inviteCoupon/inviteActivityDetail/{id}",
                        //立即开始
                        "/api/inviteCoupon/immediateStartInviteActivity/{id}",
                        //停止
                        "/api/inviteCoupon/suspendInviteActivity/{id}",
                        //删除
                        "/api/inviteCoupon/deleteActivity/{id}",
                        //邀请好友记录
                        "/api/inviteCoupon/getInviteRewardRecords",
                        //邀请好友记录，根据活动id获取配置的优惠券模板信息
                        "/api/inviteCoupon/getInviteActivityCoupons",

                        //制卡账户管理
                        //新增
                        "/api/markCard/addMakeCardInfo",
                        //详情
                        "/api/markCard/makeCardInfoDetail/{userId}",
                        //列表
                        "/api/markCard/getMakeCardListPage",

                        //欢迎页活动
                        //新增
                        "/api/welcome/addWelcomeActivity",
                        //修改
                        "/api/welcome/updateWelcomeActivity",
                        //详情
                        "/api/welcome/welcomeActivityDetail/{id} ",
                        //发布
                        "/api/welcome/publishWelcomeActivity/{id} ",
                        //立即开始
                        "/api/welcome/immediateStartWelcomeActivity/{id}",
                        //停止
                        "/api/welcome/suspendWelcomeActivity/{id}",
                        //删除
                        "/api/welcome/deleteWelcomeActivity/{id}",
                        //暂停
                        "/api/welcome/pauseWelcomeActivity/{id}",
                        //恢复
                        "/api/welcome/resumeWelcomeActivity/{id}",
                        //列表
                        "/api/welcome/queryWelcomeActivityListPage",
                        //欢迎页统计列表
                        "/api/welcome/getWelcomePageStatistics",
                        //欢迎页统计数据导出
                        "/api/welcome/exportWelcomePageStatistics",

                        //首页弹框
                        //新增
                        "/api/layer/add",
                        //修改
                        "/api/layer/update",
                        //详情
                        "/api/layer/query/{id}",
                        //发布
                        "/api/layer/publish/{id}",
                        //立即开始
                        "/api/layer/start/{id}",
                        //停止
                        "/api/layer/suspend/{id}",
                        //删除
                        "/api/layer/delete/{id}",
                        //暂停
                        "/api/layer/pause/{id}",
                        //恢复
                        "/api/layer/resume/{id}",
                        //置顶
                        "/api/layer/updateTopStatus/{id}/{layerId}",
                        //列表
                        "/api/layer/list",
                        //广告页数据统计列表
                        "/api/layer/getLayerStatistics",
                        //广告页数据统计导出
                        "/api/layer/exportLayerPageStatistics",

                        //浦发会员
                        //浦发会员信息一览
                        "/api/spdBankMembershipInfo",
                        //查询冻结结果
                        "/api/frozenDepositResult/{driverCode}",
                        //查询解冻结果
                        "/api/unfrozenDepositResult/{driverCode}",
                        //查询扣款结果
                        "/api/decreaseDepositResult/{driverCode}",
                        //同步审核结果
                        "/api/signCheck/{driverCode}",
                        //冻结押金
                        "/api/frozenDeposit/{driverCode}",
                        //解约
                        "/api/cancel/{driverCode}",
                        //解冻
                        "/api/unfrozenDeposit/{driverCode}",
                        //显示会员敏感信息
                        "/api/getSecretSpdBankInfo/{authId}",

                        //扫码发券
                        //新增
                        "/api/sweepCoupon/addSweepCouponActivity",
                        //修改
                        "/api/sweepCoupon/updateSweepCouponActivity",
                        //详情
                        "/api/sweepCoupon/sweepActivityDetail/{id}",
                        //立即开始
                        "/api/sweepCoupon/immediateStartSweepActivity/{id}",
                        //停止
                        "/api/sweepCoupon/suspendSweepActivity/{id}",
                        //删除
                        "/api/sweepCoupon/deleteSweepActivity/{id}",
                        "/api/sweepCoupon/publish/{id}",

                        //充值送券套餐
                        //新增
                        "/api/recharge/addRechargePackagesTemplate",
                        //修改
                        "/api/recharge/updateRechargePackagesTemplate",
                        //详情
                        "/api/recharge/getRechargePackagesTemplateDetail/{packagesId}",
                        //启用禁用配置
                        "/api/recharge/updateRechargePackagesStatus/{packagesId}/{packagesStatus}",
                        //列表
                        "/api/recharge/queryRechargePackagesTemplateList",
                        //日志
                        "/api/recharge/queryRechargePackagesLog",

                        //模板管理
                        //新增
                        "/api/templateManage/addTemplate",
                        //修改
                        "/api/templateManage/updateTemplate",
                        //详情
                        "/api/templateManage/queryTemplateDetail/{id}",
                        //列表
                        "/api/templateManage/queryTemplateList",
                        //删除
                        "/api/templateManage/deleteTemplate/{id}",
                        //模板日志
                        "/api/templateManage/queryTemplateLog",

                        //品牌活动发券
                        //新增
                        "/api/brandActivity/addBrandActivity",
                        //修改
                        "/api/brandActivity/updateBrandActivity",
                        //详情
                        "/api/brandActivity/brandActivityDetail/{id}",
                        //下载已导入的品牌券码
                        "/api/brandActivity/exportBrandCouponCode",
                        //立即开始品牌活动
                        "/api/brandActivity/immediateStartBrandActivity/{id}",
                        //停止品牌活动
                        "/api/brandActivity/suspendBrandActivity/{id}",
                        //删除品牌活动
                        "/api/brandActivity/deleteBrandActivity/{id}",

                        //订单分享活动发券
                        //新增
                        "/api/orderShareActivity/addOrderShareActivity",
                        //修改
                        "/api/orderShareActivity/updateOrderShareActivity",
                        //获取订单分享发券活动详情信息
                        "/api/orderShareActivity/orderShareActivityDetail/{id}",
                        //下载已导入的品牌券码
                        "/api/orderShareActivity/exportBrandCouponCode",
                        //立即开始订单分享活动
                        "/api/orderShareActivity/immediateStartOrderShareActivity/{id}",
                        //停止订单分享活动
                        "/api/orderShareActivity/suspendOrderShareActivity/{id}",
                        //删除停止订单分享活动
                        "/api/orderShareActivity/deleteOrderShareActivity/{id}",

                        //随E停活动
                        //新增
                        "/api/epark/addEParkActivity",
                        //修改
                        "/api/epark/updateEParkActivity",
                        //获取订单分享发券活动详情信息
                        "/api/epark/eparkActivityDetail/{id}",
                        //立即开始订单分享活动
                        "/api/epark/immediateStartEParkActivity/{id}",
                        //停止订单分享活动
                        "/api/epark/suspendEParkActivity/{id}",
                        //删除停止订单分享活动
                        "/api/epark/deleteEParkActivity/{id}",

                        //二维码管理
                        //新增
                        "/api/qrcode/addQRCode",
                        //修改
                        "/api/qrcode/updateQRCode",
                        //获取二维码详情信息
                        "/api/qrcode/getQRCode/{id}",
                        //二维码列表
                        "/api/qrcode/queryQRCodeList",
                        //删除二维码
                        "/api/qrcode/deleteQRCode/{id}",
                        //生成二维码
                        "/api/qrcode/createQRCode/{id}",
                        //变更二维码
                        "/api/qrcode/updateQRCodeUrl",
                        //二维码变更历史
                        "/api/qrcode/queryQrCodeUrlHistory",

                        //E币赠送/扣除
                        //赠送E币-个人
                        "/api/presentEAmount/{authId}",
                        //扣除E币-个人
                        "/api/deductPresentEAmount/{authId}",
                        //赠送E币-企业
                        "/api/presentEAmountForOrg/{agencyId}",
                        //扣除E币-企业
                        "/api/deductPresentEAmountForOrg/{agencyId}",
                        //E币赠送记录
                        "/api/queryEAmountPresentInfo",

                        //公告管理
                        //新增
                        "/api/announcement/addAnnouncement",
                        //修改
                        "/api/announcement/updateAnnouncement",
                        //获取公告详情信息
                        "/api/announcement/getAnnouncementById/{id}",
                        //公告列表
                        "/api/announcement/queryAnnouncementList",
                        //删除公告
                        "/api/announcement/deleteAnnouncement/{id}",
                        //立即上线
                        "/api/announcement/onLineImmediately/{id}",
                        //下线
                        "/api/announcement/offLineImmediately/{id}",

                        //充值记录
                        //充值记录列表
                        "/api/rechargeRecord/queryRechargeRecordList",
                        //活动详情
                        "/api/rechargeRecord/getEOfferCouponActivityDetail/{id}",

                        //批量导入活动 - 短模板
                        //新增
                        "/api/addBatchImportCouponActivity",
                        //修改
                        "/api/updateBatchImportCouponActivity",
                        //获取活动详情信息
                        "/api/deleteBatchImportCouponActivity/{id}",
                        //开始
                        "/api/startBatchImportCouponActivity",
                        //删除
                        "/api/deleteBatchImportCouponActivity/{id}",

                        //批量导入活动 - 长模板
                        //新增
                        "/api/addBatchImportCouponLong",
                        //修改
                        "/api/updateBatchImportCouponLong",
                        //开始
                        "/api/startBatchImportCouponLong",
                        //删除
                        "/api/deleteBatchImportCouponLong/{id}",


                        //批量导入活动-新接口
                        "/api/batchImportActivity/add",
                        "/api/batchImportActivity/update",
                        "/api/batchImportActivity/delete/{id}",
                        "/api/batchImportActivity/detail/{id}",
                        "/api/batchImportActivity/import",
                        "/api/batchImportActivity/checkTemplate",
                        "/api/batchImportActivity/importCoupons",

                        "/api/unservedBatchImport/add",
                        "/api/unservedBatchImport/update",
                        "/api/unservedBatchImport/delete/{id}",
                        "/api/unservedBatchImport/detail/{id}",
                        "/api/unservedBatchImport/import",
                        "/api/unservedBatchImport/checkTemplate",
                        "/api/unservedBatchImport/importCoupons",

                        "/api/templateBatchImport/add",
                        "/api/templateBatchImport/update",
                        "/api/templateBatchImport/delete/{id}",
                        "/api/templateBatchImport/detail/{id}",
                        "/api/templateBatchImport/import",
                        "/api/templateBatchImport/checkTemplate",
                        "/api/templateBatchImport/importCoupons",

                        //查看会员驾照照片
                        "/api/showMemberdrivingLicenseImgUrl/{authId}/{messageType}",

                        //额度管理
                        //增加/减少额度
                        "/api/updateCouponConfig",
                        //查询额度列表
                        "/api/queryCouponConfigList",
                        //查询额度变更日志
                        "/api/getUpdateCouponConfigLog",
                        //导出额度变更日志
                        "/api/exportCouponConfigLog/{id}",

                        //任务中心
                        //任务列表
                        "api/externalTask/queryTaskList",
                        //任务统计列表
                        "api/externalTask/queryTaskStatisticsList",
                        //任务详情
                        "api/externalTask/queryTaskDetail/{taskId}",
                        //任务处理
                        "api/externalTask/dispose",
                        "api/externalTask/transferTask",
                        "api/externalTask/exportTaskStatisticsList",


                        //支付通道文案
                        //支付通道文案列表
                        "api/paymentChannel/queryPaymentChannelList",
                        //详情
                        "api/paymentChannel/getPaymentChannelDetail/{id}",
                        //新增
                        "/api/paymentChannel/addPaymentChannel",
                        //修改
                        "/api/paymentChannel/updatePaymentChannel",
                        //显示
                        "api/paymentChannel/startPaymentChannel/{id}",
                        //结束
                        "api/paymentChannel/suspendPaymentChannel/{id}",
                        //删除
                        "api/paymentChannel/delPaymentChannel/{id}",

                        //&lt;!&ndash;子公司发券约束配置&ndash;&gt;
                        //&lt;!&ndash;查询约束配置列表&ndash;&gt;
                        //"/api/couponOfferConfiger/list",
                        //&lt;!&ndash; 详情 &ndash;&gt;
                        //"/api/couponOfferConfiger/detail",

                        //&lt;!&ndash; 子公司发券限制 &ndash;&gt;
                        //&lt;!&ndash; 限制列表 &ndash;&gt;
                        //"api/couponOfferConfiger/list",
                        //&lt;!&ndash; 详情 &ndash;&gt;
                        //"api/couponOfferConfiger/detail",
                        //&lt;!&ndash;保存&ndash;&gt;
                        //"/api/couponOfferConfiger/save",
                        //&lt;!&ndash;批量保存&ndash;&gt;
                        //"/api/couponOfferConfiger/batchSave",
                        //&lt;!&ndash; 日志 &ndash;&gt;
                        //"api/couponOfferConfiger/logs/{orgId}",

                        //注册奖励活动(首单活动)
                        //首单活动配置新增
                        "/api/firstOrderActivity/add",
                        //首单活动配置修改
                        "/api/firstOrderActivity/update",
                        //首单活动配置详情
                        "/api/firstOrderActivity/query/{id}",
                        //首单活动配置开始
                        "/api/firstOrderActivity/start/{id}",
                        //首单活动配置暂停
                        "/api/firstOrderActivity/pause/{id}",
                        //首单活动配置恢复
                        "/api/firstOrderActivity/resume/{id}",
                        //首单活动配置停止
                        "/api/firstOrderActivity/stop/{id}",
                        //首单活动配置删除
                        "/api/firstOrderActivity/delete/{id}",

                        //红包网点活动
                        //活动新增
                        "/api/redPacketActivity/add",
                        //活动修改
                        "/api/redPacketActivity/update",
                        //活动详情
                        "/api/redPacketActivity/query/{id}",
                        //活动开始
                        "/api/redPacketActivity/start/{id}",
                        //活动暂停
                        "/api/redPacketActivity/pause/{id}",
                        //活动恢复
                        "/api/redPacketActivity/resume/{id}",
                        //活动停止
                        "/api/redPacketActivity/stop/{id}",
                        //活动删除
                        "/api/redPacketActivity/delete/{id}",

                        //客服发券活动
                        //活动新增
                        "/api/callcenterRewardActivity/add",
                        //活动修改
                        "/api/callcenterRewardActivity/update",
                        //活动详情
                        "/api/callcenterRewardActivity/query/{id}",
                        //活动开始
                        "/api/callcenterRewardActivity/start/{id}",
                        //活动暂停
                        "/api/callcenterRewardActivity/pause/{id}",
                        //活动恢复
                        "/api/callcenterRewardActivity/resume/{id}",
                        //活动停止
                        "/api/callcenterRewardActivity/stop/{id}",
                        //活动删除
                        "/api/callcenterRewardActivity/delete/{id}",


                        //口令红包活动
                        //活动新增
                        "/api/PasswordRedEnvelope/add",
                        //活动修改
                        "/api/PasswordRedEnvelope/update",
                        //活动详情
                        "/api/PasswordRedEnvelope/query/{id}",
                        //活动开始
                        "/api/PasswordRedEnvelope/start/{id}",
                        //活动暂停
                        "/api/PasswordRedEnvelope/pause/{id}",
                        //活动恢复
                        "/api/PasswordRedEnvelope/resume/{id}",
                        //活动停止
                        "/api/PasswordRedEnvelope/stop/{id}",
                        //活动删除
                        "/api/PasswordRedEnvelope/delete/{id}",


                        //订单完成奖励活动
                        //活动新增
                        "/api/orderRewardActivity/add",
                        //活动修改
                        "/api/orderRewardActivity/update",
                        //活动详情
                        "/api/orderRewardActivity/query/{id}",
                        //活动开始
                        "/api/orderRewardActivity/start/{id}",
                        //活动暂停
                        "/api/orderRewardActivity/pause/{id}",
                        //活动恢复
                        "/api/orderRewardActivity/resume/{id}",
                        //活动停止
                        "/api/orderRewardActivity/stop/{id}",
                        //活动删除
                        "/api/orderRewardActivity/delete/{id}",


                        //增值服务补偿活动
                        //活动新增
                        "/api/vehicleDeliveryActivity/add",
                        //活动修改
                        "/api/vehicleDeliveryActivity/update",
                        //活动详情
                        "/api/vehicleDeliveryActivity/query/{id}",
                        //活动开始
                        "/api/vehicleDeliveryActivity/start/{id}",
                        //活动暂停
                        "/api/vehicleDeliveryActivity/pause/{id}",
                        //活动恢复
                        "/api/vehicleDeliveryActivity/resume/{id}",
                        //活动停止
                        "/api/vehicleDeliveryActivity/stop/{id}",
                        //活动删除
                        "/api/vehicleDeliveryActivity/delete/{id}",

                        //新版本邀请活动
                        //活动新增
                        "/api/inviteActivity/add",
                        //活动修改
                        "/api/inviteActivity/update",
                        //活动详情
                        "/api/inviteActivity/query/{id}",
                        //活动开始
                        "/api/inviteActivity/start/{id}",
                        //活动暂停
                        "/api/inviteActivity/pause/{id}",
                        //活动恢复
                        "/api/inviteActivity/resume/{id}",
                        //活动停止
                        "/api/inviteActivity/stop/{id}",
                        //活动删除
                        "/api/inviteActivity/delete/{id}",


                        //活动发布
                        "/api/callcenterRewardActivity/publish/{id}",
                        "/api/redPacketActivity/publish/{id}",
                        "/api/firstOrderActivity/publish/{id}",
                        "/api/epark/publish/{id}",
                        "/api/brandActivity/publish/{id}",
                        "/api/inviteCoupon/publish/{id}",
                        "/api/eCoupon/publish/{id}",
                        "/api/thirdCoupon/publish/{id}",
                        "/api/redeemCodeActivity/publish/{id}",
                        "/api/orderShareActivity/publish/{id}",

                        //evcard-tcs
                        "api/userQuery/initLoad",
                        "api/userQuery/deleteWeChat/{infoId}/{detailId}",
                        "api/userQuery/deleteWeChat/{infoId}/{detailId}",

                        "api/taskStatistics/initLoad",
                        "api/taskStatistics/export",

                        //分享文案
                        "api/shareDoc/initLoad",
                        "api/shareDoc/saveOrUpdate",

                        //通知
                        "api/notification/initLoad",
                        "/api/notification/save",
                        //活动修改
                        "/api/notification/update",
                        //活动详情
                        "/api/notification/getDetail",
                        //活动开始
                        "/api/notification/online/{id}",
                        //活动上线
                        "/api/notification/timeOut/{id}",
                        //活动发布
                        "/api/notification/release/{id}",
                        //活动停止
                        "/api/notification/stop/{id}",
                        //活动删除
                        "/api/notification/delete/{id}",

                        //公告
                        "api/adConfig/initLoad",
                        "/api/adConfig/save",
                        //活动修改
                        "/api/adConfig/update",
                        //活动详情
                        "/api/adConfig/getDetail",
                        //活动开始
                        "/api/adConfig/online/{id}",
                        //活动上线
                        "/api/adConfig/timeOut/{id}",
                        //活动发布
                        "/api/adConfig/release/{id}",
                        //活动停止
                        "/api/adConfig/stop/{id}",
                        //活动删除
                        "/api/adConfig/delete/{id}",

                        //任务列表
                        "api/task/list",

                        //签到任务
                        //活动新增
                        "/api/signInTask/add",
                        //活动修改
                        "/api/signInTask/update",
                        //活动详情
                        "/api/signInTask/query/{id}",
                        //活动开始
                        "/api/signInTask/start/{id}",
                        //活动停止
                        "/api/signInTask/stop/{id}",
                        //活动删除
                        "/api/signInTask/delete/{id}",


                        //活动新增
                        "/api/dailyOrderTask/add",
                        //活动修改
                        "/api/dailyOrderTask/update",
                        //活动详情
                        "/api/dailyOrderTask/query/{id}",
                        //活动开始
                        "/api/dailyOrderTask/start/{id}",
                        //活动停止
                        "/api/dailyOrderTask/stop/{id}",
                        //活动删除
                        "/api/dailyOrderTask/delete/{id}",

                        //活动新增
                        "/api/recallTask/add",
                        //活动修改
                        "/api/recallTask/update",
                        //活动详情
                        "/api/recallTask/query/{id}",
                        //活动开始
                        "/api/recallTask/start/{id}",
                        //活动停止
                        "/api/recallTask/stop/{id}",
                        //活动删除
                        "/api/recallTask/delete/{id}",

                        //卡片列表
                        "/api/vipcard/card/list",
                        //卡片新增
                        "/api/vipcard/card/add",
                        //卡片修改
                        "/api/vipcard/card/update",
                        //卡片详情
                        "/api/vipcard/card/info/{id}",
                        //卡片启用
                        "/api/vipcard/card/enable/{id}",
                        //卡片禁用
                        "/api/vipcard/card/disable/{id}",


                        //卡片销售活动列表
                        "/api/vipcard/activity/list",
                        //活动新增
                        "/api/vipcard/activity/add",
                        //活动修改
                        "/api/vipcard/activity/update",
                        //活动详情
                        "/api/vipcard/activity/info/{id}",
                        //活动删除
                        "/api/vipcard/activity/delete/{id}",
                        //活动发布
                        "/api/vipcard/activity/publish/{id}",
                        //活动开始
                        "/api/vipcard/activity/start/{id}",
                        //活动停止
                        "/api/vipcard/activity/stop/{id}",
                        //导入送卡
                        "/api/vipcard/userCard/import",
                        //随享卡退款日志
                        "/api/vipcard/card/refund/log",
                        //随享卡退款
                        "/api/vipcard/card/refund/{cardUseId}",


                        //邀请返现管理
                        //现金结算记录查询
                        "/api/mmpUserBonus/queryPage",

                        //现金奖励审核记录
                        //查询
                        "/api/mmpUserAudit/queryPage",
                        //详情
                        "/api/mmpUserAudit/getBonusReocords",
                        //审核通过
                        "/api/mmpUserAudit/success",
                        //审核拒绝
                        "/api/mmpUserAudit/bathRefuse",
                        //单例拒绝
                        "/api/mmpUserAudit/refuse/{id}",

                        //会员提现记录
                        //查询
                        "/api/mmpUserBonusReceive/queryPage",
                        //详情
                        "/api/mmpUserBonusReceive/getReceiveRecordInfo/{paymentSeq}",
                        //导出
                        "/api/mmpUserBonusReceive/export",
                        //补发奖励
                        "/api/mmpUserBonusReceive/manualSendReward/{id}",


                        //邀请黑名单管理
                        //查询
                        "/api/membershipBlacklist/query",
                        //添加
                        "/api/membershipBlacklist/add",
                        //详情
                        "/api/membershipBlacklist/getUserInfo/{userMobile}",
                        //审核
                        "/api/membershipBlacklist/audit/{id}",
                        //删除
                        "/api/membershipBlacklist/delete/{id}",

                        //年度账单
                        //详情
                        "/api/annualSummaryActivity/query/{id}",
                        //添加
                        "/api/annualSummaryActivity/add",
                        //删除
                        "/api/annualSummaryActivity/delete/{id}",
                        //修改
                        "/api/annualSummaryActivity/update",
                        //暂停
                        "/api/annualSummaryActivity/pause/{id}",
                        //发布
                        "/api/annualSummaryActivity/publish/{id}",
                        //恢复
                        "/api/annualSummaryActivity/resume/{id}",
                        //启动
                        "/api/annualSummaryActivity/start/{id}",
                        //停止
                        "/api/annualSummaryActivity/stop/{id}",

                        // 短信营销
                        // 审批短信
                        "/api/actSmsSendAudit/batchAudit",

                        // 金刚区配置
                        "/api/quickLinksConfig/query",
                        "/api/quickLinksConfig/update",
                        "/api/quickLinksConfig/logs",

                        // 顶部banner配置
                        "/api/bookOrderHomePage/addBookAppHomeConfig",
                        "/api/bookOrderHomePage/getBookAppHomeConfigList",
                        "/api/bookOrderHomePage/getBookAppHomeConfigDetail",
                        "/api/bookOrderHomePage/updateBookAppHomeConfig",
                        "/api/bookOrderHomePage/offLineBookAppHomeConfig",
                        "/api/bookOrderHomePage/searchBookWaitReviewConfigList",
                        "/api/bookOrderHomePage/bookReviewAppHomeConfig"
                        );
    }

}
