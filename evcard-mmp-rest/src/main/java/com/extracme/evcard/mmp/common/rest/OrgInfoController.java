package com.extracme.evcard.mmp.common.rest;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.bo.VehicleQueryBO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import com.extracme.evcard.mmp.dto.OrgInfoDTO;
import com.extracme.evcard.mmp.dto.OrgTreeDTO;
import com.extracme.evcard.mmp.service.IOrgInfoService;
import com.extracme.evcard.mmp.vo.OrgTreeVO;
import com.extracme.framework.core.vo.DefaultWebRespVO;


@Api(value="orgManager", tags = "U3-组织机构管理")
@RestController
@RequestMapping(value="api")
public class OrgInfoController {

    @Resource
    IOrgInfoService orgInfoServiceImpl;



    /**
     * 获取子公司机构树.<br>
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value="获取全部子公司机构树", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "classType", value = "机构类别", required = true, paramType = "path", dataType = "Integer")})
    @ResponseBody
    @RequestMapping(value="orgTree/{classType}",method = {RequestMethod.GET})
    public DefaultWebRespVO subOrgTree(@PathVariable Integer classType, HttpServletRequest request) {
        List<OrgTreeDTO> orgTree = orgInfoServiceImpl.subOrgTree(classType, request.getRemoteUser());
        List<OrgTreeVO> orgTreeVO = copyTreeToVO(orgTree);
        return DefaultWebRespVO.getSuccessVO(orgTreeVO);
    }

    /**
     * 构建树vo.<br>
     * @param orgTree
     * @return
     * <AUTHOR>
     */
    private List<OrgTreeVO> copyTreeToVO(List<OrgTreeDTO> orgTree) {
        List<OrgTreeVO> orgTreeVOs = new ArrayList<OrgTreeVO>();
        for(OrgTreeDTO dto:orgTree) {
            OrgTreeVO orgTreeVO = new OrgTreeVO();
            orgTreeVO.setOrgId(dto.getOrgId());
            orgTreeVO.setOrgName(dto.getOrgName());
            if(CollectionUtils.isNotEmpty(dto.getSuns())) {
                orgTreeVO.setSuns(copyTreeToVO(dto.getSuns()));
            }
            orgTreeVOs.add(orgTreeVO);
        }
        if(CollectionUtils.isEmpty(orgTreeVOs)) {
            return null;
        }
        return orgTreeVOs;
    }
    
    /**
     * 通过组织机构获取车牌
     * @param orgId
     * @param request
     * @return
     */
    @ApiOperation(value="通过组织机构获取车牌", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "orgId", value = "机构id", required = true, paramType = "path", dataType = "String")})
    @ResponseBody
    @RequestMapping(value="getVehicleNoByOrgId/{orgId}",method = {RequestMethod.GET})
    public  DefaultWebRespVO getVehicleNoByOrgId(@PathVariable String orgId, HttpServletRequest request){
    	String vehicleNo = orgInfoServiceImpl.getVehicleNoByOrgId(orgId, request.getRemoteUser());
		return DefaultWebRespVO.getSuccessVO(vehicleNo);
    	
    }

    /**
     * 查询车牌列表.<br>
     * @param vehicleQueryBO 车牌前缀，查询全部时不传
     * @param request
     * @return
     */
    @ApiOperation(value="查询车牌列表", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value="getVehicleNoList",method = RequestMethod.POST)
    public DefaultWebRespVO getOrgVehicleNo(@RequestBody VehicleQueryBO vehicleQueryBO, HttpServletRequest request) {
        List<String> vehicleNoList = orgInfoServiceImpl.getOrgVehicleNo(vehicleQueryBO, request.getRemoteUser());
        return DefaultWebRespVO.getSuccessVO(vehicleNoList);
    }

    @ApiOperation(value="获取指定orgId的机构树", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "orgId", value = "机构id", required = true, paramType = "path", dataType = "String")})
    @ResponseBody
    @RequestMapping(value="getOrgIdTree/{orgId}",method = {RequestMethod.GET})
    public  DefaultWebRespVO getOrgIdTree(@PathVariable String orgId, HttpServletRequest request){
    	List<OrgInfoDTO> orgIdTree = orgInfoServiceImpl.getOrgIdTree(orgId, request.getRemoteUser());
		return DefaultWebRespVO.getSuccessVO(orgIdTree);
    	
    }

    /**
     * 根据所属公司查询城市（运营机构，非会员所属）
     *
     * @param orgId 所属公司
     * @return
     */
    @ApiOperation(value="获取指定orgId的关联城市列表", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "orgId", value = "机构id", required = true, paramType = "query", dataType = "String")})
    @RequestMapping(value = "org/queryOrgCityByOrgId", method = RequestMethod.GET)
    public DefaultWebRespVO queryOrgCityByOrgId(String orgId) {
        List list = orgInfoServiceImpl.queryOrgCityByOrgId(orgId);
        return DefaultWebRespVO.getSuccessVO(list);
    }
}
