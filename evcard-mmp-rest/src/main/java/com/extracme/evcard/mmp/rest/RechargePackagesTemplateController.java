package com.extracme.evcard.mmp.rest;

import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.bo.RechargeCouponModelParamsBO;
import com.extracme.evcard.mmp.bo.RechargePackagesTemplateBO;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.service.IRechargePackagesTemplateService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * \* Created with IntelliJ IDEA. \* User: Elin \* Date: 2018/4/19 \* Time: 9:43
 * \* To change this template use File | Settings | File Templates. \*
 * Description:充值套餐模板配置 \
 */
@Api(value="recharge", tags = "充值套餐模板配置")
@RestController
@RequestMapping("api/recharge")
public class RechargePackagesTemplateController {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    IRechargePackagesTemplateService rechargePackagesTemplateServiceImpl;

    /**
     * 新增充值套餐模板配置信息
     *
     * @param rechargePackagesTemplateFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="新增充值套餐模板配置信息", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "addRechargePackagesTemplate", method = RequestMethod.POST)
    public DefaultWebRespVO addRechargePackagesTemplate(
            @RequestBody RechargePackagesTemplateFullDTO rechargePackagesTemplateFullDTO, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO =
                    rechargePackagesTemplateServiceImpl.addRechargePackagesTemplate(rechargePackagesTemplateFullDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("新增充值套餐模板配置信息...");
        return vo;
    }

    /**
     * 修改充值套餐模板配置信息
     *
     * @param rechargePackagesTemplateFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="修改充值套餐模板配置信息", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @RequestMapping(value = "updateRechargePackagesTemplate", method = RequestMethod.PUT)
    public DefaultWebRespVO updateRechargePackagesTemplate(
            @RequestBody RechargePackagesTemplateFullDTO rechargePackagesTemplateFullDTO, HttpServletRequest request) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            DefaultServiceRespDTO respDTO =
                    rechargePackagesTemplateServiceImpl.updateRechargePackagesTemplate(rechargePackagesTemplateFullDTO, request);
            if (respDTO.getCode() == -1) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        } catch (Exception e) {
            e.printStackTrace();
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
            return vo;
        }
        vo.setMessage("提交成功");
        log.debug("修改充值套餐模板配置信息...");
        return vo;
    }

    /**
     * 获取套餐模板配置详情信息
     *
     * @param packagesId 套餐模板Id
     * @return
     */
    @ApiOperation(value="获取套餐模板配置详情信息", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "getRechargePackagesTemplateDetail/{packagesId}", method = RequestMethod.GET)
    public DefaultWebRespVO getRechargePackagesTemplateDetail(@PathVariable("packagesId") Long packagesId) {
        RechargePackagesTemplateDetailDTO packagesTemplateDetail =
                rechargePackagesTemplateServiceImpl.getRechargePackagesTemplateDetail(packagesId);
        log.debug("获取套餐模板配置详情信息...");
        return DefaultWebRespVO.getSuccessVO(packagesTemplateDetail);
    }

    /**
     * 送券套餐列表
     *
     * @param packagesTemplateBO
     * @return
     */
    @ApiOperation(value="送券套餐列表", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "queryRechargePackagesTemplateList", method = RequestMethod.POST)
    public DefaultWebRespVO queryRechargePackagesTemplateList(@RequestBody RechargePackagesTemplateBO packagesTemplateBO) {
        PageBeanBO<RechargePackagesTemplatePageDTO> pageBeanBO =
                rechargePackagesTemplateServiceImpl.queryRechargePackagesTemplateList(packagesTemplateBO);
        log.debug("获取套餐模板配置信息列表...");
        return DefaultWebRespVO.getSuccessVO(pageBeanBO);
    }

    /**
     * 启用禁用配置
     *
     * @param packagesId
     * @param packagesStatus
     * @param request
     * @return
     */
    @ApiOperation(value="启用禁用配置", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "packagesId", value = "套餐id", required = true, paramType = "path", dataType = "Long"),
            @ApiImplicitParam(name = "packagesStatus", value = "套餐状态", required = true, paramType = "path")
    })
    @RequestMapping(value = "updateRechargePackagesStatus/{packagesId}/{packagesStatus}", method = RequestMethod.PUT)
    public DefaultWebRespVO updateRechargePackagesStatus(@PathVariable("packagesId") Long packagesId,
                                                         @PathVariable("packagesStatus") Integer packagesStatus, HttpServletRequest request) {
        DefaultServiceRespDTO respDTO =
                rechargePackagesTemplateServiceImpl.updateRechargePackagesStatus(packagesId, packagesStatus, request);
        if (respDTO.getCode() == -1) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
        }
        log.debug("启用禁用配置...");
        return DefaultWebRespVO.SUCCESS;
    }

    /**
     * 获取套餐优惠券模板分页
     *
     * @param paramsBO 套餐id
     * @return
     */
    @ApiOperation(value="获取套餐优惠券模板分页", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "getRechargePackagesCouponPage", method = RequestMethod.POST)
    public DefaultWebRespVO getRechargePackagesCouponPage(@RequestBody RechargeCouponModelParamsBO paramsBO) {
        RechargeCouponModelDTO rechargeCouponModelDTO =
                rechargePackagesTemplateServiceImpl.getRechargePackagesCouponPage(paramsBO);
        log.debug("获取套餐优惠券模板分页...");
        return DefaultWebRespVO.getSuccessVO(rechargeCouponModelDTO);
    }

    /**
     * 查询日志列表
     *
     * @param packagesId
     * @param request
     * @return
     */
    @ApiOperation(value="查询日志列表", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams(value = {
            @ApiImplicitParam(name = "packagesId", value = "套餐id", required = true),
            @ApiImplicitParam(name = "pageNum", value = "起始页", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页展示数量", required = true),
            @ApiImplicitParam(name = "isAll", value = "是否统计总数", required = true)
    })
    @RequestMapping(value = "queryRechargePackagesLog", method = RequestMethod.GET)
    public DefaultWebRespVO queryRechargePackagesLog(@RequestParam("packagesId") Long packagesId,
                                                     @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                     @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                     @RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
                                                     HttpServletRequest request) {
        PageBeanBO<MmpRechargePackagesLogsDTO> packagesLog =
                rechargePackagesTemplateServiceImpl.queryRechargePackagesLog(packagesId, pageNum, pageSize, isAll, request);
        log.debug("查询日志列表...");
        return DefaultWebRespVO.getSuccessVO(packagesLog);
    }

    /**
     * 查询已保存的套餐类型
     *
     * @return
     */
    @ApiOperation(value="查询已保存的套餐类型", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @RequestMapping(value = "querySavedRechargeTypes", method = RequestMethod.GET)
    public DefaultWebRespVO querySavedRechargeTypes() {
        List<String> typesList =
                rechargePackagesTemplateServiceImpl.querySavedRechargeTypes();
        log.debug("查询已保存的套餐类型...");
        return DefaultWebRespVO.getSuccessVO(typesList);
    }
}