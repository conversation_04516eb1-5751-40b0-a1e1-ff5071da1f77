package com.extracme.evcard.mmp.rest;

import java.util.List;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.PlatformAllDTO;
import com.extracme.evcard.mmp.dto.PlatformDTO;
import com.extracme.evcard.mmp.dto.PlatformOperateLogDTO;
import com.extracme.evcard.mmp.dto.PlatformQueryDTO;
import com.extracme.evcard.mmp.service.IPlatformManagementService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;

@RestController
@RequestMapping("api/platform")
public class PlatformManagementController {

	private final Logger log = LoggerFactory.getLogger(this.getClass());

	@Resource
	IPlatformManagementService platformManagementServiceImpl;
	
	/**
	 * 新增平台
	 * @param platformDTO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "addPlatform", method = RequestMethod.POST)
	public DefaultWebRespVO addPlatform(@RequestBody PlatformDTO platformDTO, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			DefaultServiceRespDTO respDTO = platformManagementServiceImpl.addPlatform(platformDTO, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (Exception e) {
			log.error("addPlatform Exception", e);
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("新增平台失败");
			return vo;
		}
		vo.setMessage("新增成功");
		return vo;
	}
	
	/**
	 * 修改平台
	 * @param platformDTO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "updatePlatform", method = RequestMethod.PUT)
	public DefaultWebRespVO updatePlatform(@RequestBody PlatformDTO platformDTO, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			DefaultServiceRespDTO respDTO = platformManagementServiceImpl.updatePlatform(platformDTO, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (Exception e) {
			log.error("updatePlatform Exception", e);
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("修改平台失败");
			return vo;
		}
		vo.setMessage("修改成功");
		return vo;
	}
	
	/**
	 * 获取平台详情信息
	 * @param id 平台ID
	 * @return
	 */
	@RequestMapping(value = "getPlatformDetail/{id}", method = RequestMethod.GET)
	public DefaultWebRespVO getPlatformDetail(@PathVariable Long id) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			PlatformDTO detail = platformManagementServiceImpl.getPlatformDetail(id);
			return DefaultWebRespVO.getSuccessVO(detail);
		} catch (Exception e) {
			log.error("getPlatformDetail Exception", e);
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("获取平台详情信息失败");
			return vo;
		}
	}
	
	/**
	 * 查询平台列表
	 * @param platformQueryDTO
	 * @return
	 */
	@RequestMapping(value = "queryPlatformList", method = RequestMethod.POST)
	public DefaultWebRespVO queryPlatformList(@RequestBody PlatformQueryDTO platformQueryDTO) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			PageBeanBO<PlatformDTO> pageBean = platformManagementServiceImpl.queryPlatformList(platformQueryDTO);
			return DefaultWebRespVO.getSuccessVO(pageBean);
		} catch (Exception e) {
			log.error("queryPlatformList Exception", e);
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("查询平台列表失败");
			return vo;
		}
	}
	
	/**
	 * 修改平台名称
	 * @param platformDTO
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "updatePlatformName", method = RequestMethod.PUT)
	public DefaultWebRespVO updatePlatformName(@RequestBody PlatformDTO platformDTO, HttpServletRequest request) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			DefaultServiceRespDTO respDTO = platformManagementServiceImpl.updatePlatformName(platformDTO, request);
			if (respDTO.getCode() == -1) {
				return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
			}
		} catch (Exception e) {
			log.error("updatePlatformName Exception", e);
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("修改平台名称失败");
			return vo;
		}
		vo.setMessage("修改成功");
		return vo;
	}
	
	/**
	 * 查询平台操作日志
	 * @param pageNum
	 * @param pageSize
	 * @param isAll
	 * @param platformId
	 * @return
	 */
	@RequestMapping(value = "getPlatformOperateLog", method = RequestMethod.GET)
	public DefaultWebRespVO getPlatformOperateLog(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
			@RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
			@RequestParam(value = "isAll", defaultValue = "0") Integer isAll,
			@RequestParam(value = "platformId", required = false) Long platformId) {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			PageBeanBO<PlatformOperateLogDTO> pageBean = platformManagementServiceImpl.getPlatformOperateLog(platformId,pageNum,pageSize,isAll);
			return DefaultWebRespVO.getSuccessVO(pageBean);
		} catch (Exception e) {
			log.error("getPlatformOperateLog Exception", e);
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("查询平台操作日志失败");
			return vo;
		}
	}
	
	/**
	 * 平台下拉框
	 * @return
	 */
	@RequestMapping(value = "queryAllPlatform", method = RequestMethod.GET)
	public DefaultWebRespVO queryAllPlatform() {
		DefaultWebRespVO vo = new DefaultWebRespVO();
		try {
			List<PlatformAllDTO> platform = platformManagementServiceImpl.queryAllPlatform();
			return DefaultWebRespVO.getSuccessVO(platform);
		} catch (Exception e) {
			log.error("queryAllPlatform Exception", e);
			vo.setCode(Contants.RETURN_ERROR_CODE);
			vo.setMessage("查询所有平台");
			return vo;
		}
	}
}
