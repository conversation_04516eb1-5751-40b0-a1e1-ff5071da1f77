//package com.extracme.evcard.mmp;
//
//import org.springframework.stereotype.Controller;
//import org.springframework.web.bind.annotation.RequestMapping;
//
///**
// * <AUTHOR>
// * @Discription
// * @date 2021/8/26
// */
//@Controller
//public class SwaggerController {
//
//    @RequestMapping("inner/swagger/v2/api-docs")
//    public String apiDoc(){
//        return "forward:/v2/api-docs";
//    }
//
//    @RequestMapping("inner/swagger/swagger-resources")
//    public String resources(){
//        return "forward:/swagger-resources";
//    }
//
//    @RequestMapping("inner/swagger/api-docs-ext")
//    public String apiDocExt(){
//        return "forward:/api-docs-ext";
//    }
//
//    @RequestMapping("inner/swagger/swagger-resources/configuration/ui")
//    public String resourceUi(){
//        return "forward:/swagger-resources/configuration/ui";
//    }
//
//    @RequestMapping("inner/swagger/swagger-resources/configuration/security")
//    public String resourceSecurity(){
//        return "forward:/swagger-resources/configuration/security";
//    }
//}
