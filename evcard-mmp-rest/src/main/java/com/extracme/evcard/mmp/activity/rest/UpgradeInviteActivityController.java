package com.extracme.evcard.mmp.activity.rest;

import com.extracme.evcard.mmp.bo.ThirdCouponModelParamsBO;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.dto.activity.*;
import com.extracme.evcard.mmp.service.IMarketActivityService;
import com.extracme.evcard.mmp.service.IUpgradeInviteActivityService;
import com.extracme.evcard.mmp.vo.BaseResultVo;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Discription
 * @date 2021/8/4
 */
@Api(value="inviteActivity", tags = "新版邀请活动[19]")
@RestController
@RequestMapping("api/inviteActivity")
public class UpgradeInviteActivityController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    IUpgradeInviteActivityService upgradeInviteActivityServiceImpl;

    @Resource
    protected IMarketActivityService marketActivityServiceImpl;

    /**
     * 活动新增
     * @param activityFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="活动创建", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "add" ,method = RequestMethod.POST)
    public BaseResultVo add(@RequestBody UpgradeInviteActivityFullDTO activityFullDTO,
                            HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = upgradeInviteActivityServiceImpl.add(activityFullDTO, request);
            if (respDTO.getCode() != 0) {
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("新增活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "新增失败");
        }
        vo.setMessage("新增活动成功");
        return vo;
    }

    /**
     * 活动更新
     * @param activityFullDTO
     * @param request
     * @return
     */
    @ApiOperation(value="活动修改", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "update" ,method = RequestMethod.POST)
    public BaseResultVo update(@RequestBody UpgradeInviteActivityFullDTO activityFullDTO,
                            HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = upgradeInviteActivityServiceImpl.update(activityFullDTO, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("更新活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "修改失败");
        }
        vo.setMessage("修改活动成功");
        return vo;
    }

    /**
     * 活动删除
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动删除", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "String")})
    @RequestMapping(value = "delete/{id}", method = RequestMethod.PUT)
    public BaseResultVo delete(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = upgradeInviteActivityServiceImpl.delete(id,request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("删除活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "删除失败");
        }
        vo.setMessage("删除活动成功");
        return vo;
    }

    /**
     * 活动暂停
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动暂停", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "pause/{id}", method = RequestMethod.PUT)
    public BaseResultVo suspend(@PathVariable("id") Long id,
                             HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = upgradeInviteActivityServiceImpl.suspend(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("暂停活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "暂停失败");
        }
        vo.setMessage("暂停活动成功");
        return vo;
    }

    /**
     * 活动停止
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动停止", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "stop/{id}", method = RequestMethod.PUT)
    public BaseResultVo stop(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = upgradeInviteActivityServiceImpl.stop(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("停止活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "停止失败");
        }
        vo.setMessage("停止活动成功");
        return vo;
    }

    /**
     * 活动启动
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动启动", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "start/{id}", method = RequestMethod.PUT)
    public BaseResultVo start(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = upgradeInviteActivityServiceImpl.start(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("启动活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "启动活动失败");
        }
        vo.setMessage("启动活动成功");
        return vo;
    }


    /**
     * 活动发布
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动发布", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "publish/{id}", method = RequestMethod.PUT)
    public BaseResultVo publish(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = upgradeInviteActivityServiceImpl.publish(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("发布活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "发布活动失败");
        }
        vo.setMessage("发布活动成功");
        return vo;
    }


    /**
     * 活动恢复
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动恢复", httpMethod = ApiConf.PUT, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "resume/{id}", method = RequestMethod.PUT)
    public BaseResultVo resume(@PathVariable("id") Long id, HttpServletRequest request){
        BaseResultVo vo = new BaseResultVo();
        try{
            DefaultServiceRespDTO respDTO = upgradeInviteActivityServiceImpl.resume(id, request);
            if (respDTO.getCode() != 0){
                return new BaseResultVo(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("恢复活动异常", e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "恢复失败");
        }
        vo.setMessage("恢复活动成功");
        return vo;
    }

    /**
     * 活动详情获取
     * @param id
     * @param request
     * @return
     */
    @ApiOperation(value="活动详情", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "query/{id}", method = RequestMethod.GET)
    public BaseResultVo<UpgradeInviteActivityDetailDTO> queryDetails(@PathVariable("id") Long id, HttpServletRequest request){
        UpgradeInviteActivityDetailDTO activityDetailDTO = upgradeInviteActivityServiceImpl.queryDetails(id);
        if (null == activityDetailDTO) {
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "活动信息不存在");
        }
        return BaseResultVo.getSuccessVO(activityDetailDTO);
    }

    /**
     * 查询活动发放优惠券统计
     * @param id
     * @return
     */
    @ApiOperation(value="活动发放优惠券统计", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @ApiImplicitParams({@ApiImplicitParam(name = "id", value = "活动id", required = true, paramType = "path", dataType = "Long")})
    @RequestMapping(value = "queryCouponStatistics/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO queryCouponStatistics(@PathVariable("id") Long id) {
        return marketActivityServiceImpl.queryFirstOrderCouponStatistics(id);
    }

    /**
     * 券模板列表
     * @param paramsBO
     * @param request
     * @return
     */
    @ApiOperation(value="券模板列表", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @RequestMapping(value = "getCouponModelPage", method = RequestMethod.POST)
    public BaseResultVo<ActivityCouponModelPageDTO> getCouponModelPage(@RequestBody ThirdCouponModelParamsBO paramsBO,
                                               HttpServletRequest request) {
        ActivityCouponModelPageDTO couponModelPage = upgradeInviteActivityServiceImpl.getCouponModelPage(paramsBO);
        if (null == couponModelPage) {
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "活动券模板信息不存在");
        }
        return BaseResultVo.getSuccessVO(couponModelPage);
    }

    @ApiOperation(value="活动短信发送日志", httpMethod = ApiConf.GET, notes = ApiConf.FINISH)
    @RequestMapping(value = "getActivitySmsLog/{activityId}", method = RequestMethod.GET)
    public BaseResultVo<ActivitySmsLogDto> getActivitySmsLog(@PathVariable("activityId") Long activityId){
        logger.info("导出新版邀请活动，活动编号activityId:{}", activityId);
        try {
            ActivitySmsLogDto activitySmsLog = upgradeInviteActivityServiceImpl.getActivitySmsLog(activityId);
            return BaseResultVo.getSuccessVO(activitySmsLog);
        } catch (Exception e) {
            logger.error("导出新版邀请活动短信发送日志失败，activityId:{}", activityId, e);
            return new BaseResultVo(Contants.RETURN_ERROR_CODE, "导出失败");
        }
    }
}
