package com.extracme.evcard.mmp.config;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.authority.dto.AuthDTO;
import com.extracme.evcard.authority.dto.SystemConfig;
import com.extracme.evcard.authority.interceptor.AuthRequired;
import com.extracme.evcard.authority.service.IAuthorityService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;


/**
 * 使用注解的方式对Rest请求进行拦截。
 * @UserAuthRequired 标注在方法上，代表这个方法需要进行拦截。
 *   当前登录用户的角色，所配置的权限，和当前访问方法上注解UserAuthRequired配置的resKey或resourcesUrl 满足即可访问
 *   （任意一个都行）
 */
@Component
public class EnhanceUserAuthInterceptor extends HandlerInterceptorAdapter {

    private static final Logger log = LoggerFactory.getLogger(EnhanceUserAuthInterceptor.class);

    private IAuthorityService authorityService;

    private SystemConfig systemConfig;

    static String content_type = "application/json";
    //00000006=无授权
    static String no_power = "{\"code\":\"00000006\",\"message\":\"无授权\"}";

    public EnhanceUserAuthInterceptor(IAuthorityService authorityService, SystemConfig systemConfig) {
        this.authorityService = authorityService;
        this.systemConfig = systemConfig;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            UserAuthRequired annotation = handlerMethod.getMethodAnnotation(UserAuthRequired.class);
            if (annotation == null) {
                return true;
            }

            log.debug("对请求进行权限验证");
            //拿到权限列表
            List<AuthDTO> auths = authorityService.powerRedis(request.getRemoteUser(), systemConfig.getAppCode(), systemConfig);
            if (auths == null || auths.size() == 0) {
                auths = authorityService.powerList(request.getRemoteUser(), systemConfig.getAppCode(), systemConfig);
            }

            //拿到当前的请求url以及方式
            String uri = request.getRequestURI();
            boolean hasPower = false;

            String resKey = annotation.resKey();
            String resourcesUrl = annotation.resourcesUrl();
            if (StringUtils.isNotBlank(resKey) || StringUtils.isNotBlank(resourcesUrl)) {
                for (AuthDTO auth : auths) {
                    // 当前访问的方法注解配置的reskey 、resourcesUrl 和 当前用户的权限比较，任意一个符合，即可访问。
                    if (StringUtils.contains(uri, auth.getResourcesUrl())
                            || StringUtils.equalsIgnoreCase(resKey,auth.getResKey())
                            || StringUtils.equalsIgnoreCase(resourcesUrl,auth.getResourcesUrl())) {
                        hasPower = true;
                        break;
                    }
                }
            }

            if (!hasPower) {
                log.error("请求无授权 " + uri + " "  + JSON.toJSONString(auths));
                response.setContentType(content_type);
                response.getOutputStream().write(no_power.getBytes());
            }
            return hasPower;
        }
        return true;
    }
}
