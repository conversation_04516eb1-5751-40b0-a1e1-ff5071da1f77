package com.extracme.evcard.mmp.config;


import com.extracme.evcard.authority.interceptor.AccessLogInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.aspectj.AspectJExpressionPointcut;
import org.springframework.aop.support.DefaultPointcutAdvisor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class AccessLogInterceptorConfig {

    @Bean
    public DefaultPointcutAdvisor initAccessLogInterceptor() {
        log.info("-=-=-=-initAccessLogInterceptor=-=-=");
        AccessLogInterceptor accessLogInterceptor = new AccessLogInterceptor();

        AspectJExpressionPointcut accessLogPointCut = new AspectJExpressionPointcut();
        accessLogPointCut.setExpression("execution(* com.extracme.evcard.*.rest.*.*(..)) and @annotation(org.springframework.web.bind.annotation.RequestMapping)");

        DefaultPointcutAdvisor advisor = new DefaultPointcutAdvisor();
        advisor.setPointcut(accessLogPointCut);
        advisor.setAdvice(accessLogInterceptor);
        return advisor;
    }

}
