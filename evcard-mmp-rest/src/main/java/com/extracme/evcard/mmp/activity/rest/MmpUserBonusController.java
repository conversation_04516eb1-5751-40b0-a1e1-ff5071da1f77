package com.extracme.evcard.mmp.activity.rest;

import com.extracme.evcard.activity.dto.MmpUserBonusInfoDTO;
import com.extracme.evcard.activity.dto.PageQueryDTO;
import com.extracme.evcard.mmp.ApiConf;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.common.DateUtils;
import com.extracme.evcard.mmp.service.IMmpUserBonusService;
import com.extracme.evcard.mmp.vo.BaseResultVo;
import com.extracme.evcard.mmp.vo.MmpUserBonusInfoVO;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.util.BeanCopyUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api(value="inviteBonus", tags = "邀请现金奖励")
@RestController
@RequestMapping("/api/mmpUserBonus")
@Slf4j
public class MmpUserBonusController {

    @Resource
    private IMmpUserBonusService mmpUserBonusService;

    @ApiOperation(value="现金奖励结算记录查询", httpMethod = ApiConf.POST, notes = ApiConf.FINISH)
    @PostMapping("/queryPage")
    public BaseResultVo<PageBeanBO<MmpUserBonusInfoDTO>> queryPage(
            @RequestBody(required = false) MmpUserBonusInfoVO vo){
        if(ObjectUtils.isEmpty(vo)){
            vo =new MmpUserBonusInfoVO();
        }
//        BeanCopyUtils.copyProperties(userBonusInfoVO,userBonusInfoDTO);
        String shareStartTime = DateUtils.getFormatDate(vo.getShareStartTime(),DateUtils.DATE_TYPE5);
        String shareShutDownTime = DateUtils.getFormatDate(vo.getShareShutDownTime(),DateUtils.DATE_TYPE5);
        MmpUserBonusInfoDTO queryDto = new MmpUserBonusInfoDTO();
        BeanCopyUtils.copyProperties(vo, queryDto);
        queryDto.setShareStartTime(shareStartTime);
        queryDto.setShareShutDownTime(shareShutDownTime);

        PageQueryDTO pageQueryDTO = new PageQueryDTO();
        pageQueryDTO.setPageNum(vo.getPageNum());
        pageQueryDTO.setPageSize(vo.getPageSize());
        log.info("提交对象：" + vo +"\n提交分页:"+pageQueryDTO);
        BaseResultVo<PageBeanBO<MmpUserBonusInfoDTO>> result = new BaseResultVo<>();
        try {
            PageBeanBO<MmpUserBonusInfoDTO> pageBeanBO = mmpUserBonusService.queryPage(queryDto, pageQueryDTO);
                result.setCode(Contants.RETURN_SUCCESS_CODE);
                result.setMessage("查询成功");
                result.setData(pageBeanBO);
        } catch (Exception e) {
            log.error("审核记录查询失败", e);
            return new BaseResultVo<>(Contants.RETURN_ERROR_CODE, "查询失败");
        }
        return result;
    }
}
