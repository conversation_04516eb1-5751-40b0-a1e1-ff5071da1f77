package com.extracme.evcard.mmp.config;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface UserAuthRequired {

    /**
     * 唯一资源key
     */
     String resKey() default  "";

    /**
     * 资源url
     */
    String resourcesUrl() default "";

}
