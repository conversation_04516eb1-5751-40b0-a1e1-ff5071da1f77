package com.extracme.evcard.mmp.rest;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.common.ComUtil;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.service.IProvisionGroupService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 问题分类管理
 */
@RestController
@RequestMapping("api/questionGroup/")
public class ProvisionGroupController {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    IProvisionGroupService provisionGroupService;

    /**
     * 新增
     * @param input
     * @param request
     * @return
     */
    @RequestMapping(value = "add" ,method = RequestMethod.POST)
    public DefaultWebRespVO add(@RequestBody ProvisionGroupAddDto input, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = provisionGroupService.add(input, request);
            if (respDTO.getCode() != 0) {
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("提交异常, input=" + JSON.toJSONString(input), e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "新增失败");
        }
        vo.setMessage("提交成功");
        return vo;
    }

    /**
     * 更新
     * @param input
     * @param request
     * @return
     */
    @RequestMapping(value = "update" ,method = RequestMethod.POST)
    public DefaultWebRespVO update(@RequestBody ProvisionGroupAddDto input, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = provisionGroupService.update(input, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("提交异常, input=" + JSON.toJSONString(input), e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "修改失败");
        }
        vo.setMessage("提交成功");
        return vo;
    }

    /**
     * 活动删除
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "delete/{id}", method = RequestMethod.PUT)
    DefaultWebRespVO delete(@PathVariable("id") Long id, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = provisionGroupService.delete(id, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("删除异常, id=" + id, e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "删除失败");
        }
        vo.setMessage("删除成功");
        return vo;
    }

    /**
     * 分类详情获取
     * @param id
     * @param request
     * @return
     */
    @RequestMapping(value = "{id}", method = RequestMethod.GET)
    public DefaultWebRespVO queryDetails(@PathVariable("id") Long id, HttpServletRequest request){
        ProvisionGroupVewDto dto = provisionGroupService.queryDetail(id);
        if (null == dto) {
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "分类信息不存在");
        }
        return DefaultWebRespVO.getSuccessVO(dto);
    }


    @RequestMapping(value = "nodes/{id}", method = RequestMethod.GET)
    public DefaultWebRespVO queryNodes(@PathVariable("id") Long id, HttpServletRequest request){
        List<ProvisionGroupNodeDtO> list = provisionGroupService.queryNodesByGroupId(id);
//        if (null == list) {
//            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "请先创建问题");
//        }
        return DefaultWebRespVO.getSuccessVO(list);
    }

    @RequestMapping(value = "nodeList", method = RequestMethod.GET)
    public DefaultWebRespVO querynodeList(HttpServletRequest request){
        List<ProvisionGroupNodeDtO> list = provisionGroupService.queryNodesByGroupId(null);
        return DefaultWebRespVO.getSuccessVO(list);
    }


    @RequestMapping(value = "rank", method = RequestMethod.PUT)
    public DefaultWebRespVO rank(@RequestBody ProvisionRankDto input, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = provisionGroupService.rank(input, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("操作失败, input=" + JSON.toJSONString(input), e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "操作失败");
        }
        vo.setMessage("操作成功");
        return vo;
    }

    @RequestMapping(value = "subRank", method = RequestMethod.PUT)
    public DefaultWebRespVO subRank(@RequestBody ProvisionRankDto input, HttpServletRequest request){
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try{
            DefaultServiceRespDTO respDTO = provisionGroupService.subRank(input, request);
            if (respDTO.getCode() != 0){
                return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, respDTO.getMessage());
            }
        }catch (Exception e) {
            logger.error("操作失败, input=" + JSON.toJSONString(input), e);
            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "操作失败");
        }
        vo.setMessage("操作成功");
        return vo;
    }


    /**
     * 问题分类列表
     *
     * @param
     * @param request
     * @return
     */
    @RequestMapping(value = "tree", method = RequestMethod.GET)
    public DefaultWebRespVO getCouponModelPage(HttpServletRequest request) {
        List<ProvisionGroupVewDto> data = provisionGroupService.queryAll();
//        if (null == data) {
//            return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动券模板信息不存在");
//        }
        return DefaultWebRespVO.getSuccessVO(data);
    }

    @RequestMapping(value = "logs", method = RequestMethod.GET)
    public DefaultWebRespVO queryChannelOperatorLogList(@RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum,
                                                        @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                        @RequestParam(value = "isAll", defaultValue = "0") Integer isAll) {
        DefaultWebRespVO vo = new DefaultWebRespVO();
        try {
            PageBeanBO<UserOperateLogDTO> pageBeanBO = provisionGroupService.queryOperateLogs(pageNum, pageSize, isAll);
            if (CollectionUtils.isEmpty(pageBeanBO.getList())) {
                vo.setCode(Contants.RETURN_ERROR_CODE);
                vo.setMessage("日志记录不存在");
                return vo;
            }
            return DefaultWebRespVO.getSuccessVO(pageBeanBO);
        } catch (Exception e) {
            logger.error(ComUtil.getExceptionMsg(e));
            vo.setCode(Contants.RETURN_ERROR_CODE);
            vo.setMessage(e.getMessage());
        }
        return vo;
    }
}
