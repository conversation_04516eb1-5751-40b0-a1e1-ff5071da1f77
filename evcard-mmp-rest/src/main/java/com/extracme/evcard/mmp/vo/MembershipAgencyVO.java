package com.extracme.evcard.mmp.vo;

import java.util.List;

/**
 * 项目名称：evcard-mmp-rest
 * 类名称：MembershipAgencyVO
 * 类描述：MembershipAgencyVO
 * 创建人：sunb-孙彬
 * 创建时间：2017年9月20日下午1:52:22
 * 修改备注
 * @version2.0
 */
public class MembershipAgencyVO {
    /** 关联企业id */
    private String agencyId;
    /** 是否免押金 0：不免押金 1：免押金 */
    private Integer exemptDeposit;
    /** 免租车押金理由 */
    private String reason;
    /** 备注来源 */
    private String infoOrigin;
    /** 批量处理的authId*/
    private List<String> authIds;
    public Integer getExemptDeposit() {
        return exemptDeposit;
    }

    public void setExemptDeposit(Integer exemptDeposit) {
        this.exemptDeposit = exemptDeposit;
    }

    public String getInfoOrigin() {
        return infoOrigin;
    }

    public void setInfoOrigin(String infoOrigin) {
        this.infoOrigin = infoOrigin;
    }

    public String getAgencyId() {
        return agencyId;
    }

    public void setAgencyId(String agencyId) {
        this.agencyId = agencyId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public List<String> getAuthIds() {
        return authIds;
    }

    public void setAuthIds(List<String> authIds) {
        this.authIds = authIds;
    }
}
