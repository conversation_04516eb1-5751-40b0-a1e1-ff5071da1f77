package com.extracme.evcard.mmp.rest;

import java.io.IOException;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.dto.*;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.extracme.evcard.mmp.bo.ThirdCouponModelParamsBO;
import com.extracme.evcard.mmp.common.Contants;
import com.extracme.evcard.mmp.service.IRechargeRecordService;
import com.extracme.framework.core.bo.PageBeanBO;
import com.extracme.framework.core.vo.DefaultWebRespVO;

@RestController
@RequestMapping("api/rechargeRecord")
public class RechargeRecordController {
	private final Logger log = LoggerFactory.getLogger(this.getClass());
	@Resource
	IRechargeRecordService rechargeRecordServiceImpl;

	/**
	 * 查询充值送券记录
	 * 
	 * @param rechargeRecordDTO
	 * @return
	 */
	@RequestMapping(value = "queryRechargeRecordList", method = RequestMethod.POST)
	public DefaultWebRespVO queryRechargeRecordList(@RequestBody RechargeRecordDTO rechargeRecordDTO) {
		if (null == rechargeRecordDTO.getActivitySeq() && StringUtils.isBlank(rechargeRecordDTO.getCity())
				&& StringUtils.isBlank(rechargeRecordDTO.getName())
				&& StringUtils.isBlank(rechargeRecordDTO.getMobilePhone())
				&& StringUtils.isBlank(rechargeRecordDTO.getRechargeStartTime())
				&& StringUtils.isBlank(rechargeRecordDTO.getRechargeEndTime())
				&& StringUtils.isBlank(rechargeRecordDTO.getOrgId())) {

			return new DefaultWebRespVO("-1","请输入查询条件");
		}
		PageBeanBO<RechargeRecordDTO> pageBeanBO = rechargeRecordServiceImpl.queryRechargeRecordList(rechargeRecordDTO);
		log.debug("查询充值送券记录...");
		return DefaultWebRespVO.getSuccessVO(pageBeanBO);
	}

	/**
	 * 充值E币活动详情信息
	 * 
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "getEOfferCouponActivityDetail/{id}", method = RequestMethod.GET)
	public DefaultWebRespVO getEOfferCouponActivityDetail(@PathVariable("id") Long id) {
		RechargeEActivityDetailDTO eActivityDetail = rechargeRecordServiceImpl.getEOfferCouponActivityDetail(id);
		if (null == eActivityDetail) {
			return new DefaultWebRespVO(Contants.RETURN_ERROR_CODE, "活动信息不存在");
		}
		log.debug("充值E币活动详情信息...");
		return DefaultWebRespVO.getSuccessVO(eActivityDetail);
	}

	/**
	 * 获取E优惠券模板信息
	 * 
	 * @param paramsBO
	 * @return
	 */
	@RequestMapping(value = "getECouponModelList", method = RequestMethod.POST)
	public DefaultWebRespVO getECouponModelList(@RequestBody ThirdCouponModelParamsBO paramsBO) {
		PageBeanBO<RechargeEActivityCouponModelDTO> eActivityCouponModelDTO = rechargeRecordServiceImpl
				.getECouponModelList(paramsBO);
		log.debug("获取E优惠券模板信息...");
		return DefaultWebRespVO.getSuccessVO(eActivityCouponModelDTO);
	}
	
	/**
	 * 查询会员E币剩余数量列表
	 * @param eRemainDTO
	 * @return
	 */
	@RequestMapping(value = "getERemainNumList", method = RequestMethod.POST)
	public DefaultWebRespVO getERemainNumList(@RequestBody EAccountQueryDto eRemainDTO) {
		PageBeanBO<EAccountInfoDto> eRemainNumList = rechargeRecordServiceImpl
				.getERemainNumList(eRemainDTO);
		log.debug("getERemainNumList, result={}...", JSON.toJSONString(eRemainNumList));
		return DefaultWebRespVO.getSuccessVO(eRemainNumList);
	}
	
	/**
	 * 获取企业会员E币剩余数量列表
	 * @param agencyERemainDTO
	 * @return
	 */
	@RequestMapping(value = "getAgencyERemainNumList", method = RequestMethod.POST)
	public DefaultWebRespVO getAgencyERemainNumList(@RequestBody AgencyERemainDTO agencyERemainDTO) {
		PageBeanBO<AgencyERemainDTO> agencyERemainNumList = rechargeRecordServiceImpl
				.getAgencyERemainNumList(agencyERemainDTO);
		log.debug("获取企业会员E币剩余数量...");
		return DefaultWebRespVO.getSuccessVO(agencyERemainNumList);
	}
	
	/**
	 * 导出充值记录列表
	 * @param rechargeRecordDTO
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "exportRechargeRecordList", method = RequestMethod.GET)
	public DefaultWebRespVO exportRechargeRecordList(RechargeRecordDTO rechargeRecordDTO,
			HttpServletRequest request, HttpServletResponse response) {
		try {
			response.setHeader("Content-Type", "application/vnd.ms-excel");
			response.setHeader("content-disposition",
					"attachment;filename=exportExternalTaskListTitle.xlsx");
			rechargeRecordServiceImpl.exportRechargeRecordList(rechargeRecordDTO,
					response.getOutputStream());
		} catch (IOException e) {
			e.printStackTrace();
		}
		return DefaultWebRespVO.SUCCESS;
	}

}
