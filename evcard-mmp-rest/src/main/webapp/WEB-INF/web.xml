
	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>classpath:applicationContext.xml</param-value>
	</context-param>
	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>
	<!--处理因使用内省API而导致的内存泄漏问题 -->
	<listener>
		<listener-class>org.springframework.web.util.IntrospectorCleanupListener</listener-class>
	</listener>



	<filter>
		<filter-name>ssoFilter</filter-name>
		<filter-class>com.extracme.evcard.sso.client.SsoFilter</filter-class>
		<init-param>
			<param-name>excludedPages</param-name>
			<param-value>*.js,*.css,*.map,api/thirdCoupon/offerThirdCoupon,inner/offerSweepActivityCoupon,inner/checkSweepActivityCoupon,inner/getSweepActivity,api/getBvmAgencyInfo,inner/offerBrandActivityCoupon,inner/getBrandActivity,inner/qrcode/getQRCodeUrl,inner/getChannelRewardActivity,inner/externalTask/create,inner/layer/getLayerActivity,inner/welcome/getWelcomeActivity</param-value>
		</init-param>
		<!--使用特殊版本sso-client处理swagger相关资源，使其不参与sso鉴权 -->
		<init-param>
			<param-name>excludedKeys</param-name>
			<param-value>doc.html,swagger,webjars,api-docs,webjars</param-value>
		</init-param>
	</filter>

	<filter-mapping>
		<filter-name>ssoFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<servlet>
		<servlet-name>dispatcherServlet</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
		<init-param>
			<param-name>contextConfigLocation</param-name>
			<param-value>classpath:dispatcher-servlet.xml</param-value>
		</init-param>
		<load-on-startup>1</load-on-startup>
		<async-supported>true</async-supported>
	</servlet>
	<servlet-mapping>
		<servlet-name>dispatcherServlet</servlet-name>
		<url-pattern>/</url-pattern>
	</servlet-mapping>


	<!-- 配置swagger-bootstrap-ui的url请求路径-->
	<servlet-mapping>
		<servlet-name>dispatcherServlet</servlet-name>
		<url-pattern>/v2/api-docs</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>dispatcherServlet</servlet-name>
		<url-pattern>/swagger-resources</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>dispatcherServlet</servlet-name>
		<url-pattern>/v2/api-docs-ext</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>dispatcherServlet</servlet-name>
		<url-pattern>/swagger-resources/configuration/ui</url-pattern>
	</servlet-mapping>
	<servlet-mapping>
		<servlet-name>dispatcherServlet</servlet-name>
		<url-pattern>/swagger-resources/configuration/security</url-pattern>
	</servlet-mapping>

</web-app>