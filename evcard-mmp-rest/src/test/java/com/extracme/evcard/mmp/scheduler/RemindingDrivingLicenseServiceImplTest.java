package com.extracme.evcard.mmp.scheduler;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.App;
import com.extracme.evcard.mmp.common.CommonConstant;
import com.extracme.evcard.mmp.dao.MembershipInfoMapper;
import com.extracme.evcard.mmp.dto.MembershipInfoDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/31
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class RemindingDrivingLicenseServiceImplTest {

    @Resource
    private RemindingDrivingLicenseServiceImpl remindingDrivingLicenseService;

    @Resource
    private MembershipInfoMapper membershipInfoMapper;

    @Test
    public void testRemindingDrivingLicenseService() {
        remindingDrivingLicenseService.execute(null);
    }


    @Test
    public void getMembershipInfoByIds() {
        Integer[] memberIds = new Integer[]{9459002,9459114};
        List<MembershipInfoDTO> list = membershipInfoMapper.getMembershipInfoByIds(memberIds, CommonConstant.MEMBERTYPES);
        System.out.println("list={}"+ JSON.toJSONString(list));
    }

}
