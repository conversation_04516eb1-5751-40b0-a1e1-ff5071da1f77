package com.extracme.evcard.mmp.service;

import com.extracme.evcard.mmp.dto.MemberReviewInput;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Discription
 * @date 2019/8/27
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration({"applicationContext.xml"})
public class MemberReviewTest {
    @Resource
    private IMmpMemberReviewService mmpMemberReviewServiceImpl;
    @Resource
    private IMemberProfileService memberProfileService;

    @Resource
    private UploadImageService uploadImageService;

    @Test
    public void testReviewFailed() {
        MemberReviewInput input = new MemberReviewInput();
        input.setAuthId("12111102006183400128");
        input.setDriverLicenseInputType("0");
        input.setReviewItems("111111111211");
        input.setReviewItemIds("A");
        input.setReviewItemName("什么什么");
        input.setReviewRemark("fff");
        input.setOperatorContent("ffff");
        DefaultServiceRespDTO result = mmpMemberReviewServiceImpl.reviewFailed(input, null);
    }

    @Test
    public void testSecretImages() {
        memberProfileService.showSecretImage("11122233324142223415", 1, null);
    }


}
