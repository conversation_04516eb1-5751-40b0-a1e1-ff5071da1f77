package com.extracme.evcard.mmp.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.App;
import com.extracme.evcard.mmp.dto.EAccountInfoDto;
import com.extracme.evcard.mmp.dto.EAccountQueryDto;
import com.extracme.evcard.mmp.dto.MemberDepositAccountDto;
import com.extracme.evcard.mmp.dto.OperatorDTO;
import com.extracme.evcard.mmp.mq.RawMemberListener;
import com.extracme.evcard.mq.bean.event.MemberRechargeEcoin;
import com.extracme.evcard.protobuf.ProtobufUtil;
import com.extracme.evcard.rpc.dto.PageBeanDto;
import com.extracme.evcard.rpc.pay.service.IDepositManageService;
import com.extracme.framework.core.bo.PageBeanBO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class EAccountServiceTest {
    @Resource
    private IMemEAccountService memEAccountServiceImpl;

    @Resource
    IRechargeRecordService  rechargeRecordServiceImpl;

    @Resource
    IPersonalDetailInfoService iPersonalDetailInfoServiceImpl;

    @Test
    public void ttt(){
        MemberDepositAccountDto tst = iPersonalDetailInfoServiceImpl.queryDepositAccount("18605558888104830847");
        System.out.println(JSON.toJSONString(tst));
    }

    @Test
    public void ttt2(){
        iPersonalDetailInfoServiceImpl.returnPersonalDeposit("18605558888104830847", 1L, "xxx");
        iPersonalDetailInfoServiceImpl.returnEnterpriseDeposit("18605558888104830847", 1L, "xxx");
    }

    @Test
    public void testGenerateSimpleOrg() throws Exception {
        try {
            OperatorDTO operatorDTO = new OperatorDTO();
            operatorDTO.setOperatorId(-1L);
            operatorDTO.setOperatorName("mmp-auto");
            memEAccountServiceImpl.generateEAccountReportByES("000T",
                    "上海国际汽车城新能源汽车运营服务有限公司", operatorDTO);

        } catch (Exception ex) {
            System.out.println(ex);
            ex.printStackTrace();
        }
    }

    @Test
    public void testGenerateAllReport() throws Exception {
        try {
            OperatorDTO operatorDTO = new OperatorDTO();
            operatorDTO.setOperatorId(-1L);
            operatorDTO.setOperatorName("mmp-auto");
            memEAccountServiceImpl.generateEAccountReportByES(operatorDTO);
        } catch (Exception ex) {
            System.out.println(ex);
            ex.printStackTrace();
        }
    }

    @Test
    public void testGetERemainNumList() throws Exception {
        try {
            EAccountQueryDto dto = new EAccountQueryDto();
            dto.setChargeBalanceMin(0D);
            dto.setSumBalanceMax(100D);
            dto.setMobilePhone(null);
            dto.setName(null);
            dto.setPresentBalanceMin(0D);
            dto.setOrgId(null);
            dto.setPageNum(1);
            dto.setPageSize(10);
            PageBeanBO<EAccountInfoDto> result = rechargeRecordServiceImpl.getERemainNumList(dto);
            System.out.println(JSON.toJSONString(result));
        } catch (Exception ex) {
            System.out.println(ex);
            ex.printStackTrace();
        }
    }

    @Autowired
    private RawMemberListener rawMemberListener;

    @Test
    public void testMemberRechargeEcoinEvent() throws Exception {
        MemberRechargeEcoin rechargeEcoin = new MemberRechargeEcoin();
        rechargeEcoin.setAuthId("110104196402153012");
        rechargeEcoin.setRechargeAmount(new BigDecimal(100));
        rechargeEcoin.setOrgId("000T09");
        rechargeEcoin.setRechargeTime(new Date());
        rechargeEcoin.setOutTradeSeq("outTradeSeq");

        byte[] body = ProtobufUtil.serializeProtobuf(rechargeEcoin);
        rawMemberListener.memberRechargeEcoinEvent(body);
    }


}
