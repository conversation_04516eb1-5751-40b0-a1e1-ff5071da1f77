package com.extracme.evcard.mmp.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.membership.core.input.SubmitIdCardInput;
import com.extracme.evcard.mmp.App;
import com.extracme.evcard.mmp.dto.MembershipDTO;
import com.extracme.evcard.mmp.dto.ShortRentActivityQueryDTO;
import com.extracme.evcard.mmp.model.MembershipInfo;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.servlet.*;
import javax.servlet.http.*;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.Principal;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Locale;
import java.util.Map;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class UpdateMembershipInfoTest {
    @Autowired
    IMembershipInfoService embershipInfoService;

    @Test
    public void update(){
        String json = "{\n" +
                "  \"idCardcBackPicUrl\": \"http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/newIdCardImg/1662514514466.jpg\",\n" +
                "  \"name\": \"赵炎焱2\",\n" +
                "  \"mail\": \"<EMAIL>\",\n" +
                "  \"gender\": null,\n" +
                "  \"mobilePhone\": \"18911110043\",\n" +
                "  \"authId\": \"18911110043091325178\",\n" +
                "  \"authKey\": null,\n" +
                "  \"address\": null,\n" +
                "  \"province\": null,\n" +
                "  \"city\": null,\n" +
                "  \"area\": null,\n" +
                "  \"drivingLicense\": \"******************\",\n" +
                "  \"drivingLicenseImgUrl\": \"http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/newDriverCardImg/1662530533983.jpg\",\n" +
                "  \"identityCardImgUrl\": null,\n" +
                "  \"reviewStatus\": \"1\",\n" +
                "  \"reviewStatusNum\": null,\n" +
                "  \"membershipType\": null,\n" +
                "  \"orgId\": null,\n" +
                "  \"agencyId\": null,\n" +
                "  \"dataOrigin\": null,\n" +
                "  \"idcardPicUrl\": \"http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/newIdCardImg/1662513332643.jpg\",\n" +
                "  \"holdIdcardPicUrl\": \"\",\n" +
                "  \"imeiChangeNum\": null,\n" +
                "  \"agencyName\": null,\n" +
                "  \"obtainDriverTimer\": \"2015-12-23\",\n" +
                "  \"licenseExpirationTime\": \"2031-12-23\",\n" +
                "  \"mobilePhone1\": null,\n" +
                "  \"infoOrigin\": \"\",\n" +
                "  \"drivingLicense1\": null,\n" +
                "  \"drivingLicense2\": null,\n" +
                "  \"reviewTime\": null,\n" +
                "  \"reviewMode\": null,\n" +
                "  \"faceRecognitionImgUrl\": \"\",\n" +
                "  \"userLevel\": null,\n" +
                "  \"status\": null,\n" +
                "  \"authenticationStatusName\": null,\n" +
                "  \"authenticationStatus\": 0,\n" +
                "  \"userType\": 1,\n" +
                "  \"drivingLicenseType\": \"C1\",\n" +
                "  \"registerArea\": null,\n" +
                "  \"provinceOfOrigin\": null,\n" +
                "  \"cityOfOrigin\": null,\n" +
                "  \"areaOfOrigin\": null,\n" +
                "  \"userTypeName\": \"大陆用户\",\n" +
                "  \"national\": \"中国\",\n" +
                "  \"needFace\": null,\n" +
                "  \"needReexamine\": null,\n" +
                "  \"reexamineStatus\": null,\n" +
                "  \"driverLicenseInputType\": null,\n" +
                "  \"homeAddress\": null,\n" +
                "  \"companyAddress\": null,\n" +
                "  \"membershipAdditionalDetailDTO\": null,\n" +
                "  \"applyStatus\": 0,\n" +
                "  \"mailAddress\": null,\n" +
                "  \"unregisterTime\": null,\n" +
                "  \"accountStatus\": 0,\n" +
                "  \"authenticationOrigin\": \"\",\n" +
                "  \"driveLicense\": null,\n" +
                "  \"fileNo\": \"************\",\n" +
                "  \"fileNoImgUrl\": \"\",\n" +
                "  \"licenseAuthStatus\": null,\n" +
                "  \"licenseAuthUpdateTime\": null,\n" +
                "  \"licAuthStatus\": null,\n" +
                "  \"profession\": null,\n" +
                "  \"educational\": null,\n" +
                "  \"ownCar\": null,\n" +
                "  \"idCardAuthStatus\": null,\n" +
                "  \"licenseReviewStatus\": null,\n" +
                "  \"identityId\": null,\n" +
                "  \"mid\": null,\n" +
                "  \"idType\": \"1\",\n" +
                "  \"passportNo\": \"110101199003072332\",\n" +
                "  \"idCardExpirationTime\": \"2041-01-04 00:00:00\",\n" +
                "  \"idCardExpirationType\": 1,\n" +
                "  \"idCardNumber\": \"******************\"\n" +
                "}";

        MembershipDTO input = JSON.parseObject(json, MembershipDTO.class);
        try {
            DefaultServiceRespDTO dto = embershipInfoService.updateMembershipInfo("18911110043091325178", input, null);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    @Test
    public void testChannel1() throws Exception {
//        {
//            "idCardcBackPicUrl": "http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/driving_license/***********153409149/*************.jpg",
//                "name": "测试55",
//                "mobilePhone": "***********",
//                "authId": "***********153409149",
//
//                "drivingLicense": "************073134",
//                "drivingLicenseImgUrl": "http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/driving_license/***********153409149/*************.jpg",
//
//                "reviewStatus": "1",
//
//                "idcardPicUrl": "https://evcard.oss-cn-shanghai.aliyuncs.com/test/https://evcard.oss-cn-shanghai.aliyuncs.com/test/http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/driving_license/***********153409149/*************.jpg",
//                "holdIdcardPicUrl": "https://evcard.oss-cn-shanghai.aliyuncs.com/test/https://evcard.oss-cn-shanghai.aliyuncs.com/test/http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/driving_license/***********153409149/*************.jpg",
//
//                "obtainDriverTimer": "2022-09-01",
//                "licenseExpirationTime": -1,
//
//                "faceRecognitionImgUrl": "http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/holdIcCardPic/***********153409149/*************.jpg",
//
//                "authenticationStatus": 0,
//                "userType": 1,
//                "drivingLicenseType": "C1",
//
//                "userTypeName": "大陆用户",
//                "national": "中国",
//
//                "applyStatus": 0,
//
//                "accountStatus": 0,
//
//                "fileNo": "************",
//                "fileNoImgUrl": "http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/driving_license/***********153409149/*************.jpg",
//
//                "idType": "1",
//                "passportNo": "************073134",
//                "idCardExpirationTime": -1,
//                "idCardExpirationType": 2,
//                "idCardNumber": "************073134"
//        }
        try {
            MembershipInfo vo = new MembershipInfo();
            vo.setOrgId("00");
            String authId = "***********153409149";
            MembershipDTO dto = new MembershipDTO();
            dto.setAccountStatus(0);
            dto.setReviewStatus(1);
            dto.setIdCardcBackPicUrl("http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/driving_license/***********153409149/*************.jpg");
            dto.setMobilePhone("***********");
            dto.setName("测试55");
            dto.setAuthId("***********153409149");
            dto.setDrivingLicense("************073134");
            dto.setDrivingLicenseImgUrl("http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/driving_license/***********153409149/*************.jpg");
            dto.setIdcardPicUrl("http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/driving_license/***********153409149/*************.jpg");
            dto.setHoldIdcardPicUrl("http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/driving_license/***********153409149/*************.jpg");
            dto.setObtainDriverTimer("2022-09-01");
            dto.setLicenseExpirationTime("-1");
            dto.setFaceRecognitionImgUrl("http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/holdIcCardPic/***********153409149/*************.jpg");
            dto.setAccountStatus(2);
            dto.setUserType("1");
            dto.setDrivingLicenseType("C1");
            dto.setUserTypeName("大陆用户");
            dto.setNational("中国");
            dto.setFileNo("************");
            dto.setFileNoImgUrl("http://evcard-sensitive.oss-cn-shanghai.aliyuncs.com/test/sensitiveBucket/driving_license/***********153409149/*************.jpg");
            dto.setPassportNo("************073134");
            dto.setIdCardExpirationTime("-1");
            dto.setIdCardNumber("************073134");
            dto.setIdCardExpirationType(2);
            dto.setIdType("1");

            HttpServletRequest request = new HttpServletRequest() {
                @Override
                public String getAuthType() {
                    return null;
                }

                @Override
                public Cookie[] getCookies() {
                    return new Cookie[0];
                }

                @Override
                public long getDateHeader(String name) {
                    return 0;
                }

                @Override
                public String getHeader(String name) {
                    return null;
                }

                @Override
                public Enumeration<String> getHeaders(String name) {
                    return null;
                }

                @Override
                public Enumeration<String> getHeaderNames() {
                    return null;
                }

                @Override
                public int getIntHeader(String name) {
                    return 0;
                }

                @Override
                public String getMethod() {
                    return null;
                }

                @Override
                public String getPathInfo() {
                    return null;
                }

                @Override
                public String getPathTranslated() {
                    return null;
                }

                @Override
                public String getContextPath() {
                    return null;
                }

                @Override
                public String getQueryString() {
                    return null;
                }

                @Override
                public String getRemoteUser() {
                    return "17687374990@hq";
                }

                @Override
                public boolean isUserInRole(String role) {
                    return false;
                }

                @Override
                public Principal getUserPrincipal() {
                    return null;
                }

                @Override
                public String getRequestedSessionId() {
                    return null;
                }

                @Override
                public String getRequestURI() {
                    return null;
                }

                @Override
                public StringBuffer getRequestURL() {
                    return null;
                }

                @Override
                public String getServletPath() {
                    return null;
                }

                @Override
                public HttpSession getSession(boolean create) {
                    return null;
                }

                @Override
                public HttpSession getSession() {
                    return null;
                }

                @Override
                public String changeSessionId() {
                    return null;
                }

                @Override
                public boolean isRequestedSessionIdValid() {
                    return false;
                }

                @Override
                public boolean isRequestedSessionIdFromCookie() {
                    return false;
                }

                @Override
                public boolean isRequestedSessionIdFromURL() {
                    return false;
                }

                @Override
                public boolean isRequestedSessionIdFromUrl() {
                    return false;
                }

                @Override
                public boolean authenticate(HttpServletResponse response) throws IOException, ServletException {
                    return false;
                }

                @Override
                public void login(String username, String password) throws ServletException {

                }

                @Override
                public void logout() throws ServletException {

                }

                @Override
                public Collection<Part> getParts() throws IOException, ServletException {
                    return null;
                }

                @Override
                public Part getPart(String name) throws IOException, ServletException {
                    return null;
                }

                @Override
                public <T extends HttpUpgradeHandler> T upgrade(Class<T> handlerClass) throws IOException, ServletException {
                    return null;
                }

                @Override
                public Object getAttribute(String name) {
                    return null;
                }

                @Override
                public Enumeration<String> getAttributeNames() {
                    return null;
                }

                @Override
                public String getCharacterEncoding() {
                    return null;
                }

                @Override
                public void setCharacterEncoding(String env) throws UnsupportedEncodingException {

                }

                @Override
                public int getContentLength() {
                    return 0;
                }

                @Override
                public long getContentLengthLong() {
                    return 0;
                }

                @Override
                public String getContentType() {
                    return null;
                }

                @Override
                public ServletInputStream getInputStream() throws IOException {
                    return null;
                }

                @Override
                public String getParameter(String name) {
                    return null;
                }

                @Override
                public Enumeration<String> getParameterNames() {
                    return null;
                }

                @Override
                public String[] getParameterValues(String name) {
                    return new String[0];
                }

                @Override
                public Map<String, String[]> getParameterMap() {
                    return null;
                }

                @Override
                public String getProtocol() {
                    return null;
                }

                @Override
                public String getScheme() {
                    return null;
                }

                @Override
                public String getServerName() {
                    return null;
                }

                @Override
                public int getServerPort() {
                    return 0;
                }

                @Override
                public BufferedReader getReader() throws IOException {
                    return null;
                }

                @Override
                public String getRemoteAddr() {
                    return null;
                }

                @Override
                public String getRemoteHost() {
                    return null;
                }

                @Override
                public void setAttribute(String name, Object o) {

                }

                @Override
                public void removeAttribute(String name) {

                }

                @Override
                public Locale getLocale() {
                    return null;
                }

                @Override
                public Enumeration<Locale> getLocales() {
                    return null;
                }

                @Override
                public boolean isSecure() {
                    return false;
                }

                @Override
                public RequestDispatcher getRequestDispatcher(String path) {
                    return null;
                }

                @Override
                public String getRealPath(String path) {
                    return null;
                }

                @Override
                public int getRemotePort() {
                    return 0;
                }

                @Override
                public String getLocalName() {
                    return null;
                }

                @Override
                public String getLocalAddr() {
                    return null;
                }

                @Override
                public int getLocalPort() {
                    return 0;
                }

                @Override
                public ServletContext getServletContext() {
                    return null;
                }

                @Override
                public AsyncContext startAsync() throws IllegalStateException {
                    return null;
                }

                @Override
                public AsyncContext startAsync(ServletRequest servletRequest, ServletResponse servletResponse) throws IllegalStateException {
                    return null;
                }

                @Override
                public boolean isAsyncStarted() {
                    return false;
                }

                @Override
                public boolean isAsyncSupported() {
                    return false;
                }

                @Override
                public AsyncContext getAsyncContext() {
                    return null;
                }

                @Override
                public DispatcherType getDispatcherType() {
                    return null;
                }
            };
            embershipInfoService.updateMembershipInfo(authId,dto,request);
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
    }
}
