package com.extracme.evcard.mmp.service;

import com.extracme.evcard.mmp.App;
import com.extracme.evcard.mmp.dto.activity.OrderRewardActivityFullDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class OrderRewardActivityServiceTest {

    @Resource
    IOrderRewardActivityService orderRewardActivityService;

    @Test
    public void testAdd(){

        OrderRewardActivityFullDTO dto = new OrderRewardActivityFullDTO();
        dto.setOrderDuration(50);
        dto.setOrderAmount(new BigDecimal("10"));
        dto.setPickupStartTime("10:00");
        dto.setEndTime("18:00");
        dto.setReturnStartTime("10:00");
        dto.setReturnEndTime("18:00");
        dto.setReturnDaysOfWeek("1");
        dto.setCouponWay(0);
        orderRewardActivityService.add(dto,null);

    }


}
