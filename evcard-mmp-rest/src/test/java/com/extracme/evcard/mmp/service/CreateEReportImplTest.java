package com.extracme.evcard.mmp.service;


import com.extracme.evcard.mmp.dto.OperatorDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration({"applicationContext.xml"})
public class CreateEReportImplTest {
    @Resource
    IAgencyEAccountService agencyEAccountServiceImpl;

    @Test
    public void testCreateAgencyEReport() throws Exception {
        try{
            OperatorDTO operatorDTO = new OperatorDTO();
            operatorDTO.setOperatorId(-1L);
            operatorDTO.setOperatorName("mmp-auto");
            //企业E币报表生成
            agencyEAccountServiceImpl.generateERaminReport(operatorDTO);
        }catch(Exception ex){
            System.out.println(ex.getMessage());
        }
    }
}
