package com.extracme.evcard.mmp.service;

import com.extracme.evcard.mmp.App;
import com.extracme.evcard.mmp.common.DateUtils;
import com.extracme.evcard.mmp.dto.ProvisionNodeDTO;
import com.extracme.evcard.mmp.dto.ProvisionNodesDTO;
import com.extracme.evcard.mmp.service.store.StoreService;
import com.extracme.evcard.mmp.service.vipcard.IMmpUserCardService;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MemberVipcardTest {


    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Resource
    IMmpUserCardService mmpUserCardService;


    @Resource
    private StoreService storeService;


    @Test
    public void refreshBusinessCards() throws Exception {
        mmpUserCardService.refreshBusinessCards();
    }

    @Test
    public void getStoreListByOrgCity() throws Exception {
        storeService.getStoreListByOrgCity("000T", new ArrayList<>());
//        CardPurchaseListQueryDto queryDto = new CardPurchaseListQueryDto();
//        mmpUserCardService.exportCardPurchaseList(queryDto, null);
    }

    @Test
    public void importUserCards() throws Exception {
        Long id = 100076L;
        List<String> userKeys = Arrays.asList("***********");
        mmpUserCardService.importUserCards(id, userKeys, 1, 1, OperatorDto.getSystemOp());
    }
}
