package com.extracme.evcard.mmp.service;

import com.extracme.evcard.mmp.App;
import com.extracme.evcard.mmp.common.MessageUtil;
import com.extracme.evcard.mmp.consts.WarnConsts;
import com.extracme.evcard.mmp.dao.MembershipInfoMapper;
import com.extracme.evcard.mmp.dto.MembershipDetailDTO;
import com.extracme.evcard.mmp.common.PropertyUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;

//@RunWith(SpringJUnit4ClassRunner.class)
//@ContextConfiguration({"applicationContext.xml"})
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class PropertiesTest {

    @Test
    public void testProperties(){
        System.out.println(WarnConsts.NO_VALUE.getMessage());
    }


    @Resource
    MembershipInfoMapper membershipInfoMapper;
    @Test
    public void testDbProperties() {
        String authId = "18000000042153358007";
        Map<String, String> empMap = new HashMap<String, String>();
        empMap.put("authId", authId);
        empMap.put("membershipType", "0");
        List<MembershipDetailDTO> memBer = membershipInfoMapper.querydetail(empMap);
        List<MembershipDetailDTO> member = membershipInfoMapper.queryUserDetail(authId);

        System.out.println(PropertyUtils.getProperty("evcard.bdp.dns"));
        System.out.println(PropertyUtils.getProperty("golden_level_consume"));
    }
}