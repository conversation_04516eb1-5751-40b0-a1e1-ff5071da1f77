
package com.extracme.evcard.mmp.service;

import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;


import com.extracme.evcard.mmp.es.EsClientService;
import com.extracme.evcard.mmp.es.EsResponse;
import com.extracme.evcard.mmp.es.dto.EsSearchDTO;

/**
 * Title: 
 * @date 2018年8月15日
 * @since
 *
 */

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration({"applicationContext.xml"})
public class ESSearchTest {
	
    @Autowired
    private EsClientService esClientService;
	//MemberReviewServiceImpl memberReviewServiceImpl;
    
    private static final String[] INCLUDE_FIELDS = new String[]{"auth_id"};
    
	@Test
	public void testTerm() throws Exception {
		Integer a = MmpMemberReviewServiceImpl.getNoPassAuthStatus("111111111121");


		System.out.println("---------------------------" + a);
//		BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
//	   //boolBuilder.must(QueryBuilders.termQuery("city_of_origin", "青岛市"));
//		boolBuilder.must(QueryBuilders.termQuery("card_type", 1));
//	   doesSearch(boolBuilder);
   }
	
	@Test
	public void testMatch() throws Exception {
		System.out.println("---------------------------" + "testTerm");
		BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
		boolBuilder.must(QueryBuilders.termQuery("card_type", Integer.valueOf(1)));
		   doesSearch(boolBuilder);
   }
	
	@Test
	public void testTermsRange1() throws Exception {
		System.out.println("---------------------------" + "testTermsRange1");
		BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
	   List<String> cityList = Arrays.asList("青岛市","广州市");
	   boolBuilder.must(QueryBuilders.termsQuery("city_of_origin", cityList));
	   boolBuilder.must(getTimeRangeBuilder("reg_time", null, "20180820000000"));  	
   	
	   doesSearch(boolBuilder);
   }
	@Test
	public void testTermsRange2() throws Exception {
		testName("张");
		testName("张三");
		testName("ALIEV AFGAN");
		testName("ALIEV");
		testName("哈南·塔哈·阿里");
		testName("哈南");
		testName("塔哈");
   }

	public void testName(String name) throws Exception {
		System.out.println("---------------------------" + "testName");
		BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
		//List<String> cityList = Arrays.asList("上海市","广州市");
		//ALIEV AFGAN
		boolBuilder.must(QueryBuilders.matchQuery("name", name));
//	   boolBuilder.must(QueryBuilders.termsQuery("city_of_origin", cityList));
//	   boolBuilder.must(getTimeRangeBuilder("reg_time", "2018082000000", null));

		doesSearch(boolBuilder);
	}
	
    private QueryBuilder getTimeRangeBuilder(String key, String startTime, String endTime){
    	RangeQueryBuilder rangBuilder = QueryBuilders.rangeQuery(key);
    	if(StringUtils.isNotEmpty(startTime)){
    		rangBuilder.gte(startTime);
    	}
    	if(StringUtils.isNotEmpty(endTime)){
    		rangBuilder.lt(endTime);
    	}
    	return rangBuilder;
    }
	
	@Test
	public void testTerms() throws Exception {
		System.out.println("---------------------------" + "testTerms");
		BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
	   List<String> cityList = Arrays.asList("上海市","南京市");
	   boolBuilder.must(QueryBuilders.termsQuery("city_of_origin.keyword", cityList));
	   boolBuilder.must(QueryBuilders.matchQuery("name", "张"));
		//boolBuilder.must(QueryBuilders.termQuery("mobile_phone", "13917351715"));
		//boolBuilder.must(QueryBuilders.termQuery("auth_id.keyword", "13917351715124205304"));

		//MatchPhraseQueryBuilder soldierBuilder = QueryBuilders.matchPhraseQuery("national", "中国（军人）");
		//boolBuilder.must(soldierBuilder);

	   doesSearch(boolBuilder);
   }
	
	@Test
	public void testLike() throws Exception {
		System.out.println("---------------------------" + "testLike");
		BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
		   boolBuilder.must(QueryBuilders.matchPhraseQuery("info_origin", "上海市"));
		   doesSearch(boolBuilder);
   }
	
	@Test
	public void testIds() throws Exception {
		System.out.println("---------------------------" + "testIds");
		BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
		   boolBuilder.must(QueryBuilders.matchPhraseQuery("info_origin", "东方"));
		   doesSearch(boolBuilder);
   }
	
	private void doesSearch(BoolQueryBuilder boolBuilder) throws Exception {
       try{
           EsSearchDTO esSearchDTO = new EsSearchDTO();
           esSearchDTO.setOffset(0);
           esSearchDTO.setLimit(5);
           esSearchDTO.setIncludeFields(INCLUDE_FIELDS);
           esSearchDTO.setExcludeFields(null);
           esSearchDTO.setIndices("bd_membership_search_index");
           esSearchDTO.setType("bd_membership_search_type");
           esSearchDTO.setBoolQueryBuilder(boolBuilder);
           
           EsResponse response = esClientService.search(esSearchDTO);
           if(response != null && response.getList().size()>0){
        	   System.out.println("---------------------------" + response.getTotal());
           }
           else {
        	   System.out.println("---------------------------" + 0);
           }
           
       }catch(Exception ex){
    	   System.out.println(ex.getMessage());
       }
   }
	
}
