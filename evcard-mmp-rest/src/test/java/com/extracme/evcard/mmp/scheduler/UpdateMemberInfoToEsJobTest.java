package com.extracme.evcard.mmp.scheduler;

import com.extracme.evcard.mmp.App;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
class UpdateMemberInfoToEsJobTest {

    @Resource
    private UpdateMemberInfoToEsJob updateMemberInfoToEsJob;

    @Test
    void execute() {
        updateMemberInfoToEsJob.execute(null);
    }
}