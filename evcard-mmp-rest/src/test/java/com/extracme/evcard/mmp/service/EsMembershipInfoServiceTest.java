package com.extracme.evcard.mmp.service;

import javax.annotation.Resource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import com.extracme.evcard.mmp.bo.SearchMemberListBO;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration({"applicationContext.xml"})
public class EsMembershipInfoServiceTest {
    @Resource
    private EsMembershipInfoServiceImpl esMembershipInfoServiceImpl;
    
    @Test
	public void esSearch() throws Exception {

       try{
           /*esMembershipInfoServiceImpl.exportMembershipInfo(null, null, null, null, "000T",
                   null, null, null, "0002", "嘉定政府", null, null,
                   null, null, null, null, null, null, null, null,
                   null, null, null, null, null, null,
                   null, null, null, null, null, null, 0, null);*/



           SearchMemberListBO bo = new SearchMemberListBO();
    	    bo.setPageSize(20);
    	    bo.setOffset(0);
    	    bo.setAppKeyName("分包-华为应用市场");
    	    bo.setAccountStatus(0);
    	    bo.setSesameCreditAuthorization(1);
    	    //bo.
	       	//bo.setName(null);
	       	//bo.setMobilePhone(null);
	       	bo.setCreatedStartTime("**************");
	       	bo.setCreatedEndTime("**************");

           bo.setReviewStartTime("**************");
           bo.setReviewEndTime("**************");
            //bo.setDataOrigin(4);
	       	
	       	//bo.setInfoOrigin("成都");
	       	//bo.setOrgId("003M");
	       	//bo.setReviewStatus(1);
	       	esMembershipInfoServiceImpl.searchMemberByES(bo);
           
       }catch(Exception ex){
    	   System.out.println(ex.getMessage());
       }
   }

}
