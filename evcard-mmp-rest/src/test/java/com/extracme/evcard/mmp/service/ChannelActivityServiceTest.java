package com.extracme.evcard.mmp.service;

import com.extracme.evcard.mmp.bo.SearchMemberListBO;
import com.extracme.evcard.mmp.dto.InviteActivityCouponRecordDTO;
import com.extracme.evcard.mmp.dto.PackageQueryDTO;
import com.extracme.evcard.mmp.dto.ShortRentActivityQueryDTO;
import com.extracme.evcard.mmp.scheduler.UpdateChannelRewardActivityStatusServiceImpl;
import com.extracme.evcard.sts.rpc.service.IBillingConfigurationService;
import com.extracme.evcard.sts.rpc.service.dto.BillPackageTypeInfoDto;
import com.extracme.framework.core.vo.DefaultWebRespVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration({"applicationContext.xml"})
public class ChannelActivityServiceTest {
    @Resource
    private UpdateChannelRewardActivityStatusServiceImpl updateChannelRewardActivityStatusServiceImpl;

    @Resource
    CouponGrantRecordService couponGrantRecordServiceImpl;

    @Resource
    private IInvitationActivityService invitationActivityServiceImpl;

    @Resource
    private IBillingConfigurationService billingConfigurationService;

    @Resource
    private MmpDiscountRuleServiceImpl mmpDiscountRuleServiceImpl;

    @Resource
    private IAppLayerActivityService appLayerActivityServiceImpl;

    @Resource
    private IMarketActivityService marketActivityService;

    @Test
    public void testChannel() throws Exception {
        try {
            ShortRentActivityQueryDTO vo = new ShortRentActivityQueryDTO();
            vo.setOrgId("00");
            vo.setStartTime("2020-04-21 00:00");
            vo.setEndTime("2020-04-22 00:00");
            appLayerActivityServiceImpl.getShortRentActivities(vo);
//            mmpDiscountRuleServiceImpl.getPackageTypeContent(Arrays.asList("4", "2", "3"));
//
//            List<BillPackageTypeInfoDto> packageTypeList = billingConfigurationService.getBillPackageTypeInfo();
//            //return DefaultWebRespVO.getSuccessVO(packageTypeList);
//
//
//            InviteActivityCouponRecordDTO values = invitationActivityServiceImpl.getInviteActivityCoupons("99277178,99277645", "12111103014151536833",
//                    2, 1, 1000, 0);
//
//
//            couponGrantRecordServiceImpl.exportCouponGrantRecordQuery("", "", "", "", ""
//            ,"", "", "", "", "", "", "", "","", null, null);
//            //updateChannelRewardActivityStatusServiceImpl.updateChannelRewardActivityStatus();

        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
    }


    @Test
    public void Packages() {
        PackageQueryDTO input = new PackageQueryDTO();
        input.setOrgId("000T");
        input.setRentMethod("0,3");
        input.setServiceType(1);
        input.setVehicleModle("");
        input.setVehicleNo("沪A");
        //marketActivityService.queryPackageList(input);
        input.setRentMethod(null);
        marketActivityService.queryPackageList(input);
    }
}
