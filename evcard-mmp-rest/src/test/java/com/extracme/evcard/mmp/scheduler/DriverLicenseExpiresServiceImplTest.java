package com.extracme.evcard.mmp.scheduler;

import com.extracme.evcard.mmp.App;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/8/31
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class DriverLicenseExpiresServiceImplTest {

    @Resource
    private DriverLicenseExpiresServiceImpl driverLicenseExpiresService;

    @Test
    public void testDriverLicenseExpiresService() {
        driverLicenseExpiresService.execute(null);
    }

}
