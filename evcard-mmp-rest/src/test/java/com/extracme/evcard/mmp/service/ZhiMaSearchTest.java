package com.extracme.evcard.mmp.service;

import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration({"applicationContext.xml"})
public class ZhiMaSearchTest {

    @Resource
    IPersonalDetailInfoService iPersonalDetailInfoServiceImpl;

    @Test
    public void test1(){
        System.out.println("----------------"+JSON.toJSONString(iPersonalDetailInfoServiceImpl.querySesameCreditRecord(1, 10, 0, "15502118547111616332")));
    }

}
