package com.extracme.evcard.mmp.service;

import com.extracme.evcard.mmp.bo.QueryExportInfoBO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration({"applicationContext.xml"})
public class MmpExportServiceTest {
    @Resource
    IMmpExportInfoService mmpExportInfoServiceImpl;

    @Test
    public void testAgencyReportList() throws Exception {
        try {
            QueryExportInfoBO bo = new QueryExportInfoBO();
            bo.setOrgIds(null);
            bo.setFileType(2);
            bo.setCurrentOrgId("00");
            bo.setPageSize(20);
            bo.setPageNum(1);
            bo.setIsAll(1);
            bo.setStartTimes("2019-04-15");
            bo.setEndTimes("2019-04-15");
            mmpExportInfoServiceImpl.queryExportInfoList(bo);
            //updateChannelRewardActivityStatusServiceImpl.updateChannelRewardActivityStatus();
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
    }

    @Test
    public void testEAccountReportList() throws Exception {
        try {
            QueryExportInfoBO bo = new QueryExportInfoBO();
            bo.setOrgIds(null);
            bo.setFileType(1);
            bo.setCurrentOrgId("00");
            bo.setPageSize(20);
            bo.setPageNum(1);
            bo.setIsAll(1);
            bo.setStartTimes("2019-04-15");
            bo.setEndTimes("2019-04-15");
            mmpExportInfoServiceImpl.queryExportInfoList(bo);
            //updateChannelRewardActivityStatusServiceImpl.updateChannelRewardActivityStatus();
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
    }

    @Test
    public void testEAccountReportList2() throws Exception {
        try {
            QueryExportInfoBO bo = new QueryExportInfoBO();
            bo.setOrgIds(Arrays.asList(new String[]{"000T","008N", "008N01"}));
            bo.setFileType(1);
            bo.setCurrentOrgId("00");
            bo.setPageSize(20);
            bo.setPageNum(1);
            bo.setIsAll(1);
            bo.setStartTimes("2019-04-15");
            bo.setEndTimes("2019-04-15");
            mmpExportInfoServiceImpl.queryExportInfoList(bo);
            //updateChannelRewardActivityStatusServiceImpl.updateChannelRewardActivityStatus();
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
    }

    @Test
    public void cleanEReport() throws Exception {
        try {
            List<Long> fileTypes = java.util.Arrays.asList(1L,2L);
            //mmpExportInfoServiceImpl.updateExportInfoStatus(fileTypes, 14);
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
    }
}
