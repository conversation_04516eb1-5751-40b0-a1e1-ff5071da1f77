package com.extracme.evcard.mmp.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.dto.activity.FirstOrderActivityFullDTO;
import com.extracme.evcard.mmp.service.activity.impl.FirstOrderReliefServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration({"applicationContext.xml"})
public class FirstOrderSvcTest {

    @Resource
    FirstOrderReliefServiceImpl firstOrderReliefService;

    @Test
    public void testChannel() throws Exception {
        try {
            String str = "{\"activityName\":\"fafaf\",\"orgId\":\"00\",\"couponModels\":[{\"validTimeType\":\"1\",\"startDate\":\"2019-04-23\",\"expiresDate\":\"2019-04-30\",\"couponName\":\"11\",\"couponValue\":\"11\",\"serviceType\":\"1\",\"minAmount\":\"\",\"vehicleNo\":\"\",\"vehicleModle\":\"\",\"pickshopSeq\":\"\",\"returnshopSeq\":\"\",\"timeType\":\"\",\"discountRate\":\"\",\"couponType\":3,\"pickshopCityName\":\"\",\"returnshopCityName\":\"\",\"pickshopCity\":\"\",\"returnshopCity\":\"\",\"activityOverlap\":0,\"availableDaysOfWeek\":\"1,2,3,4,5,6,7\",\"holidaysAvailable\":1,\"checkAmount\":1,\"limitMoney\":\"\",\"durationLimit\":\"\",\"orgId\":\"00\"}],\"quotaOrgId\":\"00\",\"remark\":\"fafa\"}";
            FirstOrderActivityFullDTO dto = JSON.parseObject(str, FirstOrderActivityFullDTO.class);
           // firstOrderReliefService.updateFirstOrderRelief(dto, null);
            //updateChannelRewardActivityStatusServiceImpl.updateChannelRewardActivityStatus();

        } catch (Exception ex) {
            System.out.println(ex.getMessage());
        }
    }

}
