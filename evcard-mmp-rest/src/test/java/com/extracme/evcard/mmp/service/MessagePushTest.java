
package com.extracme.evcard.mmp.service;

import com.extracme.evcard.mmp.App;
import com.extracme.evcard.mmp.dao.MembershipInfoMapper;
import com.extracme.evcard.rpc.messagepush.service.IMessagepushServ;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.*;

/**
 * Title: 
 * @date 2018年8月15日
 * @since
 *
 */

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MessagePushTest {

	@Resource
	private IMessagepushServ messagepushServ;

	@Resource
	private MembershipInfoMapper membershipInfoMapper;


	String[] areas = {"嘉定区","黄浦区","青浦区", "宝山区","普陀区","长宁区","浦东新区"};
	@Test
	public void testUser() throws Exception {
//		for(int i = 0; i < 300; i ++){
//			Calendar cal = Calendar.getInstance();
//			cal.set(Calendar.YEAR, 2018);
//			cal.set(Calendar.MONTH, 11);
//			cal.set(Calendar.DAY_OF_MONTH, 10 + i % 16);
//
//			Date time = cal.getTime();
//			String createTime = DateUtils.formatDate(time, ComUtil.DATE_TYPE3);
//			String regTime = DateUtils.formatDate(time, ComUtil.DATE_TYPE4);
//			System.out.println(i);
//			String area = are as[i%7];
//			membershipInfoMapper.updateUser(i, regTime, createTime, area);
//		}

	}




	@Test
	public void testMessagePush() throws Exception {
		List<String> mobilePhones = new ArrayList<>();
		mobilePhones.add("13818071668");
		messagepushServ.syncSendBehaviorSMSForMarket("1234567910", "测试", "evcard_mmp");
//		System.out.println("---------------------------" + "testMessagePush");
//		for(int i = 0; i < 100; i ++) {
//			try {
//				messagepushServ.pushWeb("evcard-mmp", "15835898714@hq",
//						"mmpTaskNotify",
//						"{\"orgId\":\"000T\",\"taskCount\":5}");
//			}catch (Exception ex) {
//				System.out.println(ex.getMessage());
//			}
//		}
	}
	
}
