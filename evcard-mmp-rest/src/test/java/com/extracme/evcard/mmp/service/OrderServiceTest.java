package com.extracme.evcard.mmp.service;


import com.extracme.evcard.mmp.service.activity.ShopServ;
import com.extracme.evcard.mmp.service.activity.VehicleModelServ;
import com.extracme.evcard.rpc.order.service.IOrderService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration({"applicationContext.xml"})
public class OrderServiceTest {
    @Resource
    private PersonalDetailInfoServiceImpl personalDetailInfoServiceImpl;

    @Resource
    private IOrderService orderService;


    @Resource
    private ShopServ shopServ;

    @Resource
    private VehicleModelServ vehicleModelServ;

    @Test
    public void checkOrderByCardNo() {

        String str = shopServ.getShopNames("1,4957,6021,3");

        String str1 = vehicleModelServ.getVehicleModelNames("1,11,88,90");


        String lastOrder = orderService.selectOneUserOrder("15839636064181314984", 0, null, null, null);

        personalDetailInfoServiceImpl.checkOrderByCardNo("58867160", 0, null);

        personalDetailInfoServiceImpl.checkOrderByCardNo("52983145", 0, null);
    }
}
