package com.extracme.evcard.mmp.service;

import com.alibaba.fastjson.JSON;
import com.extracme.evcard.mmp.dto.AgencyInfoSearchDTO;
import com.extracme.evcard.rpc.exception.BusinessException;
import com.extracme.evcard.saic.bean.AddCityInput;
import com.extracme.evcard.saic.service.ISaicMemberService;
import com.extracme.evcard.mmp.dto.PackageQueryDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration({"applicationContext.xml"})
public class PackageSearch {

    @Autowired
    IAgencyService iAgencyService;

    @Resource
    ISaicMemberService saicMemberService;

    @Test
    public void query(){
        AgencyInfoSearchDTO agencyInfoSearchDTO = new AgencyInfoSearchDTO();
        agencyInfoSearchDTO.setPageNum(1);
        agencyInfoSearchDTO.setPageSize(10);
        agencyInfoSearchDTO.setIsAll(0);
        agencyInfoSearchDTO.setPackageType("2");
        System.out.println(JSON.toJSONString(iAgencyService.find(agencyInfoSearchDTO)));
    }

    @Test
    public void query2(){
        AddCityInput input = new AddCityInput();
        input.setMobile("13988880001");
        input.setToken("");
        input.setProvince("上海市");
        input.setCity("上海市");
        input.setArea("嘉定区");
        input.setAddress("fafafa");
        input.setRegisterOrigin(1);
        input.setAppKey("haiertest");
        input.setShareUid("ffff");
        //input.setImeiNo();
        //input.setOperateUser();
        //input.setInvitationCode();
        //input.setOrgId();
        //input.setAnonymousId();
        //input.setTrackPlatForm();
        //input.setMembershipPolicyVersion();
        //input.setPrivacyPolicyVersion();
        try {
            System.out.println(JSON.toJSONString(saicMemberService.register(input)));
        } catch (BusinessException e) {
            e.printStackTrace();
        }
    }

}
