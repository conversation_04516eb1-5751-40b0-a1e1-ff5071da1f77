package com.extracme.evcard.mmp.service;

import com.extracme.evcard.mmp.App;
import com.extracme.evcard.mmp.rest.MdRestClient;
import com.extracme.evcard.mmp.rest.entity.*;
import com.extracme.evcard.mmp.scheduler.UpdateGoodsModelForCouponDefJob;
import com.extracme.evcard.mmp.scheduler.UpdateGoodsModelForMmpCardDefJob;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = App.class)
public class MdRestTest {
    @Autowired
    MdRestClient mdRestClient;
    @Autowired
    IPersonalDetailInfoService personalDetailInfoService;

    @Autowired
    UpdateGoodsModelForCouponDefJob updateGoodsModelForCouponDefJob;

    @Autowired
    UpdateGoodsModelForMmpCardDefJob  updateGoodsModelForMmpCardDefJob;

    @Test
    public void getOrderCountByStatusTest() {
        GetOrderCountByStatusRequest request = new GetOrderCountByStatusRequest();
        request.setMid("2008241400001");
        request.setContractStatus(Arrays.asList(6, 7));
        GetOrderCountByStatusResponse res = mdRestClient.getOrderCountByStatus(request);
        System.out.println(res);
        Integer result = res.getData().getResult();
        System.out.println(result);
    }

    @Test
    public void checkOrderByCardNoTest() {
        DefaultServiceRespDTO res = personalDetailInfoService.checkOrderByCardNo("0002517911", 2, "注销");
        System.out.println(res);
    }

    @Test
    public void getGoodsTest() {
        GetGoodsVehicleModelRelationRequest request = new GetGoodsVehicleModelRelationRequest();
        request.setCfgMd5("aa");
        GetsGoodsVehicleModelRelationResponse response = mdRestClient.getCacheGoodsVehicleModelRelation(request);
        GoodsVehicleModelRelationData data = response.getData();
        ArrayList<GoodsVehicleModelRelationInfo> info = data.getGoodsVehicleModelRelationInfo();
        System.out.println(info);
    }
    @Test
    public void updateGoodsModelForMmpCardDefJob() {
        updateGoodsModelForCouponDefJob.execute(null);
    }

    @Test
    public void updateGoodsModelForCouponDefTest() {
        updateGoodsModelForMmpCardDefJob.execute(null);
    }
}
