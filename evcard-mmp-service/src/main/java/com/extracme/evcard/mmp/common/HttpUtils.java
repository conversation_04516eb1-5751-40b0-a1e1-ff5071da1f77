package com.extracme.evcard.mmp.common;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.BufferedHttpEntity;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * <AUTHOR>
 */
public class HttpUtils {

    private static Logger logger = LoggerFactory.getLogger(HttpUtils.class);

    public static JSONObject httptGet(String url){
        HttpGet httpGet = new HttpGet(url);
        CloseableHttpClient client = HttpClients.createDefault();
        CloseableHttpResponse resp = null;
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(3000).setConnectionRequestTimeout(3000)
                .setSocketTimeout(3000).build();
        httpGet.setConfig(requestConfig);
        String respContent = null;
        try {
            resp = client.execute(httpGet);
            if (resp.getStatusLine().getStatusCode() == 200) {
                HttpEntity resultEntity = resp.getEntity();
                respContent = EntityUtils.toString(resultEntity, "UTF-8");
                int index = respContent.lastIndexOf("}") + 1;
                respContent = respContent.substring(0, index);
            } else {
                logger.info(String.valueOf(resp.getStatusLine().getStatusCode()));
                logger.info("调用接口："+url+"失败");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        } finally {
            try {
                client.close();
                if(resp != null) {
                    resp.close();
                }
            } catch (IOException e) {
                logger.error(e.getMessage(),e);
            }
        }
        if(StringUtils.isNotBlank(respContent)){
            return JSON.parseObject(respContent.trim());
        }else{
            return null;
        }
    }

    /**
     * http调用post接口
     * @param url 访问地址
     * @param param 参数
     * @return JSONObject 结果
     */
    public static JSONObject httpPostWithJSON(String url, Object param) {
        HttpPost httpPost = new HttpPost(url);
        CloseableHttpClient client = HttpClients.createDefault();
        CloseableHttpResponse resp = null;
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(3000).setConnectionRequestTimeout(3000)
                .setSocketTimeout(3000).build();
        httpPost.setConfig(requestConfig);
        String respContent = null;
        try {
            // 解决中文乱码问题
            logger.info("请求参数："+JSON.toJSONString(param));
            StringEntity entity = new StringEntity(JSON.toJSONString(param), "utf-8");
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            resp = client.execute(httpPost);
            if (resp.getStatusLine().getStatusCode() == 200 || resp.getStatusLine().getStatusCode()==500) {
                HttpEntity resultEntity = resp.getEntity();
                respContent = EntityUtils.toString(resultEntity, "UTF-8");
                int index = respContent.lastIndexOf("}") + 1;
                respContent = respContent.substring(0, index);
            } else {
                logger.info(String.valueOf(resp.getStatusLine().getStatusCode()));
                logger.info("调用接口："+url+"失败");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        } finally {
            try {
                client.close();
                if(resp != null) {
                    resp.close();
                }
            } catch (IOException e) {
                logger.error(e.getMessage(),e);
            }
        }
        if(StringUtils.isNotBlank(respContent)){
            logger.info(respContent);
            return JSON.parseObject(respContent.trim());
        }else{
            return null;
        }

    }

    /**
     * 用于微信api返回图片的调用
     * @param url
     * @param param
     * @return
     */
    public static JSONObject httpJpgPostWithJSON(String url, Object param) {
        HttpPost httpPost = new HttpPost(url);
        CloseableHttpClient client = HttpClients.createDefault();
        CloseableHttpResponse resp = null;
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(3000).setConnectionRequestTimeout(3000)
                .setSocketTimeout(3000).build();
        httpPost.setConfig(requestConfig);
        String respContent = null;
        String uploadFileUrl = null;
        try {
            // 解决中文乱码问题
            logger.info("请求参数："+JSON.toJSONString(param));
            StringEntity entity = new StringEntity(JSON.toJSONString(param), "utf-8");
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            resp = client.execute(httpPost);
            if (resp.getStatusLine().getStatusCode() == 200 || resp.getStatusLine().getStatusCode()==500) {
                HttpEntity resultEntity = resp.getEntity();
                resultEntity = new BufferedHttpEntity(resultEntity);

                respContent = EntityUtils.toString(resultEntity, "UTF-8");
                int index = respContent.lastIndexOf("}") + 1;
                respContent = respContent.substring(0, index);
                //校验是否为json格式
                if (!isValidJson(respContent)){
                    byte[] imageData = EntityUtils.toByteArray(resultEntity);
                    InputStream inputStream = new ByteArrayInputStream(imageData);
                    long timestamp = System.currentTimeMillis();
                    uploadFileUrl = getUploadFileUrl(String.valueOf(timestamp));
                    UploadImgUtil.uploadStreamSyn(inputStream, uploadFileUrl.toString());
                }
            } else {
                logger.info(String.valueOf(resp.getStatusLine().getStatusCode()));
                logger.info("调用接口："+url+"失败");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        } finally {
            try {
                client.close();
                if(resp != null) {
                    resp.close();
                }
            } catch (IOException e) {
                logger.error(e.getMessage(),e);
            }
        }
        if(StringUtils.isNotBlank(respContent) && isValidJson(respContent)){
            logger.info(respContent);
            return JSON.parseObject(respContent.trim());
        }else if(StringUtils.isNotBlank(uploadFileUrl)){
            return JSON.parseObject("{\"pictureUrl\": \""+uploadFileUrl.trim()+"\"}");
        }else{
            return null;
        }
    }
    //校验是否为json格式
    public static boolean isValidJson(String jsonString) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            objectMapper.readTree(jsonString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    private static String getUploadFileUrl(String randomCode) {
        StringBuffer fileUrl = new StringBuffer("/qrCode/wechat/");
        String suffix = ".png";
        fileUrl.append(ComUtil.getSystemDate(ComUtil.DATE_TYPE3) + "_" + randomCode + suffix);
        return fileUrl.toString();
    }
}
