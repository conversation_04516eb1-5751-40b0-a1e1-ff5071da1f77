package com.extracme.evcard.mmp.common;

import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.env.Environment;

import java.sql.Date;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.ResourceBundle;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 项目名称：evcard-fas-service
 * 类名称：MessageConsts
 * 类描述：验证共通类
 * 创建人：周约
 * 创建时间：2017年4月11日 下午4:11:52
 * 修改备注：
 * @version1.0
 */
public class MessageUtil {

    // 该数据已经存在,请重新输入！
    public static final String CHECK_EXIST_KEY = "10030001";

    // {?}不能为空！
    public static final String CHECK_MUST_INPUT_KEY = "10030002";

    // {?}的输入长度不能超过{?}位！！
    public static final String CHECK_MAX_LEN_KEY = "10030003";

    // {?}必须输入整数！
    public static final String CHECK_MUST_INTEGER_KEY = "10030004";

    // {?}必须输入数字类型！
    public static final String CHECK_MUST_DIGIT_KEY = "10030005";

    // {?}的整数位不能大于{i}位,小数位不能大于{?}位！
    public static final String CHECK_NUMERIC_LEN_KEY = "10030006";

    // {?}的日期输入不合法！
    public static final String CHECK_DATE_KEY = "10030007";

    // {?}的日期不能大于{?}的日期
    public static final String CHECK_DATE_RANGE_KEY = "10030008";

    // {?}的下拉框未选择
    public static final String CHECK_DROPDOWNLIST_KEY = "10030009";

    // {?}的输入值不能超过{?}
    public static final String CHECK_INT_RANGE_KEY = "10030010";

    //private static final ResourceBundle bundle = ResourceBundle.getBundle("message");

    /**
     * 通过键获取值
     * @param key
     * @return
     */
    public static final String getString(String key) {
        Environment environment = SpringContextUtil.getBean(Environment.class);
        return environment.getProperty(key);
        //return bundle.getString(key);
    }

    /**
     * 传入参数列表返回message信息
     * @param params 参数列表
     * @return String
     */
    public static String getMessage(String key, List<String> params) {
        String str = getString(key);
        if (null == params || params.size() == 0) {
            return str;
        }

        int paramsSize = params.size();
        for (int i = 0; i < paramsSize; i++) {
            str = str.replaceFirst("\\{\\?\\}", params.get(i));
        }
        return str;
    }

    /**
     * 取得业务数据存在message情报
     * @param str 重复项目名称
     * @return DefaultServiceRespDTO
     */
    public static DefaultServiceRespDTO getExistMessageInfo(String str) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();

        List<String> params = new ArrayList<String>();
        vo.setCode(Integer.valueOf(CHECK_EXIST_KEY));
        params.add(str);
        vo.setMessage(getMessage(CHECK_EXIST_KEY, params));

        return vo;
    }

    /**
     * 最大长度输入check
     * @param inputName 画面项目名称
     * @param inputValue 画面项目内容
     * @param len DB定义长度
     * @return DefaultServiceRespDTO
     */
    public static DefaultServiceRespDTO checkLength(String inputName, String inputValue, int len) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();

        if (null == inputValue || StringUtils.trim(inputValue).isEmpty()) {
            return vo;
        }

        if (StringUtils.trim(inputValue).length() > len) {
            List<String> params = new ArrayList<String>();
            params.add(inputName);
            params.add(String.valueOf(len));
            vo.setCode(Integer.valueOf(CHECK_MAX_LEN_KEY));
            vo.setMessage(getMessage(CHECK_MAX_LEN_KEY, params));
        }

        return vo;
    }

    /**
     * 必须输入check
     * @param inputName 画面项目名称
     * @param inputValue 画面项目内容
     * @return DefaultServiceRespDTO
     */
    public static DefaultServiceRespDTO checkMustInput(String inputName, String inputValue) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();

        if (null == inputValue || StringUtils.trim(inputValue).isEmpty()) {
            List<String> params = new ArrayList<String>();
            params.add(inputName);
            vo.setCode(Integer.valueOf(CHECK_MUST_INPUT_KEY));
            vo.setMessage(getMessage(CHECK_MUST_INPUT_KEY, params));
        }
        return vo;
    }

    /**
     * 下拉框必须输入check
     * @param inputName 画面项目名称
     * @param inputValue 画面项目内容
     * @return DefaultServiceRespDTO
     */
    public static DefaultServiceRespDTO checkMustDropDownList(String inputName, String inputValue) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();

        if (inputValue == null || StringUtils.trim(inputValue).isEmpty() || Contants.DROPDOWNLIST_DEFAULT.equals(inputValue) || Contants.DROPDOWNLIST_DEFAULT.equals(inputValue)) {
            List<String> params = new ArrayList<String>();
            params.add(inputName);
            vo.setCode(Integer.valueOf(CHECK_DROPDOWNLIST_KEY));
            vo.setMessage(getMessage(CHECK_DROPDOWNLIST_KEY, params));
        }
        return vo;
    }

    /**
     * 必须输入整数check
     * @param inputName 画面项目名称
     * @param inputValue 画面项目内容
     * @return DefaultServiceRespDTO
     */
    public static DefaultServiceRespDTO checkInteger(String inputName, String inputValue) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();

        if (null == inputValue || StringUtils.trim(inputValue).isEmpty()) {
            return vo;
        }
        if ("0".equals(StringUtils.trim(inputValue))) {
            return vo;
        }
        String rex = "^\\+?[1-9][0-9]*$";
        Pattern pattern = Pattern.compile(rex);
        Matcher isNum = pattern.matcher(inputValue);
        if (!isNum.matches()) {
            List<String> params = new ArrayList<String>();
            params.add(inputName);
            vo.setCode(Integer.valueOf(CHECK_MUST_INTEGER_KEY));
            vo.setMessage(getMessage(CHECK_MUST_INTEGER_KEY, params));
        }
        return vo;
    }

    /**
     * 允许输入小数的项目的整数位和小数位长度check
     * @param inputName 画面项目名称
     * @param inputValue 画面项目内容
     * @param integerLen DB定义整数位长度
     * @param decimalLen DB定义小数位长度
     * @return DefaultServiceRespDTO Integer decimal
     */
    public static DefaultServiceRespDTO checkNumericLen(String inputName, String inputValue, int integerLen, int decimalLen) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        if (null == inputValue || StringUtils.trim(inputValue).isEmpty()) {
            return vo;
        }

        List<String> params = new ArrayList<String>();

        Pattern pattern = ComUtil.NUMERIC_PATTERN;
        Matcher isNum = pattern.matcher(inputValue);
        if (!isNum.matches()) {
            params.add(inputName);
            vo.setCode(Integer.valueOf(CHECK_MUST_DIGIT_KEY));
            vo.setMessage(getMessage(CHECK_MUST_DIGIT_KEY, params));
            return vo;
        } else {
            // 有效长度check
            String[] numberList = inputValue.split("\\.");
            int listLen = numberList.length;
            params.add(inputName);
            params.add(String.valueOf(integerLen));
            params.add(String.valueOf(decimalLen));
            if (listLen == 1) {
                // 只输入了整数
                if (numberList[0].toString().length() > integerLen) {
                    vo.setCode(Integer.valueOf(CHECK_NUMERIC_LEN_KEY));
                    vo.setMessage(getMessage(CHECK_NUMERIC_LEN_KEY, params));
                    return vo;
                }
            } else if (listLen == 2) {
                // 整数位检查
                if (numberList[0].toString().length() > integerLen) {
                    vo.setCode(Integer.valueOf(CHECK_NUMERIC_LEN_KEY));
                    vo.setMessage(getMessage(CHECK_NUMERIC_LEN_KEY, params));
                    return vo;
                }
                // 小数位检查
                if (numberList[1].toString().length() > decimalLen) {
                    vo.setCode(Integer.valueOf(CHECK_NUMERIC_LEN_KEY));
                    vo.setMessage(getMessage(CHECK_NUMERIC_LEN_KEY, params));
                    return vo;
                }
            }
        }
        return vo;
    }

    /**
     * 允许输入小数的项目的整数位和小数位长度check
     * @param inputName 画面项目名称
     * @param inputValue 画面项目内容
     * @param integerLen DB定义整数位长度
     * @param decimalLen DB定义小数位长度
     * @return DefaultServiceRespDTO Integer decimal
     */
    public static DefaultServiceRespDTO checkNumericLenOfYuan(String inputName, String inputValue, int integerLen, int decimalLen) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        if (null == inputValue || StringUtils.trim(inputValue).isEmpty()) {
            return vo;
        }

        List<String> params = new ArrayList<String>();

        Pattern pattern = ComUtil.NUMERIC_PATTERN;
        Matcher isNum = pattern.matcher(inputValue);
        if (!isNum.matches()) {
            params.add(inputName);
            vo.setCode(Integer.valueOf(CHECK_MUST_DIGIT_KEY));
            vo.setMessage(getMessage(CHECK_MUST_DIGIT_KEY, params));
            return vo;
        } else {
            // 有效长度check
            String[] numberList = inputValue.split("\\.");
            int listLen = numberList.length;
            params.add(inputName);
            params.add(String.valueOf(integerLen + 4));
            params.add(String.valueOf(decimalLen - 4));
            if (listLen == 1) {
                // 只输入了整数
                if (numberList[0].toString().length() > integerLen) {
                    vo.setCode(Integer.valueOf(CHECK_NUMERIC_LEN_KEY));
                    vo.setMessage(getMessage(CHECK_NUMERIC_LEN_KEY, params));
                    return vo;
                }
            } else if (listLen == 2) {
                // 整数位检查
                if (numberList[0].toString().length() > integerLen) {
                    vo.setCode(Integer.valueOf(CHECK_NUMERIC_LEN_KEY));
                    vo.setMessage(getMessage(CHECK_NUMERIC_LEN_KEY, params));
                    return vo;
                }
                // 小数位检查
                if (numberList[1].toString().length() > decimalLen) {
                    vo.setCode(Integer.valueOf(CHECK_NUMERIC_LEN_KEY));
                    vo.setMessage(getMessage(CHECK_NUMERIC_LEN_KEY, params));
                    return vo;
                }
            }
        }
        return vo;
    }

    /**
     * 有效期开始时间小于结束时间
     * @param contractStartDate 合同开始时间
     * @param contractEndDate 合同结束时间
     * @return DefaultServiceRespDTO
     */
    public static DefaultServiceRespDTO checkDateRangeKey(String inputName, String contractStartDate, String contractEndDate) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();

        if (Date.valueOf(contractStartDate).after(Date.valueOf(contractEndDate))) {
            List<String> params = new ArrayList<String>();
            params.add(inputName);
            vo.setCode(Integer.valueOf(CHECK_DATE_RANGE_KEY));
            vo.setMessage(getMessage(CHECK_DATE_RANGE_KEY, params));
        }
        return vo;
    }

    /**
     * 开始时间小于结束时间
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return DefaultServiceRespDTO
     */
    public static DefaultServiceRespDTO checkTimeRangeKey(String inputName, String startTime, String endTime) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        if (Timestamp.valueOf(startTime).getTime() > Timestamp.valueOf(endTime).getTime()) {
            List<String> params = new ArrayList<String>();
            params.add(inputName);
            vo.setCode(Integer.valueOf(CHECK_DATE_RANGE_KEY));
            vo.setMessage(getMessage(CHECK_DATE_RANGE_KEY, params));
        }
        return vo;
    }

    /**
     * mysql的smallint型最大值check
     * @param inputName 画面项目名称
     * @param inputValue 画面项目内容
     * @return DefaultServiceRespDTO
     */
    public static DefaultServiceRespDTO checkSmallIntMaxRange(String inputName, String inputValue) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        vo = checkInteger(inputName, inputValue);
        if (!vo.getCode().equals(Contants.RETURN_SUCCESS_CODE)) {
            return vo;
        }

        if (null == inputValue || StringUtils.trim(inputValue).isEmpty()) {
            return vo;
        }

        int val = 0;
        try {
            val = Integer.valueOf(inputValue);
            if (val > Contants.MYSQL_SMALLINT_MAX) {
                List<String> params = new ArrayList<String>();
                params.add(inputName);
                params.add(String.valueOf(Contants.MYSQL_SMALLINT_MAX));
                vo.setCode(Integer.valueOf(CHECK_INT_RANGE_KEY));
                vo.setMessage(getMessage(CHECK_INT_RANGE_KEY, params));
            }
        } catch (NumberFormatException ex) {
            List<String> params = new ArrayList<String>();
            params.add(inputName);
            params.add(String.valueOf(Contants.MYSQL_SMALLINT_MAX));
            vo.setCode(Integer.valueOf(CHECK_INT_RANGE_KEY));
            vo.setMessage(getMessage(CHECK_INT_RANGE_KEY, params));
        }

        return vo;
    } 
}
