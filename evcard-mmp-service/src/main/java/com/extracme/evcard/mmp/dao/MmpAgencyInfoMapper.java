package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.dto.MmpAgencyInfoDTO;
import com.extracme.evcard.mmp.model.MmpAgencyInfo;
import com.extracme.framework.data.dao.OecsMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@OecsMapper
public interface MmpAgencyInfoMapper  {

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_export_info
     *
     * @mbggenerated Fri Apr 12 06:48:29 CST 2019
     */
    MmpAgencyInfoDTO selectByPrimaryKey(Long id);

    /**
     * 根据企业信用代码，查询企业信息
     * @param socialCreditCode
     * @return
     */
    List<MmpAgencyInfoDTO> queryAgencyInfo(@Param("socialCreditCode")String socialCreditCode);

    /**
     * 保存机构信息
     * @param record
     * @return
     */
    int saveReocrd(MmpAgencyInfo record);

    /**
     * @description:增加生成兑换码企业购买信息
     * @return:
     * @author: DongYU
     * @param:
     */
    int addAgencyInfo(MmpAgencyInfoDTO mmpAgencyInfoDTO);

}
