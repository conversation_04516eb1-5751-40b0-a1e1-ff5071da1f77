package com.extracme.evcard.mmp.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.dto.PlatformAllDTO;
import com.extracme.evcard.mmp.dto.PlatformDTO;
import com.extracme.evcard.mmp.dto.PlatformOperateLogDTO;
import com.extracme.evcard.mmp.dto.PlatformQueryDTO;
import com.extracme.evcard.mmp.model.MmpPlatformInfo;

public interface MmpPlatformInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_info
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_info
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    int insert(MmpPlatformInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_info
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    int insertSelective(MmpPlatformInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_info
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    MmpPlatformInfo selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_info
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    int updateByPrimaryKeySelective(MmpPlatformInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_info
     *
     * @mbg.generated Tue Jan 22 15:40:39 CST 2019
     */
    int updateByPrimaryKey(MmpPlatformInfo record);

	List<PlatformDTO> queryPlatformList(PlatformQueryDTO platformQueryDTO);

	int updatePlatformName(@Param("id") Long id, @Param("platformName") String platformName, @Param("updateTime") String updateTime, @Param("updateOperId") Long updateOperId, @Param("updateOperName") String updateOperName);

	List<PlatformAllDTO> queryAllPlatform();

	MmpPlatformInfo queryByPlatformName(String platformName);
}