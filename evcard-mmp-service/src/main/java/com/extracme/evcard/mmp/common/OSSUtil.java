package com.extracme.evcard.mmp.common;

import java.io.InputStream;

import com.aliyun.oss.OSSClient;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class OSSUtil {
    protected String endPoint;
    protected String accessId;
    protected String accessKey;
    protected String bucket;

    public String getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(String endPoint) {
        this.endPoint = endPoint;
    }

    public String getAccessId() {
        return accessId;
    }

    public void setAccessId(String accessId) {
        this.accessId = accessId;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getBucket() {
        return bucket;
    }

    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    private OSSClient client;

    public void init() {
        client = new OSSClient(endPoint, accessId, accessKey);
    }

    public void uploadFile(InputStream inputStream, String fileName) {
        // 上传
        try {
            client.putObject(bucket, fileName, inputStream);
        } catch (Exception e) {
            log.error("OSSUtil.uploadFile 异常， fileName=" + fileName, e);
            e.printStackTrace();
        }
    }

    public void shutdown() {
        client.shutdown();
    }
}
