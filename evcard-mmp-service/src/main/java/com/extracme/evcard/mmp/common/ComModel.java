package com.extracme.evcard.mmp.common;

import java.sql.Timestamp;

public class ComModel {
    /** 创建时间  */
    private Timestamp createTime;
    /** 创建人ID */
    private Long createOperId;
    /** 创建人姓名 */
    private String createOperName;
    /** 修改时间  */
    private Timestamp updateTime;
    /** 修改人ID */
    private Long updateOperId;
    /** 修改人姓名 */
    private String updateOperName;
    /** 手机号/ */
    private String userName;
    /** 组织机构ID */

    private String orgId;

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Long getCreateOperId() {
        return createOperId;
    }

    public void setCreateOperId(Long createOperId) {
        this.createOperId = createOperId;
    }

    public String getCreateOperName() {
        return createOperName;
    }

    public void setCreateOperName(String createOperName) {
        this.createOperName = createOperName;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public Long getUpdateOperId() {
        return updateOperId;
    }

    public void setUpdateOperId(Long updateOperId) {
        this.updateOperId = updateOperId;
    }

    public String getUpdateOperName() {
        return updateOperName;
    }

    public void setUpdateOperName(String updateOperName) {
        this.updateOperName = updateOperName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
