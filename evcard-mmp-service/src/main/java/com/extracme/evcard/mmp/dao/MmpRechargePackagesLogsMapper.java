package com.extracme.evcard.mmp.dao;

import java.util.List;

import com.extracme.evcard.mmp.dto.MmpRechargePackagesLogsDTO;
import com.extracme.evcard.mmp.model.MmpRechargePackagesLogs;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

@OecsMapper
public interface MmpRechargePackagesLogsMapper extends Dao<MmpRechargePackagesLogs> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_logs
     *
     * @mbggenerated Wed Apr 25 09:02:51 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_logs
     *
     * @mbggenerated Wed Apr 25 09:02:51 CST 2018
     */
    int insert(MmpRechargePackagesLogs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_logs
     *
     * @mbggenerated Wed Apr 25 09:02:51 CST 2018
     */
    int insertSelective(MmpRechargePackagesLogs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_logs
     *
     * @mbggenerated Wed Apr 25 09:02:51 CST 2018
     */
    MmpRechargePackagesLogs selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_logs
     *
     * @mbggenerated Wed Apr 25 09:02:51 CST 2018
     */
    int updateByPrimaryKeySelective(MmpRechargePackagesLogs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_logs
     *
     * @mbggenerated Wed Apr 25 09:02:51 CST 2018
     */
    int updateByPrimaryKeyWithBLOBs(MmpRechargePackagesLogs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_logs
     *
     * @mbggenerated Wed Apr 25 09:02:51 CST 2018
     */
    int updateByPrimaryKey(MmpRechargePackagesLogs record);

	List<MmpRechargePackagesLogsDTO> queryLogByPackagesId(Long packagesId);
}