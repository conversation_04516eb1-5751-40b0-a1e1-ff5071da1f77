package com.extracme.evcard.mmp.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.dto.MmpQrCodeManagementDTO;
import com.extracme.evcard.mmp.model.MmpQrCodeManagement;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

@OecsMapper
public interface MmpQrCodeManagementMapper extends Dao<MmpQrCodeManagement> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_qr_code_management
     *
     * @mbggenerated Tue Aug 14 14:54:44 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_qr_code_management
     *
     * @mbggenerated Tue Aug 14 14:54:44 CST 2018
     */
    int insert(MmpQrCodeManagement record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_qr_code_management
     *
     * @mbggenerated Tue Aug 14 14:54:44 CST 2018
     */
    int insertSelective(MmpQrCodeManagement record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_qr_code_management
     *
     * @mbggenerated Tue Aug 14 14:54:44 CST 2018
     */
    MmpQrCodeManagement selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_qr_code_management
     *
     * @mbggenerated Tue Aug 14 14:54:44 CST 2018
     */
    int updateByPrimaryKeySelective(MmpQrCodeManagement record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_qr_code_management
     *
     * @mbggenerated Tue Aug 14 14:54:44 CST 2018
     */
    int updateByPrimaryKey(MmpQrCodeManagement record);
    
    /**
     * 查询二维码列表
     * @param qrName 二维码名称
     * @param id 二维码ID
     * @param orgId 运营公司
     * @param qrType 二维码类型
     * @return
     */
	List<MmpQrCodeManagementDTO> queryQRCodeList(@Param("qrName") String qrName, @Param("id") Long id, @Param("orgId") String orgId, @Param("qrType") Integer qrType);
	
	/**
	 * 逻辑删除二维码
	 * @param id
	 * @return
	 */
	int updateQRCodeStatus(@Param("id") Long id);

	MmpQrCodeManagementDTO getQRCodeInfo(@Param("id") Long id);

}