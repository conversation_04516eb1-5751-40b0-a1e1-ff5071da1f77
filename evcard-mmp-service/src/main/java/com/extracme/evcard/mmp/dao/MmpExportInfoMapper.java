package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.bo.QueryExportInfoBO;
import com.extracme.evcard.mmp.dto.MmpExportInfoDTO;
import com.extracme.evcard.mmp.model.MmpExportInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MmpExportInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_export_info
     *
     * @mbggenerated Fri Apr 12 06:48:29 CST 2019
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_export_info
     *
     * @mbggenerated Fri Apr 12 06:48:29 CST 2019
     */
    int insert(MmpExportInfo record);

    /**
     * 写入记录并返回主键
     * @param record
     * @return
     */
    int insertWithId(MmpExportInfo record);

    /**
     * 新建导出记录
     * @param record
     * @return
     */
    int insertRecord(MmpExportInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_export_info
     *
     * @mbggenerated Fri Apr 12 06:48:29 CST 2019
     */
    int insertSelective(MmpExportInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_export_info
     *
     * @mbggenerated Fri Apr 12 06:48:29 CST 2019
     */
    MmpExportInfo selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_export_info
     *
     * @mbggenerated Fri Apr 12 06:48:29 CST 2019
     */
    int updateByPrimaryKeySelective(MmpExportInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_export_info
     *
     * @mbggenerated Fri Apr 12 06:48:29 CST 2019
     */
    int updateByPrimaryKey(MmpExportInfo record);


    /**
     * 批量查询文件信息
     * @param orgIds
     * @return
     */
    List<MmpExportInfo> selectByIds(@Param("list") List<Long> orgIds);

    /**
     * 获取文件导出列表，默认过滤过期文件
     * @param queryExportInfoBO
     * @return
     */
    List<MmpExportInfoDTO> queryExportInfoList(QueryExportInfoBO queryExportInfoBO);

    /**
     * 获取文件导出列表(关联组织机构)，默认过滤过期文件
     * @param queryExportInfoBO
     * @return
     */
    List<MmpExportInfoDTO> queryExportInfoListWithOrg(QueryExportInfoBO queryExportInfoBO);

    /**
     * 更新已过期文件记录
     * @param startTime
     * @param endTime
     * @return
     */
    int updateExpireFiles(@Param("list") List<Long> fileTypes,
                          @Param("startTime") String startTime,
                          @Param("endTime") String endTime);

    /**
     * 获取当日过期的文件列表
     * @param startTime
     * @param endTime
     * @return
     */
    List<MmpExportInfo> queryDailyExpireFileList(@Param("list") List<Long> fileTypes,
                                                 @Param("startTime") String startTime,
                                                 @Param("endTime") String endTime);

}