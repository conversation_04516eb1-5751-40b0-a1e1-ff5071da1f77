package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.model.MmpProvisionUserFeedback;
import com.extracme.evcard.rpc.dto.Page;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface MmpProvisionUserFeedbackMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_user_feedback
     *
     * @mbggenerated Wed Sep 30 13:59:28 CST 2020
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_user_feedback
     *
     * @mbggenerated Wed Sep 30 13:59:28 CST 2020
     */
    int insert(MmpProvisionUserFeedback record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_user_feedback
     *
     * @mbggenerated Wed Sep 30 13:59:28 CST 2020
     */
    int insertSelective(MmpProvisionUserFeedback record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_user_feedback
     *
     * @mbggenerated Wed Sep 30 13:59:28 CST 2020
     */
    MmpProvisionUserFeedback selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_user_feedback
     *
     * @mbggenerated Wed Sep 30 13:59:28 CST 2020
     */
    int updateByPrimaryKeySelective(MmpProvisionUserFeedback record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_user_feedback
     *
     * @mbggenerated Wed Sep 30 13:59:28 CST 2020
     */
    int updateByPrimaryKey(MmpProvisionUserFeedback record);

    List<MmpProvisionUserFeedback> queryPageByNodeId(@Param("nodeId") String nodeId, @Param("provisionType") int type,
                                                     @Param("startDate") Date startDate,
                                                     @Param("endDate") Date endDate);

    List<MmpProvisionUserFeedback> queryByNodeId(@Param("nodeId") String nodeId, @Param("provisionType") int type,
                                                 @Param("startDate") Date startDate,
                                                 @Param("endDate") Date endDate,
                                                 @Param("id") Long id, @Param("limitSize") Integer limit);
}