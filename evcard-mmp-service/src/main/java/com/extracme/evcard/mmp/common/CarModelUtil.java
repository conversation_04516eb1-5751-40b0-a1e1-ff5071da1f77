package com.extracme.evcard.mmp.common;

import com.extracme.evcard.mmp.dao.MmpPackNightActivityMapper;
import com.extracme.evcard.mmp.dto.VehicleModel;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * \* Created with IntelliJ IDEA.
 * \* User: Elin
 * \* Date: 2018/1/16
 * \* Time: 14:40
 * \* To change this template use File | Settings | File Templates.
 * \* Description:
 * \
 */
public class CarModelUtil {

    private static String[] carlicense = {"京", "津", "沪", "渝", "藏", "川", "鄂", "甘", "赣", "贵", "桂", "黑", "吉", "冀", "晋", "辽", "鲁",
            "蒙", "闽", "宁", "青", "琼", "陕", "苏", "皖", "湘", "新", "豫", "粤", "云", "浙", "台", "港", "澳"};
    private static String[] letter = {"A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q",
            "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l",
            "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"};

    /**
     * 验证车牌
     *
     * @param address
     * @param vo
     * @return
     */
    public static boolean checkAddress(String address, DefaultServiceRespDTO vo) {
        address = address.replace("，", ",");
        String c = "";
        c = address.substring(address.length() - 1, address.length());
        if (",".equals(c)) {
            vo.setMessage("地域限制不能以逗号结尾");
            return false;
        }
        String[] split = address.split(",");
        // 地区限制个数不能超过10个
        if (split.length > 15) {
            vo.setMessage("车牌限制数量不能超过15个");
            return false;
        }
        for (String str : split) {
            if (null == str || "".equals(str)) {
                vo.setMessage("地域限制逗号间隔之间的车牌号不能为空");
                return false;
            }
            if (str.length() > 2) {
                vo.setMessage("地域限制车牌长度填写错误--" + str);
                return false;
            }
            // 第一个字符必须在全国车牌简称中
            c = str.substring(0, 1);
            if (!Arrays.asList(carlicense).contains(c)) {
                vo.setMessage("地域限制车牌简称填写错误--" + str);
                return false;
            }
            if (str.length() == 2) {
                // 第二个字符必须在26个子母中
                c = str.substring(1, 2);
                if (!Arrays.asList(letter).contains(c)) {
                    vo.setMessage("地域限制车牌号填写错误--" + str);
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 验证车型
     *
     * @param vehicleModle
     * @param vo
     * @return
     */
    public static String checkVehicleModel(String vehicleModle, DefaultServiceRespDTO vo,
                                           MmpPackNightActivityMapper mmpPackNightActivityMapper) {
        vehicleModle = vehicleModle.replace("，", ",");
        String result = "";
        List<String> resultList = new ArrayList<String>();
        String c = "";
        c = vehicleModle.substring(vehicleModle.length() - 1, vehicleModle.length());
        if (",".equals(c)) {
            vo.setMessage("车型限制不能以逗号结尾");
            return "";
        }
        String[] split = vehicleModle.split(",");
        // 20250305紧急去掉限制；产品支莉
        /*if (split.length > 10) {
            vo.setMessage("车型限制不能超过10个车型");
            return "";
        }*/
        /**
         * 改为校验商品车型
         * TODO 门店-配置时暂不校验具体商品车型-待完善
         */
        //List<VehicleModel> vehicleModels = queryVehicleModelList
        //List<VehicleModel> vehicleModels = mmpPackNightActivityMapper.queryVehicleModel();
        for (String str : split) {
            if (null == str || "".equals(str)) {
                vo.setMessage("车型限制逗号间隔之间的车型不能为空");
                return "";
            }
            String value = str;
            resultList.add(value);
//            String value = "";
//            for (VehicleModel model : vehicleModels) {
//                if (str.equals(model.getVehicleModelSeq())) {
//                    value = model.getVehicleModelSeq();
//                    resultList.add(model.getVehicleModelSeq());
//                    break;
//                }
//            }

            if (StringUtils.isEmpty(value)) {
                vo.setMessage("未找到对应的车型--" + str);
                return "";
            }
        }
        result = StringUtils.join(resultList.toArray(), ",");
        return result;
    }

}