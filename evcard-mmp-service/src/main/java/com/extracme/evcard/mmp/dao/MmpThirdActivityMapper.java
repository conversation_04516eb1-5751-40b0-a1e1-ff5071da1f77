package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.model.MmpThirdActivity;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@OecsMapper
public interface MmpThirdActivityMapper extends Dao<MmpThirdActivity> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_third_activity
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_third_activity
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    int insert(MmpThirdActivity record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_third_activity
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    int insertSelective(MmpThirdActivity record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_third_activity
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    MmpThirdActivity selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_third_activity
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    int updateByPrimaryKeySelective(MmpThirdActivity record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_third_activity
     *
     * @mbggenerated Wed Dec 13 14:07:18 CST 2017
     */
    int updateByPrimaryKey(MmpThirdActivity record);

    List<MmpThirdActivity> selectChannelThirdActivityByAppKey(String appKey);

    List<MmpThirdActivity> selectActivityByIds(@Param("list") Set<Long> activityIds);
}