package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.bo.RechargePackagesTemplateBO;
import com.extracme.evcard.mmp.dto.RechargePackagesTemplateFullDTO;
import com.extracme.evcard.mmp.dto.RechargePackagesTemplatePageDTO;
import com.extracme.evcard.mmp.dto.RechargePackagesTemplateSelectDTO;
import com.extracme.evcard.mmp.model.MmpRechargePackagesTemplate;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@OecsMapper
public interface MmpRechargePackagesTemplateMapper extends Dao<MmpRechargePackagesTemplate> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_template
     *
     * @mbggenerated Thu Apr 19 09:33:05 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_template
     *
     * @mbggenerated Thu Apr 19 09:33:05 CST 2018
     */
    int insert(MmpRechargePackagesTemplate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_template
     *
     * @mbggenerated Thu Apr 19 09:33:05 CST 2018
     */
    int insertSelective(MmpRechargePackagesTemplate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_template
     *
     * @mbggenerated Thu Apr 19 09:33:05 CST 2018
     */
    MmpRechargePackagesTemplate selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_template
     *
     * @mbggenerated Thu Apr 19 09:33:05 CST 2018
     */
    int updateByPrimaryKeySelective(MmpRechargePackagesTemplate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_template
     *
     * @mbggenerated Thu Apr 19 09:33:05 CST 2018
     */
    int updateByPrimaryKey(MmpRechargePackagesTemplate record);

    /**
     * 根据套餐id查询绑定的活动
     *
     * @param packagesId
     * @return
     */
    Integer getBindingPackagesActivity(Long packagesId);

    List<RechargePackagesTemplatePageDTO> queryRechargePackagesTemplateList(RechargePackagesTemplateBO templateBO);

    int updateRechargePackagesStatus(@Param("packagesId") Long packagesId, @Param("packagesStatus") Integer packagesStatus);

    List<RechargePackagesTemplateSelectDTO> getOrganizationsAllPackagesByOrgId(@Param("orgId") String orgId);

    Integer selectPackagesByType(@Param("packagesId") Long id, @Param("packagesType") String packagesType);

    List<String> querySavedRechargeTypes();
}