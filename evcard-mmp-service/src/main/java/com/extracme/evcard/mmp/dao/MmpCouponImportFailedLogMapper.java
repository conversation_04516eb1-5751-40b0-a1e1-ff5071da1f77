package com.extracme.evcard.mmp.dao;

import java.util.List;

import com.extracme.evcard.mmp.dto.CouponImportFailedDTO;
import com.extracme.evcard.mmp.model.MmpCouponImportFailedLog;

public interface MmpCouponImportFailedLogMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_coupon_import_failed_log
	 * @mbg.generated  Mon Sep 17 23:15:01 CST 2018
	 */
	int deleteByPrimaryKey(Long id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_coupon_import_failed_log
	 * @mbg.generated  Mon Sep 17 23:15:01 CST 2018
	 */
	int insert(MmpCouponImportFailedLog record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_coupon_import_failed_log
	 * @mbg.generated  Mon Sep 17 23:15:01 CST 2018
	 */
	int insertSelective(MmpCouponImportFailedLog record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_coupon_import_failed_log
	 * @mbg.generated  Mon Sep 17 23:15:01 CST 2018
	 */
	MmpCouponImportFailedLog selectByPrimaryKey(Long id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_coupon_import_failed_log
	 * @mbg.generated  Mon Sep 17 23:15:01 CST 2018
	 */
	int updateByPrimaryKeySelective(MmpCouponImportFailedLog record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_coupon_import_failed_log
	 * @mbg.generated  Mon Sep 17 23:15:01 CST 2018
	 */
	int updateByPrimaryKey(MmpCouponImportFailedLog record);

	List<CouponImportFailedDTO> queryFailedLogByActivityId(Long activityId);
	
	List<MmpCouponImportFailedLog> queryFailedLogsByActivityId(Long activityId);
}