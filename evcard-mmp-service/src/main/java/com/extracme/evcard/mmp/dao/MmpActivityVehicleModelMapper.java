package com.extracme.evcard.mmp.dao;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.model.MmpActivityVehicleModel;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

/**
 * Mapper类，，对应表mmp_activity_vehicle_model
 */
@OecsMapper
public interface MmpActivityVehicleModelMapper extends Dao<MmpActivityVehicleModel> {
       /**
        * @param string string
        * @return vo vo
        */
    int deleteByActivityTypeId(@Param("string") String string);

}