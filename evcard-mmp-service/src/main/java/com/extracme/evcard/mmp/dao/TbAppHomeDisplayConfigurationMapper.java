package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.dto.activity.SearchAppHomeConfigTo;
import com.extracme.evcard.mmp.model.TbAppHomeDisplayConfiguration;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface TbAppHomeDisplayConfigurationMapper {
    /**
     * 新增
     * @param record
     * @return
     */
    int insertSelective(TbAppHomeDisplayConfiguration record);

    /**
     * 查询
     * @param id
     * @return
     */
    TbAppHomeDisplayConfiguration selectByPrimaryKey(Long id);

    /**
     * 更新
     * @param record
     * @return
     */
    int updateByPrimaryKeySelective(TbAppHomeDisplayConfiguration record);

    /**
     * 查询已存在的数据
     * @param orgId
     * @param configType
     * @param displayPlatform
     * @return
     */
    List<TbAppHomeDisplayConfiguration> selectExistConfig(@Param("orgId") String orgId,
                                                          @Param("configType") Integer configType,
                                                          @Param("displayPlatform") Integer displayPlatform,
                                                          @Param("productLineList") List<Integer> productLineList);

    /**
     * 查询列表
     * @param to
     * @return
     */
    List<TbAppHomeDisplayConfiguration> selectList(SearchAppHomeConfigTo to);

    /**
     * 查询数量
     * @param to
     * @return
     */
    long selectCount(SearchAppHomeConfigTo to);

    /**
     * 时间到期后，自动失效
     */
    void updateExpiredInfo();

    /**
     * 查询生效的数据
     * @param orgId
     * @param configType
     * @param currentDate
     * @param displayPlatform
     * @param productLine
     * @return
     */
    TbAppHomeDisplayConfiguration selectCurrentEffectiveConfig(@Param("orgId") String orgId,
                                                               @Param("configType") Integer configType,
                                                               @Param("currentDate") Date currentDate,
                                                               @Param("displayPlatform") Integer displayPlatform,
                                                               @Param("productLine") Integer productLine);

}