package com.extracme.evcard.mmp.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.dto.MmpWelcomeDataPageDTO;
import com.extracme.evcard.mmp.model.MmpWelcomeDataPage;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

@OecsMapper
public interface MmpWelcomeDataPageMapper  extends Dao<MmpWelcomeDataPage> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_welcome_data_page
     *
     * @mbggenerated Fri Jul 13 10:10:52 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_welcome_data_page
     *
     * @mbggenerated Fri Jul 13 10:10:52 CST 2018
     */
    int insert(MmpWelcomeDataPage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_welcome_data_page
     *
     * @mbggenerated Fri Jul 13 10:10:52 CST 2018
     */
    int insertSelective(MmpWelcomeDataPage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_welcome_data_page
     *
     * @mbggenerated Fri Jul 13 10:10:52 CST 2018
     */
    MmpWelcomeDataPage selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_welcome_data_page
     *
     * @mbggenerated Fri Jul 13 10:10:52 CST 2018
     */
    int updateByPrimaryKeySelective(MmpWelcomeDataPage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_welcome_data_page
     *
     * @mbggenerated Fri Jul 13 10:10:52 CST 2018
     */
    int updateByPrimaryKey(MmpWelcomeDataPage record);

	List<MmpWelcomeDataPageDTO> queryWelcomePageStatistics(@Param("id") Long id);
}