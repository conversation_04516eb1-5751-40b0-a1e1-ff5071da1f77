package com.extracme.evcard.mmp.common;

import java.rmi.RemoteException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

import org.apache.commons.httpclient.URIException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.dahantc.api.sms.json.JSONHttpClient;
import com.dahantc.api.voice.json.HttpJSONClient;
import com.dahantc.api.voice.json.VoiceReqData;

/**
 * 大汉三通
 * <AUTHOR>
 *
 */
public class SMSUtil {

    private static final Logger logger = LoggerFactory.getLogger(SMSUtil.class);

    private static String account = "dh17761";// 用户名（必填）dh17762用于终端唤醒
    private static String password = "80nLSu6a";// 密码（必填）
    public static String sign = "【EVCARD】"; // 短信签名（必填）
    public static String subcode = ""; // 子号码（可选）
    public static String msgid = UUID.randomUUID().toString().replace("-", ""); // 短信id，查询短信状态报告时需要，（可选）
    public static String sendtime = ""; // 定时发送时间（可选）

    public static String URL = "http://www.dh3t.com";

    public static String VOICE_URL = "http://voice.3tong.net";

    public static void main(String[] args) throws RemoteException {
        SMSUtil.SendSMS("***********", "测试测试");
    }

    /**
     * 获取短信剩余条数
     * @return
     * @throws RemoteException 
     */
    public static int GetBalance() throws RemoteException {
        return 0;
    }

    /**
     * 发送短信
     * @param mobile  群发时手机号码之间用英文半角逗号隔开 例如 ***********,***********
     * @param smscontent
     * @return
     */
    public static int SendSMS(String mobile, String smscontent) {

        JSONHttpClient jsonHttpClient;
        try {
            jsonHttpClient = new JSONHttpClient(URL);
            jsonHttpClient.setRetryCount(1);
            String sendhRes = jsonHttpClient.sendSms(account, password, mobile, smscontent, sign, subcode);
            logger.info("提交单条普通短信响应：" + sendhRes);
        } catch (URIException e) {
            logger.error("", e);
            return -1;
        }

        // CCPRestSDK instance;
        // instance = SmsCCPRClient.getInstance();
        // HashMap<String, Object> result = instance.sendTemplateSMS(mobile,
        // "96057", new String[]{smscontent});
        // if("000000".equals(result.get("statusCode"))){
        // return 0;
        // } else {
        // logger.error("错误码=" + result.get("statusCode")
        // +" 错误信息= "+result.get("statusMsg"));
        // }
        return 0;

    }

    /**
     * 发送语音验证码
     * @param mobile
     * @param smscontent 只能够是数字
     * @return
     */
    public static int voiceVerify(String mobile, String smscontent) {

        String reg = "^\\d+$";
        if (!Pattern.compile(reg).matcher(smscontent).find()) {
            logger.error(smscontent + " is not digital");
            return -1;
        }

        try {
            // 初始化客户端
            HttpJSONClient client = new HttpJSONClient(VOICE_URL);

            List<VoiceReqData> list = new ArrayList<VoiceReqData>();
            list.add(new VoiceReqData(mobile, smscontent, "", UUID.randomUUID().toString().replace("-", ""), 1, 0));// 平台默认提示音
            // 验证码短信
            String submitResp = client.sendAuthCodeVoiceSms(account, password, list);
            System.out.println(submitResp);

            String reportResp = client.getVoiceReport(account, password);

            System.out.println(reportResp);
        } catch (Exception e) {
            logger.error("", e);
            return -1;
        }
        return 0;

        // CCPRestSDK instance = SmsCCPRClient.getInstance();
        // HashMap<String, Object> result = instance.voiceVerify(smscontent,
        // mobile, null, "3", null, null, null);
        // if("000000".equals(result.get("statusCode"))){
        // return 0;
        // } else {
        // logger.error("错误码=" + result.get("statusCode")
        // +" 错误信息= "+result.get("statusMsg"));
        // }
        // return -1;

    }
}