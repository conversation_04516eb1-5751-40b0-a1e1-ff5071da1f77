package com.extracme.evcard.mmp.common;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.Producer;
import com.aliyun.openservices.ons.api.SendResult;
import com.aliyun.openservices.ons.api.exception.ONSClientException;
import com.extracme.evcard.mmp.config.OnsConfig;
import com.extracme.evcard.mmp.model.UnregisterCouponDto;
import com.extracme.evcard.mq.bean.EventEnum;
import com.extracme.evcard.mq.bean.event.*;
import com.extracme.evcard.protobuf.ProtobufUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ProduceFactory {

    @Resource
    private Producer producer;

    @Autowired
    private OnsConfig onsConfig;


    private String topic;
    private String tag;
    private String messageKey;
    private byte[] messageBody;

    private static final Log logger = LogFactory.getLog(ProduceFactory.class);

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }
    public void setMessageKey(String messageKey) {
        this.messageKey = messageKey;
    }

    public void setMessageBody(byte[] messageBody) {
        this.messageBody = messageBody;
    }

    public ProduceFactory() {

    }

    /**

     * @param topic
     * @param tag
     * @param messageBody
     */
    public ProduceFactory(String topic, String tag, byte[] messageBody) {
        this.topic = topic;
        this.tag = tag;
        this.messageBody = messageBody;
    }

    /**
     * @param topic
     * @param tag
     * @param messageBody
     * @param messageKey
     */
    public ProduceFactory(String topic, String tag, byte[] messageBody, String messageKey) {
        this.topic = topic;
        this.tag = tag;
        this.messageKey = messageKey;
        this.messageBody = messageBody;
    }

    public boolean sendMessage() {
        Message msg = new Message(topic, tag, messageBody);
        msg.setKey(messageKey);
        try {
            SendResult sendResult = producer.send(msg);
            assert sendResult != null;
            return true;
        } catch (ONSClientException e) {
            logger.error("", e);
            return false;
        }
    }

    /**
     * @param topic
     * @param tag
     * @param messageBody
     * @param messageKey
     * @return
     */
    public boolean sendMessage(String topic, String tag, byte[] messageBody, String messageKey) {
        Message msg = new Message(topic, tag, messageBody);
        msg.setKey(messageKey);
        try {
            SendResult sendResult = producer.send(msg);
            assert sendResult != null;
            return true;
        } catch (ONSClientException e) {
            logger.error("", e);
            return false;
        }
    }

    /**
     * 会员审核事件上传消息队列MQ方法
     */
    public Map<String, String> uploadMemberAuditMessageQueue(MemberAudit memberAudit) {
        Map<String, String> result = new HashMap<>();
        byte[] messagebody = ProtobufUtil.serializeProtobuf(memberAudit);
        String topic = onsConfig.getOnsTopic();
        Message msg = new Message(topic, "MEMBER_AUDIT", messagebody);
        try {
            SendResult sendResult = producer.send(msg);
            result.put("status", "1");
            result.put("msg", "发送成功" + sendResult.getMessageId());
        } catch (Exception e) {
            result.put("status", "0");
            result.put("msg", "发送失败" + memberAudit.getAuthId() + "|||" + e);
        }
        return result;
    }

    /**
     * 会员删除事件上传消息队列MQ方法
     */
    public Map<String, String> uploadMemberDeleteMessageQueue(MemberUnRegister memberUnRegister) {
        Map<String, String> result = new HashMap<>();
        byte[] messagebody = ProtobufUtil.serializeProtobuf(memberUnRegister);
        String topic = onsConfig.getOnsTopic();
        Message msg = new Message(topic,EventEnum.MEMBER_UNREGISTER.getTag() , messagebody);
        try {
            SendResult sendResult = producer.send(msg);
            result.put("status", "1");
            result.put("msg", "发送成功" + sendResult.getMessageId());
        } catch (Exception e) {
            result.put("status", "0");
            result.put("msg", "发送失败" + memberUnRegister.getMid() + "|||" + e);
        }
        return result;
    }

    /**
     * 会员邀请好友送券事件上传消息队列MQ方法
     */
    public Map<String, String> uploadMemberInvitationMessageQueue(MemberInvitation memberInvitation) {
        Map<String, String> result = new HashMap<>();
        byte[] messagebody = ProtobufUtil.serializeProtobuf(memberInvitation);
        String topic = onsConfig.getOnsTopic();
        Message msg = new Message(topic, "MEMBER_INVITATION", messagebody);
        try {
            SendResult sendResult = producer.send(msg);
            result.put("status", "1");
            result.put("msg", "发送成功" + sendResult.getMessageId());
        } catch (Exception e) {
            result.put("status", "0");
            result.put("msg", "发送失败" + memberInvitation.getAuthId() + "|||" + e);
        }
        return result;
    }

    /**
     * 会员注册事件推送
     *
     * @param memberRegister
     * @return
     */
    public Map<String, String> uploadMemberRegisterMessageQueue(MemberRegister memberRegister) {
        Map<String, String> result = new HashMap<>();
        byte[] messagebody = ProtobufUtil.serializeProtobuf(memberRegister);
        String topic = onsConfig.getOnsTopic();
        Message msg = new Message(topic, "MEMBER_REGISTER", messagebody);
        try {
            SendResult sendResult = producer.send(msg);
            result.put("status", "1");
            result.put("msg", "发送成功" + sendResult.getMessageId());
        } catch (Exception e) {
            result.put("status", "0");
            result.put("msg", "发送失败" + memberRegister.getAuthId() + "|||" + e);
        }
        return result;
    }

    /**
     * 充值e币事件推送
     * @param rechargeEcoin
     * @return
     */
    public Map<String,String> uploadMemberEMessageQueue(MemberRechargeEcoin rechargeEcoin) {
        Map<String, String> result = new HashMap<>();
        byte[] messagebody = ProtobufUtil.serializeProtobuf(rechargeEcoin);
        String topic = onsConfig.getOnsTopic();
        Message msg = new Message(topic, "MEMBER_RECHARGE_ECOIN", messagebody);
        try {
            SendResult sendResult = producer.send(msg);
            result.put("status", "1");
            result.put("msg", "发送成功" + sendResult.getMessageId());
        } catch (Exception e) {
            result.put("status", "0");
            result.put("msg", "发送失败" + rechargeEcoin.getAuthId() + "|||" + e);
        }
        return result;
    }
    
    /**
     * 优惠券导入事件推送
     * @param couponImport
     * @return
     */
    public Map<String,String> pushSendCouponMessage(CouponImport couponImport) {
        logger.warn("推送批量导入发券事件:topic=" + onsConfig.getOnsCouponTopic());
        Map<String, String> result = new HashMap<>();
        byte[] messagebody = ProtobufUtil.serializeProtobuf(couponImport);
        String topic = onsConfig.getOnsCouponTopic();
        Message msg = new Message(topic, EventEnum.IMPORT_COUPON.toString(), messagebody);
        try {
            producer.sendOneway(msg);
            /*SendResult sendResult = ProduceUtil.getProducer().send(msg);
            result.put("status", "1");
            result.put("msg", "发送优惠券导入事件推送成功" + sendResult.getMessageId());*/
        } catch (Exception e) {
            result.put("status", "0");
            result.put("msg", "发送优惠券导入事件推送失败" + couponImport.getAuthId() + "|||" + e);
        }
        return result;
    }

    public Map<String,String> pushRegisterCouponMessage(List<UnregisterCouponDto> unregisterCouponDtos) {
        logger.warn("推送批量导入发券事件:topic=" + onsConfig.getOnsTopic());
        Map<String, String> result = new HashMap<>();
        byte[] messagebody = ProtobufUtil.serializeListProtobuf(unregisterCouponDtos);
        String topic = onsConfig.getOnsTopic();
        Message msg = new Message(topic, EventEnum.MEMBER_REGISTER_COUPON.toString(), messagebody);
        try {
            producer.sendOneway(msg);
            /*SendResult sendResult = ProduceUtil.getProducer().send(msg);
            result.put("status", "1");
            result.put("msg", "发送优惠券导入事件推送成功" + sendResult.getMessageId());*/
        } catch (Exception e) {
            result.put("status", "0");
            result.put("msg", "发送优惠券导入事件推送失败" + JSON.toJSONString(unregisterCouponDtos) + "|||" + e);
        }
        return result;
    }
}
