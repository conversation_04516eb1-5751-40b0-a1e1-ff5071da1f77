package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.model.MmpShortLinkManagement;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Component;

@Component
public interface MmpShortLinkManagementMapper {

    int deleteByPrimaryKey(Long id);

    Long insert(MmpShortLinkManagement record);

    int insertSelective(MmpShortLinkManagement record);

    MmpShortLinkManagement selectByPrimaryKey(Long id);

    int updateByDto(MmpShortLinkManagement record);

    int updateByPrimaryKey(MmpShortLinkManagement record);

    List<MmpShortLinkManagement> selectByKeys(String shortLinkName, String shortLinkUrl, String originalUrl, String randomCode, Date failureTime, Integer status);

    List<MmpShortLinkManagement> selectNotHiddenByKey(String shortLinkName, String shortLinkUrl, String originalUrl, String randomCode, Date failureTime, Integer status);

    int deleteByShortLinkNameOneWeek(String shortLinkName);
}