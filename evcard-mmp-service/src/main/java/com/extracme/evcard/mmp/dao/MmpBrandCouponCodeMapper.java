package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.dto.BrandCodeExportDTO;
import com.extracme.evcard.mmp.model.MmpBrandCouponCode;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@OecsMapper
public interface MmpBrandCouponCodeMapper extends Dao<MmpBrandCouponCode> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_brand_coupon_code
     *
     * @mbggenerated Tue Jul 03 10:48:39 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_brand_coupon_code
     *
     * @mbggenerated Tue Jul 03 10:48:39 CST 2018
     */
    int insert(MmpBrandCouponCode record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_brand_coupon_code
     *
     * @mbggenerated Tue Jul 03 10:48:39 CST 2018
     */
    int insertSelective(MmpBrandCouponCode record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_brand_coupon_code
     *
     * @mbggenerated Tue Jul 03 10:48:39 CST 2018
     */
    MmpBrandCouponCode selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_brand_coupon_code
     *
     * @mbggenerated Tue Jul 03 10:48:39 CST 2018
     */
    int updateByPrimaryKeySelective(MmpBrandCouponCode record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_brand_coupon_code
     *
     * @mbggenerated Tue Jul 03 10:48:39 CST 2018
     */
    int updateByPrimaryKey(MmpBrandCouponCode record);

    int batchInsertBrandCouponCode(List<MmpBrandCouponCode> brandCouponCodes);

    Integer findAllActivityCodeCount(@Param("activityId") Long activityId, @Param("status") Integer status);

    List<BrandCodeExportDTO> selectBrandActivityCouponCodeByActivityId(long activityId);

    List<String> selectBrandCodeListByActivityId(Long activityId);

    MmpBrandCouponCode selectOneBrandCodeToUser(String activityId);

    int offerBrandCodeToUser(Map<String, Object> updateParams);

    int batchInsertShareBrandCouponCode(List<MmpBrandCouponCode> brandCouponCodes);

}