package com.extracme.evcard.mmp.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.dto.ActivityVehicleDTO;
import com.extracme.evcard.mmp.model.MmpActivityType;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

/**
 * Mapper类，，对应表mmp_activity_type
 */
@OecsMapper
public interface MmpActivityTypeMapper extends Dao<MmpActivityType> {
    /**
     * @param valueOf valueOf
     * @return vo vo
     */
    List<String> selectByPackNightActivityId(Long valueOf);

    /**
     * @param id id
     * @return vo vo
     */
    int deletePackNightactivityId(@Param("id") Long id);

    /**
     * @param id id
     * @return vo vo
     */
    List<String> selecId(@Param("id") Long id);

    /**
     * @param id id
     * @return vo vo
     */
    List<ActivityVehicleDTO> queryActivityType(@Param("id") Long id);

}