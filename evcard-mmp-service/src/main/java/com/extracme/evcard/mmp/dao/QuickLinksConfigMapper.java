package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.model.QuickLinksConfig;
import com.extracme.evcard.mmp.model.QuickLinksConfigExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface QuickLinksConfigMapper {
    int countByExample(QuickLinksConfigExample example);

    int deleteByPrimaryKey(Long id);

    int insert(QuickLinksConfig record);

    int insertSelective(QuickLinksConfig record);

    List<QuickLinksConfig> selectByExample(QuickLinksConfigExample example);

    QuickLinksConfig selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") QuickLinksConfig record, @Param("example") QuickLinksConfigExample example);

    int updateByExample(@Param("record") QuickLinksConfig record, @Param("example") QuickLinksConfigExample example);

    int updateByPrimaryKeySelective(QuickLinksConfig record);

    int updateByPrimaryKey(QuickLinksConfig record);
}