package com.extracme.evcard.mmp.common;


import com.extracme.evcard.mmp.config.OssConfigUtil;
import com.extracme.evcard.rpc.coupon.Constants.CouponConsts;

import java.util.Arrays;
import java.util.List;

/**
 * 项目名称：evcard-fas-service
 * 类名称：Contants
 * 类描述：Service层用常量类
 * 创建人：shenjh-沈建华
 * 创建时间：2017年4月11日 下午4:12:35
 * 修改备注：
 * @version1.0
 */
public class Contants {
    public static final Integer STATUS_OK = 0;

    // 请求协议
    public static final String HTTP = "http://";
    public static final String HTTPS = "https://";
    // 左斜杆
    public static final String LEFT_DIAGONAL = "/";
    // 是
    public static final String YES = "是";
    // 否
    public static final String NO = "否";

    // 成功返回的code
    public static final String FLAG_YES = "1";

    // 逗号
    public static final String COMMA_SIGN_SPLIT_NAME = ",";

    public static final String BR = "<br>";

    public static final String BRACKET_BEFORE = "[";

    public static final String BRACKET_AFTER = "]";

    public static final String MARK_COMMA = ",";

    public static final String MARK_COMMA_CH = "，";

    public static final String MARK_SEMICOLON = ";";

    public static final String MARK_SEMICOLON_ZH = "；";

    public static final String MARK_COLON = ":";

    public static final String QUESTION_MARK = "?";
    public static final String QUESTION_REGIX = "//?";


    // 分割图片的后缀名的点号
    public static final String SPLIT_MARK_DOT = "\\.";
    // 分割图片的后缀名的点号
    public static final String MARK_DOT = ".";

    public static final String UPDATETO = "改为";

    public static final String NULLVALUE = "空值";

    // 插入
    public static final String TABLE_OPE_INSERT = "新增";
    // 更新
    public static final String TABLE_OPE_UPDATE = "修改";
    // 删除
    public static final String TABLE_OPE_DELETE = "删除";
    // 启用
    public static final String TABLE_OPE_USING = "启用";
    // 禁用
    public static final String TABLE_OPE_FORBIDDEN = "禁用";
    // 下拉框默认值
    public static final String DROPDOWNLIST_DEFAULT = "-1";
    // 图片上传分目录
    public static final String IMAGE_PATH = "imagePath";
    // 图片服务器路径
    public static final String IMAGE_HTTP_PATH = "imageHttpPath";
    // 成功返回的code
    public static final String RETURN_SUCCESS_CODE = "0";
    // 成功返回的code
    public static final Integer CODE_SUCCESS = 0;
    // 失败返回的code
    public static final String RETURN_ERROR_CODE = "-1";
    // 权限限制返回的code
    public static final String RETURN_AUTHORITY_CODE = "99";
    // mysql的int型最大值
    public static final int MYSQL_INT_MAX = 2147483647;
    // mysql的smallint型最大值
    public static final int MYSQL_SMALLINT_MAX = 32767;

    // xls文件后缀
    public static final String XLS = ".xls";

    // xlsx文件后缀
    public static final String XLSX = ".xlsx";

    // .xlsm文件后缀
    public static final String XLSM = ".xlsm";

    // doc文件后缀
    public static final String DOC = ".doc";

    // 会员信息修改
    public static final String UPDATE_MEMBERSHIP = "修改会员信息";
    // 姓名
    public static final String MEMBERSHIP_NAME = "姓名";
    // 手机号
    public static final String MEMBERSHIP_MOBILEPHONE = "手机号";
    // 驾驶证号
    public static final String MEMBERSHIP_DRIVINGLICENSE = "驾驶证号";
    // 驾照照片
    public static final String MEMBERSHIP_DRIVINGLICENSEIMGURL = "驾照照片";
    // 身份证照片
    public static final String MEMBERSHIP_IDCARDPICURL = "身份证照片";
    // 手持身份照片
    public static final String MEMBERSHIP_HOLDIDCARDPICURL = "手持身份证照片";
    // 邮寄地址
    public static final String MEMBERSHIP_ADDRESS = "邮寄地址";
    // 备注来源
    public static final String MEMBERSHIP_INFOORIGIN = "备注来源";
    // 审核状态
    public static final String MEMBERSHIP_INFO_STATUS = "审核状态";
    // 关联企业
    public static final String AGENCY_NAME = "关联企业";
    // 备注来源
    public static final String INFO_ORIGIN = "备注来源";
    // 连接符
    public static final String CONNECTION_CODE = "-";
    // 不发送短信的标志
    public static final String APP_KEY = "sgm_car_sharing";
    public static final String APP_KEY_IBUICK = "ibuick_car_sharing";
    public static final String APP_KEY_SONG = "sgm_songjiang";
    // 不发送短信的APP_KEY集合
    public static final List<String> NOT_SEND_SMS_LIST = Arrays.asList(APP_KEY, APP_KEY_IBUICK);
    // 不发送APP消息的APP_KEY集合
    public static final List<String> NOT_SEND_MESSAGE_LIST = Arrays.asList(APP_KEY, APP_KEY_IBUICK, APP_KEY_SONG);
    // 押金等级
    public static final String EXEMPT_DEPOSIT_GRADE = "免押等级";
    // 会员所属公司
    public static final String ORG_NAME = "会员所属公司";
    // 领证日期
    public static final String OBTAIN_DRIVER_TIME = "领证日期";
    // 证件到期日期
    public static final String LICENSE_EXPIRATION_TIME = "证件到期日期";
    // 邮箱
    public static final String MAIL = "邮箱";
    // 准驾类型
    public static final String DRIVING_LICENSE_TYPE = "准驾类型";
    // 所在地区
    public static final String REGISTER_AREA = "所在地区";
    // 国籍
    public static final String NATIONAL = "国籍";
    // 押金
    public static final int DEPOSIT_AMOUNT = 1000;
    // 秘钥钥匙
    public static final String ENCRYPT_KEY = "evcardbs";
    // 认证状态
    public static final String MEMBERSHIP_AUTHENTICATION_STATUS = "认证状态";
    // 人脸识别图片
    public static final String MEMBERSHIP_FACERECOGNITIONIMGURL = "人脸识别图片";

    public static final String[] CARLICENSE = { "京", "津", "沪", "渝", "藏", "川", "鄂", "甘", "赣", "贵", "桂", "黑", "吉", "冀", "晋", "辽", "鲁", "蒙", "闽", "宁", "青", "琼", "陕", "苏", "皖", "湘", "新", "豫",
            "粤", "云", "浙", "台", "港", "澳" };

    public static final String[] LETTER = { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "a", "b", "c",
            "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z" };
    public static final String COUPON_BATCH_IMPORT_TOPIC = "ali.mmp.couponImport.topic";

    /**
     * orgId->环球
     */
    public static final String ORG_HQ = "00";
    /**
     * 子公司发券限制
     * @remark 20190717 由216->600
     */
    public static final int ORG_COUPON_MAX = CouponConsts.COUPON_VALUE_MAX;
    public static final Integer[] SERVICE_TYPES = {0,1,2,3,4,11};

    public static final String SYSTEM_CODE = "mmp";

    public static final String OSS_PATH = OssConfigUtil.getBasePath();
    public static final String OSS_ENV = OssConfigUtil.getENV();
    public static final String OSS_BASE_PATH = OSS_PATH + "/" + OSS_ENV;
    public static final String SENSITIVE_BUCKET = "sensitiveBucket";
    public static final String SENSITIVE_BUCKET_KEY = "evcard-sensitive";


    /***
     * 审核项-未审核
     */
    public static final String  REVIEW_ITEMS_INIT = "000000000000";
    /**
     * 审核项-审核通过
     */
    public static final String REVIEW_ITEMS_OK = "111111111111";
    /**
     * 新h5域名
     */
    public static final String COMM_REST_URL = "http://csms.evcard.vip/evcard-rest";
    public static final String H5_REST_URL = "http://csms-h5.evcard.vip/evcard-rest";

    public static final String FOREIGN_NATIONAL = "外国";
    public static final String CHINA_NATIONAL = "中国";
    public static final String CHINA_HKMT_NATIONAL = "港澳台";
    public static final String CHINA_NATIONAL_SOLIER = "中国（军人）";
    public static final String MAX_LICENSE_EXPIRE_TIME = "2099-12-31";
    public static final String MAX_LICENSE_EXPIRE_TIME_DESC = "永久";

    public static final String[] JOBS = {"机关团队公司", "农牧业", "交通运输业","餐旅业","建筑工程业","制造加工维修业","新闻出版印刷业","医疗卫生保健业","买卖（零售批发业）",
            "服务业（金融业/银行/保险/信托/租赁/证券/信合社/邮局/农渔会）","自由业","物业管理","家政管理","司法、治安人员","IT业（软、硬件开发制作）","学生","其他"};

    public static final String[] EDUCATION = {"高中及以下", "大专", "本科","硕士","博士及以上"};


    public static final String[] OWN_CARS = {"无，不打算买车", "无，打算买车", "已有私家车"};


    public static final String[] REVIEW_STATUS_NAMES = new String[]{"资料不全", "待审核", "审核通过", "审核不通过", "用户无效", "重新审核"};

    public static final String SMS_OSS_PATH = "/mmp/smsData/";

    /**
     * evcard 平台 id，特殊的，平台表不存储
     */
    public static final Integer EVCARD_PLATFORM_ID = -9999;

    /**
     * evcard 一级渠道appkey，特殊的，一级渠道表不存储
     */
    public static final String EVCARD_FIRST_APP_KEY = "evcard";

    /**
     * evcard 2级渠道appkey，特殊的，2级渠道表不存储
     */
    public static final String EVCARD_SECOND_APP_KEY = "second_evcard";

    /**
     * evcard 前端展示
     */
    public static final String EVCARD_NAME = "Evcard";

    //渠道注册生成url链接
    public static final String ACTIVITY_URL_CHANNEL_REWARD = "channelRewardActivity";
    //渠道注册兼容历史URL
    public static final String ACTIVITY_URL_CHANNEL_REWARD_OLD = "channelRewardActivityOld";
    //渠道注册文件路径地址
    public static final String ACTIVITY_URL_CHANNEL_REWARD_DIR = "channelRewardActivity";
    //扫码发券生成url链接
    public static final String ACTIVITY_URL_SWEEP_CODE = "sweepTheCodeToSendCoupons";
    //扫码发券文件路径地址
    public static final String ACTIVITY_URL_SWEEP_CODE_DIR = "activityCouponUrl";
    //扫码发券兼容历史URL
    public static final String ACTIVITY_URL_SWEEP_CODE_DIR_OLD = "activityCouponUrlOld";
}
