package com.extracme.evcard.mmp.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.dto.MmpLayerDataPageDTO;
import com.extracme.evcard.mmp.model.MmpLayerDataPage;
import com.extracme.framework.data.dao.Dao;

public interface MmpLayerDataPageMapper  extends Dao<MmpLayerDataPage> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_layer_data_page
     *
     * @mbggenerated Fri Jul 13 10:13:17 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_layer_data_page
     *
     * @mbggenerated Fri Jul 13 10:13:17 CST 2018
     */
    int insert(MmpLayerDataPage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_layer_data_page
     *
     * @mbggenerated Fri Jul 13 10:13:17 CST 2018
     */
    int insertSelective(MmpLayerDataPage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_layer_data_page
     *
     * @mbggenerated Fri Jul 13 10:13:17 CST 2018
     */
    MmpLayerDataPage selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_layer_data_page
     *
     * @mbggenerated Fri Jul 13 10:13:17 CST 2018
     */
    int updateByPrimaryKeySelective(MmpLayerDataPage record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_layer_data_page
     *
     * @mbggenerated Fri Jul 13 10:13:17 CST 2018
     */
    int updateByPrimaryKey(MmpLayerDataPage record);

	List<MmpLayerDataPageDTO> getLayerPageStatistics(@Param("activitySeq") String activitySeq);
}