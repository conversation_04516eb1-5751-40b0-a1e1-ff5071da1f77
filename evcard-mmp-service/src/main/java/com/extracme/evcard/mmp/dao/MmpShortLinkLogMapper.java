package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.model.MmpShortLinkLog;

import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MmpShortLinkLogMapper {

    int deleteByPrimaryKey(Long id);

    int insert(MmpShortLinkLog record);

    int insertSelective(MmpShortLinkLog record);


    MmpShortLinkLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MmpShortLinkLog record);

    int updateByPrimaryKey(MmpShortLinkLog record);

    List<MmpShortLinkLog> selectByLinkedId(@Param("linkedId")Long linkedId);
}