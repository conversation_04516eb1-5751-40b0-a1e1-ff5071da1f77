package com.extracme.evcard.mmp.common;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文件工具类
 *
 * <AUTHOR>
 * @date 2023/1/16
 */
@Slf4j
public class FileUtil {

    /**
     * 从url下载到本地localFile
     *
     * @param urlString
     * @param localFile
     * @return
     */
    public static boolean downloadFromUrl(String urlString, File localFile) {
        InputStream is = null;
        FileOutputStream os = null;
        try {
            URL url = new URL(urlString);
            URLConnection con = url.openConnection();
            is = con.getInputStream();
            os = new FileOutputStream(localFile, true);
            byte[] buffer = new byte[1024];
            int len;
            while ((len = is.read(buffer)) != -1) {
                os.write(buffer, 0, len);
            }
            return true;
        } catch (Exception e) {
            log.error("downloadFromUrl error!", e);
            return false;
        } finally {
            if (os != null) {
                try {
                    os.close();
                } catch (IOException e) {
                    log.error("close os error!", e);
                }
            }
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    log.error("close is error!", e);
                }
            }
        }
    }

    public static void main(String[] args) {
        List<String> localFileFUllPathList = new ArrayList<>();
        localFileFUllPathList.add("C:\\Users\\<USER>\\Desktop\\work\\zip5\\二维码.zip");
        List<String> ossFileNameList = new ArrayList<>();
        ossFileNameList.add("https://evcard.oss-cn-shanghai.aliyuncs.com/test/qrCode/20180903135004418_medium.png");
        ossFileNameList.add("https://evcard.oss-cn-shanghai.aliyuncs.com/test/qrCode/20180904174647367_large.png");
        String zipFilePath = "C:\\Users\\<USER>\\Desktop\\work\\zip5\\";
        String zipFileDirectName = "二维码2.zip";

        File file = fileToZip2(localFileFUllPathList, ossFileNameList, zipFilePath, zipFileDirectName, false);
        System.out.println(file);

        System.out.println(file);

    }

    /**
     * 将本地文件列表打包成zip文件
     *
     * @param localFileFUllPathList
     * @param ossFileNameList
     * @param zipFileName
     * @param isDeleteSourceFile
     * @return
     */
    public static File fileToZip2(List<String> localFileFUllPathList,List<String> ossFileNameList,String zipFilePath,String zipFileName,boolean isDeleteSourceFile) {
        if (CollectionUtils.isEmpty(localFileFUllPathList)  && CollectionUtils.isEmpty(ossFileNameList)) {
            log.warn("File list is empty or null");
            return null;
        }
        // 默认zip文件名
        if (StringUtils.isBlank(zipFileName)) {
            zipFileName = System.currentTimeMillis() + ".zip";
        }

        String fullZipPath = zipFilePath + File.separator + zipFileName;
        // 建立文件夹
        File zipFileDirectory = new File(zipFilePath);
        try {
            FileUtils.forceMkdir(zipFileDirectory);
        } catch (IOException e) {
            log.error("文件夹创建失败");
            return null;
        }

        List<File> localFileList = getFileListFromPathList(localFileFUllPathList);
        List<File> ossFileList = getFileListFromOssFileList(ossFileNameList);
        List<File> fileList = new ArrayList<>();
        fileList.addAll(localFileList);
        fileList.addAll(ossFileList);

        File zipFile = new File(fullZipPath);
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
            for (File file : fileList) {
                if (file == null || !file.exists() || !file.isFile()) {
                    log.warn("Invalid file: {}", file);
                    continue;
                }
                addFileToZip(file, zos);
            }
        } catch (IOException e) {
            log.error("Error creating zip file", e);
            return null;
        }


        if (isDeleteSourceFile) {
            fileList.forEach(File::delete);
        }

        return zipFile;
    }

    /**
     * 从oss文件路径列表中获取文件列表
     *
     * @param ossFileNameList
     * @return
     */
    public static List<File> getFileListFromOssFileList(List<String> ossFileNameList) {
        //UploadImgUtil.downloadStream(inFileName);

        List<File> fileList = new ArrayList<>();
        ossFileNameList.stream().forEach(ossFullFileName-> {
            try {
                String tempFile = "/data/"+ossFullFileName.substring(ossFullFileName.lastIndexOf("/") + 1);;
                File file = new File(tempFile);
                if (downloadFromUrl(ossFullFileName, file)) {
                    fileList.add(file);
                }
            } catch (Exception e) {
                log.error("getFileListFromOssFileList 异常，ossFullFileName={}",ossFullFileName,e);
            }
        });
        return fileList;

    }

    /**
     * 从本地文件路径列表中获取文件列表
     * @param localFileFUllPathList
     * @return
     */
    public static List<File> getFileListFromPathList(List<String> localFileFUllPathList) {
        List<File> fileList = new ArrayList<>();
        localFileFUllPathList.stream().forEach(path-> {
            try {
                fileList.add(new File(path));
            } catch (Exception e) {
                log.error("getFileListFromPathList 异常，path={}",path,e);
            }
        });
        return fileList;
    }

    public static File fileToZip(List<File> fileList,String zipFileName,boolean isDeleteSourceFile) {
        if (fileList == null || fileList.isEmpty()) {
            log.warn("File list is empty or null");
            return null;
        }

        File zipFile = new File(zipFileName);
        try (ZipOutputStream zos = new ZipOutputStream(new FileOutputStream(zipFile))) {
            for (File file : fileList) {
                if (file == null || !file.exists() || !file.isFile()) {
                    log.warn("Invalid file: {}", file);
                    continue;
                }
                addFileToZip(file, zos);
            }
        } catch (IOException e) {
            log.error("Error creating zip file", e);
            return null;
        }

        if (isDeleteSourceFile) {
            fileList.forEach(File::delete);
        }


        return zipFile;
    }

    public static void addFileToZip(File file, ZipOutputStream zos) throws IOException {
        String fileName = file.getName();
        FileInputStream fis = new FileInputStream(file);
        ZipEntry zipEntry = new ZipEntry(fileName);
        zos.putNextEntry(zipEntry);

        byte[] bytes = new byte[1024];
        int length;
        while ((length = fis.read(bytes)) >= 0) {
            zos.write(bytes, 0, length);
        }

        zos.closeEntry();
        fis.close();
    }
}
