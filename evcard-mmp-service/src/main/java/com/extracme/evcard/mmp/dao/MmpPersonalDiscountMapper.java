package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.dto.OperationLogPersonalDetailDTO;
import com.extracme.evcard.mmp.model.MmpPersonalDiscount;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

import java.util.List;

@OecsMapper
public interface MmpPersonalDiscountMapper extends Dao<MmpPersonalDiscount>{
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_personal_discount
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_personal_discount
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    int insert(MmpPersonalDiscount record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_personal_discount
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    int insertSelective(MmpPersonalDiscount record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_personal_discount
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    MmpPersonalDiscount selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_personal_discount
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    int updateByPrimaryKeySelective(MmpPersonalDiscount record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_personal_discount
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    int updateByPrimaryKey(MmpPersonalDiscount record);

    List<OperationLogPersonalDetailDTO> findPersonalList(Long id);
}