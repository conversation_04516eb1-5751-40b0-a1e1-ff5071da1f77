package com.extracme.evcard.mmp.dao;

import java.math.BigDecimal;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.bo.CouponConfigBO;
import com.extracme.evcard.mmp.dto.CouponConfigDTO;
import com.extracme.evcard.mmp.model.MmpCouponConfigure;

public interface MmpCouponConfigureMapper {
    /**
     * This method was generated by MyBatis Generator. This method corresponds
     * to the database table mmp_coupon_configure
     * @mbggenerated Mon Jul 17 16:55:40 CST 2017
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator. This method corresponds
     * to the database table mmp_coupon_configure
     * @mbggenerated Mon Jul 17 16:55:40 CST 2017
     */
    int insert(MmpCouponConfigure record);

    /**
     * This method was generated by MyBatis Generator. This method corresponds
     * to the database table mmp_coupon_configure
     * @mbggenerated Mon Jul 17 16:55:40 CST 2017
     */
    int insertSelective(MmpCouponConfigure record);

    /**
     * This method was generated by MyBatis Generator. This method corresponds
     * to the database table mmp_coupon_configure
     * @mbggenerated Mon Jul 17 16:55:40 CST 2017
     */
    MmpCouponConfigure selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator. This method corresponds
     * to the database table mmp_coupon_configure
     * @mbggenerated Mon Jul 17 16:55:40 CST 2017
     */
    int updateByPrimaryKeySelective(MmpCouponConfigure record);

    /**
     * This method was generated by MyBatis Generator. This method corresponds
     * to the database table mmp_coupon_configure
     * @mbggenerated Mon Jul 17 16:55:40 CST 2017
     */
    int updateByPrimaryKey(MmpCouponConfigure record);

    /**
     * 查询优惠券额度配置信息列表
     * @param couponConfigBO
     * @return
     */
    List<CouponConfigDTO> getCouponConfigList(CouponConfigBO couponConfigBO);

    /**
     * 查询当月配置信息
     * @param orgId
     * @param year
     * @param month
     * @return
     */
    CouponConfigDTO getCouponConfigInfo(@Param("orgId") String orgId, @Param("year") String year, @Param("month") String month);

    List<MmpCouponConfigure> queryLastCouponConfig(String lastMonthDate, String lastYear);

    /**
     * 根据机构时间获取优惠券额度信息.<br>
     * @param orgId 机构id.<br>
     * @param year 年.<br>
     * @param month 月.<br>
     * @return
     */
    MmpCouponConfigure selectByOrgAndTime(@Param("orgId") String orgId, @Param("year") String year, @Param("month") String month);

    /**
     * 扣减指定机构的优惠券月额度.<br>
     * @param amount
     * @param id
     */
    void deductionByPrimaryKey(@Param("amount") BigDecimal amount, @Param("id") Long id);

    // 查询所属机构当年当月剩余额度
    List<Float> getRemainAvailable(@Param("vehicleOrgId") String vehicleOrgId, @Param("currentYear") String currentYear, @Param("currentMonth") String currentMonth);
}