package com.extracme.evcard.mmp.common;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.UUID;

public class CodeUtil {

	static Logger logger = LoggerFactory.getLogger(CodeUtil.class);

	public static String UUID() {
		UUID uuid = UUID.randomUUID();
		return compressedUUID(uuid);
	}

	private static String compressedUUID(UUID uuid) {
		byte[] byUuid = new byte[16];
		long least = uuid.getLeastSignificantBits();
		long most = uuid.getMostSignificantBits();
		long2bytes(most, byUuid, 0);
		long2bytes(least, byUuid, 8);
		String compressUUID = Base64.encodeBase64URLSafeString(byUuid);
		return compressUUID;
	}

	private static void long2bytes(long value, byte[] bytes, int offset) {
		for (int i = 7; i > -1; i--) {
			bytes[offset++] = (byte) ((value >> 8 * i) & 0xFF);
		}
	}

	private static final char[] char_32 = new char[] { 'M', '5', 'Q', '7', 'R',
			'N', 'T', '4', 'W', 'X', 'Y', '8', 'Z', 'a', 'b', '6', 'U', 'V',
			'c', 'd', 'e', 'f', '2', '9', 'P', 'S', '3', 'g', 'h', 'J', 'K',
			'L' };

	public static String getRandomPwd() {
		StringBuffer pwd = new StringBuffer();
		for (int i = 0; i < 8; i++) {
			int ranIndex = RandomUtils.nextInt(0, 32);
			pwd.append(char_32[ranIndex]);
		}
		return pwd.toString();

	}

	
	
	public static void main(String[] args) {
		for(int i=0;i<10;i++){
			System.out.println(UUID());
		}
		
	}
}