package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.model.MmpProvisionGroup;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface MmpProvisionGroupMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    int insert(MmpProvisionGroup record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    int insertSelective(MmpProvisionGroup record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    MmpProvisionGroup selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    int updateByPrimaryKeySelective(MmpProvisionGroup record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    int updateByPrimaryKey(MmpProvisionGroup record);

    List<MmpProvisionGroup> queryGroups(int provisionType);

    int stepBackwardAfter(Long rankId);

    Long getMaxRankId();

    MmpProvisionGroup selectNextToGroup(@Param("id") Long groupId, @Param("rankId")Long rankId);

    MmpProvisionGroup selectPrevGroup(@Param("id") Long groupId, @Param("rankId")Long rankId);

    void updateRankByPrimaryKey(@Param("id") Long groupId, @Param("rankId")Long rankId,
                                @Param("updateTime") Date dateTime, @Param("updateOperId") Long updateOperId,
                                @Param("updateOperName") String updateOperName);

    MmpProvisionGroup selectByGroupName(@Param("id")Long id, @Param("groupName") String groupName);


    MmpProvisionGroup selectOneActiveGroup(@Param("id")Long id);
}