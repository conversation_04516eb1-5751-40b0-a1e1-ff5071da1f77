package com.extracme.evcard.mmp.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

@Configuration
public class MultipartResolverConfig {
    private String encode = "UTF-8";
    private long maxUploadSize = 104857600L;
    private int maxInMemorySize = 1;

    @Bean(value = "multipartResolver")
    public CommonsMultipartResolver initMultipartResolver(){
        CommonsMultipartResolver bean = new CommonsMultipartResolver();
        bean.setDefaultEncoding(encode);
        bean.setMaxUploadSize(maxUploadSize);
        bean.setMaxInMemorySize(maxInMemorySize);
        return bean;
    }
}
