package com.extracme.evcard.mmp.common;

import com.extracme.evcard.mmp.model.ActivitySmsLog;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;

public class BatchExportExecutor {

    private final Workbook workbook;
    private final Sheet sheet;
    // 表头， <字段名， EXCEL名>
    private final Map<String, String> headers;
    int totalRows = 0;

    public BatchExportExecutor(Map<String, String> headers) {
        this.headers = headers;
        workbook = new XSSFWorkbook();
        sheet = workbook.createSheet();
        createHeader();
    }

    // 创建表头
    private void createHeader() {
        Row headerRow = sheet.createRow(totalRows++);
        AtomicInteger i = new AtomicInteger();
        headers.forEach((k, v) -> {
            headerRow.createCell(i.getAndIncrement()).setCellValue(v);
        });
    }

    public String export(List<Map<String, Object>> data, String tempFileDir, String filePrefix) {
        write(data);
        return saveToAliyun(tempFileDir, filePrefix);
    }

    // 写入
    public void write(List<Map<String, Object>> data) {
        for (Map<String, Object> rowData : data) {
            Row row = sheet.createRow(totalRows++);
            int i = 0;
            for (String key : headers.keySet()) {
                Cell cell = row.createCell(i++);
                Object value = rowData.getOrDefault(key, "");
                if (value instanceof String) {
                    cell.setCellValue((String) value);
                } else if (value instanceof Integer) {
                    cell.setCellValue((Integer) value);
                } else if (value instanceof Double) {
                    cell.setCellValue((Double) value);
                } else if (value instanceof Boolean) {
                    cell.setCellValue((Boolean) value);
                } else {
                    cell.setCellValue(value.toString());
                }
            }
        }
    }

    public String saveToAliyun(String tempFileDir, String filePrefix){
        // 生成结果excel文件，并上传阿里云
        ComUtil.checkAndMkDir(tempFileDir);
        String fileName = filePrefix + System.currentTimeMillis() + "_" + new Random().nextInt(10000) + ".xls";
        String filePath = tempFileDir + fileName;

        // excel内容写到本次路径
        FileOutputStream fos = null;
        File resultFile = new File(filePath);
        try {
            fos = new FileOutputStream(resultFile);
            workbook.write(fos);
        } catch (Exception e) {
            //log.error("发送短信的结果文件，写入到本地失败！filePath={}", filePath, e);
            return null;
        } finally {
            if (workbook != null) {
                try {
                    workbook.close();
                } catch (IOException e) {
                    //log.error("close workbook error", e);
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    //log.error("close fos error", e);
                }
            }
        }
        //log.info("发送短信，写本地结果文件完毕");

        // 6.将新的结果表格保存在阿里云
        String resultFileUrl = UploadImgUtil.uploadSynByFilePath(filePath, "/mmpResult/" + fileName);
        if (resultFileUrl == null) {
            //log.error("发送短信文件处理完毕！但是上传发送结果文件到阿里云失败！");
            return null;
        }

        // http替换成https解决前端在https环境下不能下载的问题
        if (resultFileUrl.startsWith("http")) {
            resultFileUrl = resultFileUrl.replaceFirst("http", "https");
        }

        //log.info("发送短信，结果文件上传阿里云完毕");

        // 删除本地临时文件
        FileUtils.deleteQuietly(resultFile);
        //log.info("删除临时文件！filePath[{}]result[{}]", resultFile.getAbsolutePath(), result);

        return resultFileUrl;
    }

}
