package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.model.MmpCouponStatistics;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

import java.util.List;

/**
 * Mapper类，，对应表mmp_coupon_statistics
 */
@OecsMapper
public interface MmpCouponStatisticsMapper extends Dao<MmpCouponStatistics> {
    /**
     * 查询活动优惠券发放统计
     * @param activityId
     * @return
     */
    List<MmpCouponStatistics> selectByActivityId(Long activityId);

    int batchUpdateStatistics(List<MmpCouponStatistics> list);
}