package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.dto.UserOperateLogDTO;
import com.extracme.evcard.mmp.model.MmpProvisionGroupOperateLog;

import java.util.List;

public interface MmpProvisionGroupOperateLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group_operate_log
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group_operate_log
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    int insert(MmpProvisionGroupOperateLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group_operate_log
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    int insertSelective(MmpProvisionGroupOperateLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group_operate_log
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    MmpProvisionGroupOperateLog selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group_operate_log
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    int updateByPrimaryKeySelective(MmpProvisionGroupOperateLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group_operate_log
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    int updateByPrimaryKey(MmpProvisionGroupOperateLog record);

    List<UserOperateLogDTO> queryOperateLogs();
}