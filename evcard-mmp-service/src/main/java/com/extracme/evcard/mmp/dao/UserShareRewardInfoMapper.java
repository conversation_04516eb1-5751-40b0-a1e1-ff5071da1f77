package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.activity.dto.UserShareRewardDTO;
import com.extracme.evcard.mmp.bo.InviteRewardBO;
import com.extracme.evcard.mmp.dto.InviteRewardDTO;
import com.extracme.evcard.mmp.model.UserShareRewardInfo;
import com.extracme.framework.data.dao.OecsMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@OecsMapper
public interface UserShareRewardInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_share_reward_info
     *
     * @mbggenerated Sat Feb 24 11:01:44 CST 2018
     */
    int deleteByPrimaryKey(Long userShareRewardInfoSeq);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_share_reward_info
     *
     * @mbggenerated Sat Feb 24 11:01:44 CST 2018
     */
    int insert(UserShareRewardInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_share_reward_info
     *
     * @mbggenerated Sat Feb 24 11:01:44 CST 2018
     */
    int insertSelective(UserShareRewardInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_share_reward_info
     *
     * @mbggenerated Sat Feb 24 11:01:44 CST 2018
     */
    UserShareRewardInfo selectByPrimaryKey(Long userShareRewardInfoSeq);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_share_reward_info
     *
     * @mbggenerated Sat Feb 24 11:01:44 CST 2018
     */
    int updateByPrimaryKeySelective(UserShareRewardInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table user_share_reward_info
     *
     * @mbggenerated Sat Feb 24 11:01:44 CST 2018
     */
    int updateByPrimaryKey(UserShareRewardInfo record);

    List<InviteRewardDTO> getInviteRewardRecords(InviteRewardBO inviteRewardBO);

    /**
     * 自定义countsql
     * @param inviteRewardBO
     * @return
     */
    Long getInviteRewardRecords_COUNT(InviteRewardBO inviteRewardBO);

	List<InviteRewardDTO> exportInviteRewardRecords(InviteRewardBO bo);

    /**
     * 邀请奖励记录新增
     * @param record
     */
    int shareRewardInsert(UserShareRewardInfo record);

    /**
     * 批量写入
     * @param records
     * @return
     */
    int shareRewardBatchInsert(List<UserShareRewardInfo> records);

    /**
     * 邀请记录更新
     * @param record
     */
    int shareRewardUpdate(UserShareRewardInfo record);

    /**
     * 查询邀请记录
     * @param authId
     * @return
     */
    UserShareRewardInfo getInviteRecord(@Param("authId") String authId,
                                        @Param("orginAuthId") String orginAuthId,
                                        @Param("rewardType") String rewardType);

    List<InviteRewardDTO> getFullInviteRecord(@Param("list") List<String> authIds);

    List<String> getUnsaveInviteRecord(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 获取因自动审核不通过，而未发券的新会员。
     * @param startDate
     * @return
     */
    List<InviteRewardDTO> getUnsaveInviteRewardRecord(@Param("startDate") String startDate);
}