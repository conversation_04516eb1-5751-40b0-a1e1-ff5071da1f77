package com.extracme.evcard.mmp.config;

import com.extracme.evcard.elasticsearch.core.EvcardESTemplate;
import com.extracme.framework.data.service.BaseService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class BeanConfig {
    @Bean(value = "evcardESTemplate")
    public EvcardESTemplate initEsTemplate(){
        return new EvcardESTemplate();
    }

    @Bean(value = "baseService")
    public BaseService initBaseService(){
        return new BaseService();
    }
}
