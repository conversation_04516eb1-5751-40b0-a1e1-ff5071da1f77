package com.extracme.evcard.mmp.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.dto.RechargePackagesCouponModelDTO;
import com.extracme.evcard.mmp.model.MmpRechargePackagesCoupon;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

@OecsMapper
public interface MmpRechargePackagesCouponMapper extends Dao<MmpRechargePackagesCoupon> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_coupon
     *
     * @mbggenerated Thu Apr 19 09:41:02 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_coupon
     *
     * @mbggenerated Thu Apr 19 09:41:02 CST 2018
     */
    int insert(MmpRechargePackagesCoupon record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_coupon
     *
     * @mbggenerated Thu Apr 19 09:41:02 CST 2018
     */
    int insertSelective(MmpRechargePackagesCoupon record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_coupon
     *
     * @mbggenerated Thu Apr 19 09:41:02 CST 2018
     */
    MmpRechargePackagesCoupon selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_coupon
     *
     * @mbggenerated Thu Apr 19 09:41:02 CST 2018
     */
    int updateByPrimaryKeySelective(MmpRechargePackagesCoupon record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_coupon
     *
     * @mbggenerated Thu Apr 19 09:41:02 CST 2018
     */
    int updateByPrimaryKey(MmpRechargePackagesCoupon record);

    /**
     * 根据套餐id查询套餐优惠券模板
     * @param packagesId
     * @return
     */
	List<MmpRechargePackagesCoupon> queryRechargePackagesCouponModelByPackagesId(Long packagesId);

    /**
     * 根据套餐id获取优惠券模板编号couponSeq
     * @param packagesId
     * @return
     */
	List<Long> getCouponSeqByPackagesId(Long packagesId);

    /**
     * 批量删除套餐优惠券模板
     * @param couponSeqList
     * @param packagesId
     * @return
     */
	int batchDelPackagesCoupon(@Param("list") List<Long> couponSeqList, @Param("packagesId") Long packagesId);

    /**
     * 根据套餐id查询运营公司orgName
     * @param packagesId
     * @return
     */
	String queryOrgNameByPackagesId(Long packagesId);

    /**
     * 批量保存套餐优惠券模板
     * @param list
     * @return
     */
	int batchInsertPackagesCoupon(List<MmpRechargePackagesCoupon> list);

	String queryOrgNameByOrgIds(List<String> list);

    /**
     * 删除所有的套餐优惠券模板
     * @param packagesId
     * @return
     */
    int deletePackagesTemplateCoupons(Long packagesId);

}