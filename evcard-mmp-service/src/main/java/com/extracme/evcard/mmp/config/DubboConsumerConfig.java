package com.extracme.evcard.mmp.config;

import com.extracme.evcard.authority.service.IAuthorityService;
import com.extracme.evcard.authority.service.UserSynService;
import com.extracme.evcard.membership.core.service.IBaseInfoService;
import com.extracme.evcard.sso.service.SsoUserService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class DubboConsumerConfig {
    /**
     * 消费资源权限相关的dubbo服务
     * 服务由evcard-sso提供
     */
    @DubboReference
    SsoUserService ssoUserService;
    @Bean
    @Primary
    public SsoUserService ssoUserService() {
        return ssoUserService;
    }

    // 资源权限相关服务
    @DubboReference
    IAuthorityService authorityService;
    @Bean
    @Primary
    public IAuthorityService authorityService() {
        return authorityService;
    }

    // 用户同步服务
    @DubboReference
    UserSynService userSynService;
    @Bean
    @Primary
    public UserSynService userSynService() {
        return userSynService;
    }


    /**
     * 引入commonUserService依赖的membership的rpc服务
     * com.extracme.evcard.membership.core.service.IBaseInfoService
     */
    @DubboReference
    IBaseInfoService baseInfoService;
    @Bean
    @Primary
    public IBaseInfoService baseInfoService() {
        return baseInfoService;
    }
}
