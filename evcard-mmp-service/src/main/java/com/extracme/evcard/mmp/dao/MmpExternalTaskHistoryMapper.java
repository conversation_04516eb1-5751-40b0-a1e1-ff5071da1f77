package com.extracme.evcard.mmp.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.dto.TaskDisposeProcessDTO;
import com.extracme.evcard.mmp.dto.TaskDisposeRecordDTO;
import com.extracme.evcard.mmp.dto.TaskStatisticsDTO;
import com.extracme.evcard.mmp.model.MmpExternalTaskHistory;

public interface MmpExternalTaskHistoryMapper {
    /**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_external_task_history
	 * @mbg.generated  Wed Dec 05 15:44:29 CST 2018
	 */
	int deleteByPrimaryKey(Long id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_external_task_history
	 * @mbg.generated  Wed Dec 05 15:44:29 CST 2018
	 */
	int insert(MmpExternalTaskHistory record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_external_task_history
	 * @mbg.generated  Wed Dec 05 15:44:29 CST 2018
	 */
	int insertSelective(MmpExternalTaskHistory record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_external_task_history
	 * @mbg.generated  Wed Dec 05 15:44:29 CST 2018
	 */
	MmpExternalTaskHistory selectByPrimaryKey(Long id);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_external_task_history
	 * @mbg.generated  Wed Dec 05 15:44:29 CST 2018
	 */
	int updateByPrimaryKeySelective(MmpExternalTaskHistory record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_external_task_history
	 * @mbg.generated  Wed Dec 05 15:44:29 CST 2018
	 */
	int updateByPrimaryKey(MmpExternalTaskHistory record);

	List<TaskDisposeRecordDTO> queryTaskDisposeRecord(String taskSeq);

	List<TaskDisposeProcessDTO> queryTaskDisposeProcess(String taskSeq);

	List<MmpExternalTaskHistory> selectByTaskSeqAndUserId(@Param("taskSeq") String taskSeq, @Param("createOperId") Long createOperId);
}