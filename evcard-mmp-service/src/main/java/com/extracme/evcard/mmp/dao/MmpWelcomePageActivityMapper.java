package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.bo.WelcomeActivityBO;
import com.extracme.evcard.mmp.dto.WelcomeActivityDetailDTO;
import com.extracme.evcard.mmp.dto.WelcomeActivityPageDTO;
import com.extracme.evcard.mmp.model.MmpWelcomePageActivity;
import com.extracme.evcard.mmp.model.WelcomePageInfoDto;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@OecsMapper
public interface MmpWelcomePageActivityMapper extends Dao<MmpWelcomePageActivity> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_welcome_page_activity
     *
     * @mbggenerated Tue Mar 20 14:43:36 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_welcome_page_activity
     *
     * @mbggenerated Tue Mar 20 14:43:36 CST 2018
     */
    int insert(MmpWelcomePageActivity record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_welcome_page_activity
     *
     * @mbggenerated Tue Mar 20 14:43:36 CST 2018
     */
    int insertSelective(MmpWelcomePageActivity record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_welcome_page_activity
     *
     * @mbggenerated Tue Mar 20 14:43:36 CST 2018
     */
    MmpWelcomePageActivity selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_welcome_page_activity
     *
     * @mbggenerated Tue Mar 20 14:43:36 CST 2018
     */
    int updateByPrimaryKeySelective(MmpWelcomePageActivity record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_welcome_page_activity
     *
     * @mbggenerated Tue Mar 20 14:43:36 CST 2018
     */
    int updateByPrimaryKey(MmpWelcomePageActivity record);

    int updateActivityImmediateStartStatus(@Param("id") Long id, @Param("createOperId") Long createOperId,
                                           @Param("createOperName") String createOperName);

    MmpWelcomePageActivity selectNoPublishActivityByActivityId(Long id);

    MmpWelcomePageActivity selectOnGoingActivityByOrgId(String orgId);

    int updateActivitySuspendStatus(@Param("id") Long id,
                                    @Param("createOperId") Long createOperId,
                                    @Param("createOperName") String createOperName);

    int deleteActivity(Long id);

    int updateActivityPauseStatus(@Param("id") Long id,
                                  @Param("createOperId") Long createOperId,
                                  @Param("createOperName") String createOperName);

    int updateActivityResumeStatus(@Param("id") Long id,
                                   @Param("createOperId") Long createOperId,
                                   @Param("createOperName") String createOperName);

    WelcomeActivityDetailDTO selectWelcomeActivityDetailById(Long id);

    List<WelcomeActivityPageDTO> queryWelcomeActivityList(WelcomeActivityBO welcomeActivityBO);

    /**
     * 查询欢迎页活动即将开始的活动
     * @return
     */
    List<MmpWelcomePageActivity> selectWelcomeActivityByStatusStart();

    /**
     * 查询欢迎页活动即将结束的活动
     * @return
     */
    List<MmpWelcomePageActivity> selectWelcomeActivityByStatusCompleted();

    /**
     * 将未开始的欢迎页活动状态改为进行中状态
     * @param list
     */
    void updateActivityStatus(List<Integer> list);

    /**
     * 将进行中和暂停中的欢迎页活动状态改为已停止状态
     * @param list1
     */
    void updateActivityStatusCompleted(List<Integer> list1);

	List<String> queryAllWelcomeActivityName();

	List<Long> queryTimeConflictWelcomeActivityByOrgId(@Param("id") Long id, @Param("orgId") String orgId, @Param("activityStartDate") String activityStartDate,
            @Param("activityEndDate") String activityEndDate);

    /**
     * 根据活动i的查询欢迎页信息
     * @param id
     * @return
     */
    WelcomePageInfoDto getWelcomPageInfoById(@Param("id") Long id);

    /**
     * 查询环球进行的活动
     * @return
     */
    WelcomePageInfoDto getHQActivity();
}