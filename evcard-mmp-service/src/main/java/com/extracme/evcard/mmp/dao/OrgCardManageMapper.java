package com.extracme.evcard.mmp.dao;

import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.model.OrgCardManage;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

/**
 * Mapper类，，对应表org_card_manage
 */
@OecsMapper
public interface OrgCardManageMapper extends Dao<OrgCardManage> {
    void updateOrgCardManaStatus(@Param("updateData") Map updateData);
}