package com.extracme.evcard.mmp.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.dto.MmpUserLevelAnalysisDTO;
import com.extracme.evcard.mmp.dto.MmpUserLevelAnalysisInfoDTO;
import com.extracme.evcard.mmp.model.MmpUserLevelAnalysis;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

/**
 * Mapper类，，对应表mmp_user_level_analysis
 */
@OecsMapper
public interface MmpUserLevelAnalysisMapper extends Dao<MmpUserLevelAnalysis> {

    // 新增会员分析信息
    Integer addAnalysisInfo(MmpUserLevelAnalysisInfoDTO mmpUserLevelAnalysisInfoDTO);

    // 修改会员分析信息
    Integer updateAnalysisInfo(MmpUserLevelAnalysisInfoDTO mmpUserLevelAnalysisInfoDTO);

    // 检验唯一性
    Integer getAnalysisInfoNum(@Param("year") Integer year, @Param("month") Integer month, @Param("orgId") String orgId, @Param("userLevel") Integer userLevel);

    // 查询会员分析信息
    List<MmpUserLevelAnalysisDTO> getAnalysisInfo(@Param("year") Integer year, @Param("month") Integer month, @Param("orgId") String orgId, @Param("type") Integer type,
            @Param("userLevelId") Integer userLevelId);

    // 查询各个等级会员分析信息
    List<Double> getMmpAnalysisInfo(@Param("year") Integer year, @Param("orgId") String orgId, @Param("type") Integer type, @Param("userLevelId") Integer userLevelId);

}