package com.extracme.evcard.mmp.dao;


import com.extracme.evcard.mmp.model.CouponDef;
import com.extracme.evcard.mmp.model.MmpCardDef;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MmpCardDefMapper {

    List<MmpCardDef> queryMmpCardDefs(@Param("cardId") Long cardId,
                                   @Param("limitSet") Integer limitSet);

    void batchUpdateMmpCardDef(@Param("list") List<MmpCardDef> list);
}
