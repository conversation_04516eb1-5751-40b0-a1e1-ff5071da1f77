package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.dto.QuickLinksConfigOperateLogDTO;
import com.extracme.evcard.mmp.model.QuickLinksConfigOperateLog;
import com.extracme.evcard.mmp.model.QuickLinksConfigOperateLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface QuickLinksConfigOperateLogMapper {
    int countByExample(QuickLinksConfigOperateLogExample example);

    int deleteByPrimaryKey(Long id);

    int insert(QuickLinksConfigOperateLog record);

    int insertSelective(QuickLinksConfigOperateLog record);

    List<QuickLinksConfigOperateLog> selectByExample(QuickLinksConfigOperateLogExample example);

    QuickLinksConfigOperateLog selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") QuickLinksConfigOperateLog record, @Param("example") QuickLinksConfigOperateLogExample example);

    int updateByExample(@Param("record") QuickLinksConfigOperateLog record, @Param("example") QuickLinksConfigOperateLogExample example);

    int updateByPrimaryKeySelective(QuickLinksConfigOperateLog record);

    int updateByPrimaryKey(QuickLinksConfigOperateLog record);

    List<QuickLinksConfigOperateLogDTO> selectDetailLogs();
}