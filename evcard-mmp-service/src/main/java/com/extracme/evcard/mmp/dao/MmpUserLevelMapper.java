package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.dto.MmpUserLevelDTO;
import com.extracme.evcard.mmp.model.MmpUserLevel;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

/**
 * Mapper类，，对应表mmp_user_level
 */
@OecsMapper
public interface MmpUserLevelMapper extends Dao<MmpUserLevel> {

    // 新增会员等级信息
    Integer addMmpUserLevelInfo(MmpUserLevelDTO mmpUserLevelDTO);

    // 更新会员等级信息
    Integer updateById(MmpUserLevelDTO mmpUserLevelDTO);

    // 检验唯一性
    Integer queryNumById(String authId);

    // 根据会员id查询会员消费等级
    String queryUserLevel(String authId);

    //根据会员authId查询是否已首单免两小时
    String queryUserFirstTwoHourRightOrder(String authId);
}