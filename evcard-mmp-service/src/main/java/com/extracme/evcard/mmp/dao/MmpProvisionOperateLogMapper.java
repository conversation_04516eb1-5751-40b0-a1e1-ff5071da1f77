package com.extracme.evcard.mmp.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.bo.ProvisionOperateLogBO;
import com.extracme.evcard.mmp.model.MmpProvisionOperateLog;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

/**
 * Mapper类，，对应表mmp_provision_operate_log
 */
@OecsMapper
public interface MmpProvisionOperateLogMapper extends Dao<MmpProvisionOperateLog>{
	// 获取日志记录 
	List<ProvisionOperateLogBO> getLogList(@Param("provisionId") Long provisionId);
}