package com.extracme.evcard.mmp.dao;

import java.util.List;
import java.util.Map;
import java.util.Set;

import com.extracme.evcard.mmp.bo.MmpCouponGroupBO;
import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.dto.BatchImportCouponInfoDTO;
import com.extracme.evcard.mmp.dto.CouponDetailDTO;
import com.extracme.evcard.mmp.model.MmpThirdCoupon;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

@OecsMapper
public interface MmpThirdCouponMapper extends Dao<MmpThirdCoupon> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_third_coupon
     * @mbggenerated Wed Dec 13 14:59:36 CST 2017
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_third_coupon
     * @mbggenerated Wed Dec 13 14:59:36 CST 2017
     */
    int insert(MmpThirdCoupon record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_third_coupon
     * @mbggenerated Wed Dec 13 14:59:36 CST 2017
     */
    int insertSelective(MmpThirdCoupon record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_third_coupon
     * @mbggenerated Wed Dec 13 14:59:36 CST 2017
     */
    MmpThirdCoupon selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_third_coupon
     * @mbggenerated Wed Dec 13 14:59:36 CST 2017
     */
    int updateByPrimaryKeySelective(MmpThirdCoupon record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_third_coupon
     * @mbggenerated Wed Dec 13 14:59:36 CST 2017
     */
    int updateByPrimaryKey(MmpThirdCoupon record);

    int batchDeleteThirdCoupon(@Param("list") List<Long> couponSeqList, @Param("thirdActivityId") Long thirdActivityId);

    List<MmpThirdCoupon> selectAllCouponSeqByThirdId(Long thirdActivityId);

    int deleteByThirdActivityId(Long thirdActivityId);

    //优惠券详情 只用于生成兑换码
    List<CouponDetailDTO> getCouponDetail(@Param("id") Long id);

    List<MmpThirdCoupon> selectAllCouponSeqByThirdIds(List<Long> thirdActivityIds);

    List<MmpThirdCoupon> selectAllCouponSeqByThirdIdAndOrgId(@Param("thirdActivityId") Long thirdActivityId,
                                                             @Param("orgId") String orgId,
                                                             @Param("couponTarget") Integer couponTarget);

    Map<String,String> selectSweepActivitySubOrgIdByThirdActivityId(Long thirdActivityId);

    int batchSaveThirdFullCoupons(List<MmpThirdCoupon> thirdCoupons);

    int batchDeleteThirdCouponById(List<Long> thirdCouponIdList);

	Set<String> getCouponIdByThirdId(Long thirdActivityId);

	List<BatchImportCouponInfoDTO> getCouponInfoById(@Param("list") Set<String> couponIdSet);

    List<MmpCouponGroupBO> selectGroupByKey(@Param("activityId") Long activityId, @Param("couponTarget") Integer couponTarget);

    List<MmpCouponGroupBO> selectGroupByKeyNoOrg(Long activityId);

    Integer countNewActivity(Long thirdActivityId);
}