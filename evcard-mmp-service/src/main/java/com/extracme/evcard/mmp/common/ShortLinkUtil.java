package com.extracme.evcard.mmp.common;

import com.extracme.evcard.mmp.dao.MmpShortLinkManagementMapper;
import com.extracme.evcard.mmp.model.MmpShortLinkManagement;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.List;

/**
 * 短链生成工具类
 * 
 * <AUTHOR>
 *
 */
@Slf4j
public class ShortLinkUtil {
	/**
	 * 生成活动url的公用方法
	 * @param activitySeq
	 * @return
	 */
	public static String createActivityUrl(Long activitySeq,String appSecret,String activityChannelKey,Integer channelLevel){
		String activityUrl = StringUtils.EMPTY;
		try {
			String sn = MD5Util.md5Encode(appSecret);
			activityUrl = "?activityNo=" + activitySeq + "&secretKey=" + sn + "&channelKey=" + activityChannelKey + "&channelLevel=" + channelLevel;
			return activityUrl;
		} catch (Exception e1) {
			log.error("MD5加密失败=activityId:"+activitySeq ,e1);
			return activityUrl;
		}
	}
	/**
	 * 生成6位随机Id
	 * @param length 生成字符串的长度
	 * @return
	 */
	public static String createRandomStr(int length) {
		if (length < 1) {
			return "";
		}
		String allChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
		Random random = new Random();
		StringBuilder sb = new StringBuilder(length);

		for (int i = 0; i < length; i++) {
			// 随机生成大小写字母或数字
			int randomChar = random.nextInt(allChars.length());
			sb.append((char)allChars.charAt(randomChar));
		}
		String returnStr = sb.toString();
		if (checkRandomStr(returnStr)){
			return returnStr;
		}else {
			return createRandomStr(length);
		}
	}

	/**
	 * 校验随机Id
	 * @param randomStr
	 * @return	TRUE - 符合规则，可以生成，FALSE - 不符合规则，不能生成
	 */
	public static Boolean checkRandomStr(String randomStr) {
		if (StringUtils.isBlank(randomStr)) {
			return Boolean.TRUE;
		}
		//查询数据库是否存在，如果存在，则返回false，否则返回true
		MmpShortLinkManagementMapper mmpShortLinkManagementMapper = SpringContextUtil.getBean(MmpShortLinkManagementMapper.class);
		List<MmpShortLinkManagement> mmpShortLinkManagements = mmpShortLinkManagementMapper.selectByKeys(null,null,null,randomStr, null,null);
		if (CollectionUtils.isNotEmpty(mmpShortLinkManagements)){
			//代表已经存在，重新生成
			return Boolean.FALSE;
		}

		return Boolean.TRUE;
	}

	public static void main(String[] args) {
		Set<String> sets = new HashSet<>();
		for (int i = 0; i < 10000000; i++) {
			String randomStr = createRandomStr(6);
			if (sets.contains(randomStr)){
				System.out.println("重复数据："+randomStr);
			}else {
				sets.add(randomStr);
				//System.out.println(randomStr);
			}
		}
	}
}
