package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.model.MmpOrderShareActivity;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

@OecsMapper
public interface MmpOrderShareActivityMapper extends Dao<MmpOrderShareActivity> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_order_share_activity
     *
     * @mbggenerated Mon Jul 09 17:13:43 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_order_share_activity
     *
     * @mbggenerated Mon Jul 09 17:13:43 CST 2018
     */
    int insert(MmpOrderShareActivity record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_order_share_activity
     *
     * @mbggenerated Mon Jul 09 17:13:43 CST 2018
     */
    int insertSelective(MmpOrderShareActivity record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_order_share_activity
     *
     * @mbggenerated Mon Jul 09 17:13:43 CST 2018
     */
    MmpOrderShareActivity selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_order_share_activity
     *
     * @mbggenerated Mon Jul 09 17:13:43 CST 2018
     */
    int updateByPrimaryKeySelective(MmpOrderShareActivity record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_order_share_activity
     *
     * @mbggenerated Mon Jul 09 17:13:43 CST 2018
     */
    int updateByPrimaryKey(MmpOrderShareActivity record);
}