package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.model.MmpOrderShareCoupon;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@OecsMapper
public interface MmpOrderShareCouponMapper extends Dao<MmpOrderShareCoupon> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_order_share_coupon
     *
     * @mbggenerated Mon Jul 09 17:16:43 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_order_share_coupon
     *
     * @mbggenerated Mon Jul 09 17:16:43 CST 2018
     */
    int insert(MmpOrderShareCoupon record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_order_share_coupon
     *
     * @mbggenerated Mon Jul 09 17:16:43 CST 2018
     */
    int insertSelective(MmpOrderShareCoupon record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_order_share_coupon
     *
     * @mbggenerated Mon Jul 09 17:16:43 CST 2018
     */
    MmpOrderShareCoupon selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_order_share_coupon
     *
     * @mbggenerated Mon Jul 09 17:16:43 CST 2018
     */
    int updateByPrimaryKeySelective(MmpOrderShareCoupon record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_order_share_coupon
     *
     * @mbggenerated Mon Jul 09 17:16:43 CST 2018
     */
    int updateByPrimaryKey(MmpOrderShareCoupon record);

    int batchSaveShareCoupons(List<MmpOrderShareCoupon> orderShareCoupons);

    List<MmpOrderShareCoupon> selectAllCouponSeqByShareActivityId(Long shareActivityId);

    int batchDeleteShareCouponById(List<Long> shareCouponIdList);

    List<MmpOrderShareCoupon> selectGiftBagCouponSeqByShareActivityId(@Param("shareActivityId") Long shareActivityId,
                                                                      @Param("giftBagType") Integer giftBagType);

    int deleteByOrderShareActivityId(Long shareActivityId);
}