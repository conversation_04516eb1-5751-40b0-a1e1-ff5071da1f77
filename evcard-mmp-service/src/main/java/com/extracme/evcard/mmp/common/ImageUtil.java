package com.extracme.evcard.mmp.common;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;

public class ImageUtil {

    /**
     * 给图片添加水印
     * @param originImgPath 原始图片的路径
     * @param targetImgPath 添加水印后图片的保存路径
     * @param markImgPath 水印的路径
     * @param mode 内部枚举类，用于指定水印铺设的样式，平铺，拉伸等
     * @param margin_x 水印之间的水平间距
     * @param margin_y 水印之间的垂直间距
     * @param opacity 水印透明度
     * @param markAngle 水印旋转角度，应在正负45度之间
     * @throws IOException
     */
    public static void markImage(String originImgPath,String targetImgPath,String markImgPath,int mode,int margin_x,int margin_y,float opacity,double markAngle) throws IOException {
        if(markAngle>45||markAngle<-45){
            throw new RuntimeException("旋转角度必须在正负45度之间。");
        }
        BufferedImage originImg= ImageIO.read(new File(originImgPath));
        BufferedImage markImage = ImageIO.read(new File(markImgPath));
        Graphics2D graphics = (Graphics2D) originImg.getGraphics();
        graphics.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_ATOP, opacity));
        graphics.rotate(markAngle);

        if(mode == PAINT_MODE.TILED.mode){
            int canvasHeight = originImg.getHeight();
            int canvasWidth = originImg.getWidth();
            int markHeight = markImage.getHeight();
            int markWidth = markImage.getHeight();
            int interval = markWidth+markHeight;
            for(int i=-canvasHeight;i<canvasWidth+canvasHeight;i=i+interval+margin_x){
                for(int j=-canvasWidth;j<canvasHeight+canvasWidth;j=j+interval+margin_y){
                    graphics.drawImage(markImage,i,j,markImage.getWidth(),markImage.getHeight(),null);
                }
            }
        }
        graphics.dispose();
        ImageIO.write(originImg,"png",new File(targetImgPath));
    }

    enum PAINT_MODE{
        REGULAR(0),//常规
        TILED(1),//平铺
        STRETCHED(2);//拉伸
        private int mode;
        PAINT_MODE(int mode){
            this.mode = mode;
        }
    }

    /**
     * 缩小图片
     * @param multiple 缩小倍数 1表示原大小
     * @param sourcePath  缩小图片路径
     * @param targetImgPath 图片输出路径
     * @throws IOException
     */
    public static void zoomByScale(double multiple, String sourcePath, String targetImgPath) throws IOException {
        //得到最后一个.的位置
        int index = sourcePath.lastIndexOf(".");
        //获取被缩放的图片的格式
        String ext = sourcePath.substring(index + 1);
        //获取目标路径(和原始图片路径相同,在文件名后添加了一个_s)
        String destFile = targetImgPath + "_s." + ext;
        //读取图片,返回一个BufferedImage对象
        BufferedImage img = ImageIO.read(new File(sourcePath));
        //获取图片的长和宽
        int width = img.getWidth();
        int height = img.getHeight();
        //获取缩放后的长和宽
        int _width = (int) (multiple * width);
        int _height = (int) (multiple * height);
        //获取缩放后的Image对象
        Image _img = img.getScaledInstance(_width, _height, Image.SCALE_DEFAULT);
        //新建一个和Image对象相同大小的画布
        BufferedImage image = new BufferedImage(_width, _height, BufferedImage.TYPE_INT_RGB);
        //获取画笔
        Graphics2D graphics = image.createGraphics();
        //将Image对象画在画布上,最后一个参数,ImageObserver:接收有关 Image 信息通知的异步更新接口,没用到直接传空
        graphics.drawImage(_img, 0, 0, null);
        //释放资源
        graphics.dispose();
        //使用ImageIO的方法进行输出,记得关闭资源
        OutputStream out = new FileOutputStream(destFile);
        ImageIO.write(image, ext, out);
        out.close();
    }

}
