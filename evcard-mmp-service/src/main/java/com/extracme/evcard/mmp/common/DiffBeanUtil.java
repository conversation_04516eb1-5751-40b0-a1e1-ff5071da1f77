package com.extracme.evcard.mmp.common;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.extracme.framework.core.util.BeanCopyUtils;

/**
 *项目名称：evcard-mtc-service
 *类名称：DiffBeanUtil
 *类描述：TODO
 *创建：xujian-徐建
 *创建时间：2017年12月7日下午2:16:30
 *修改备注
 *@version 1.0
 *
 */
public class DiffBeanUtil {

    private static final Logger log = LoggerFactory.getLogger(DiffBeanUtil.class);

    public static final String NULL_VALUE = "空值";
    public static final String BR = "<br/>";

    /**
     * 差分2个POJO相同属性的值，取得差分信息<br/>
     * 目前只支持比较String类型的属性(VO和DTO中所以属性都为String类型)
     * @param originalBean 修改之前的数据DTO（或者VO）
     * @param modifiedBean 修改之后的数据DTO
     * @param descriptionMap 页面上显示字段的Map
     * @return
     * @throws IntrospectionException
     * @throws InstantiationException
     * @throws IllegalAccessException
     * @throws IllegalArgumentException
     * @throws InvocationTargetException
     */
    public static String diff(Object originalBean, Object modifiedBean, Map<String, String> descriptionMap) {
    	String result = StringUtils.EMPTY;
    	
    	try {
            Class<? extends Object> modifiedBeanClass = modifiedBean.getClass();

            Object originalCopyBean = modifiedBeanClass.newInstance();
            BeanCopyUtils.copyProperties(originalBean, originalCopyBean);

            Class<? extends Object> originalCopyBeanClass = originalCopyBean.getClass();

            StringBuffer resultBuffer = new StringBuffer();

            Field[] modifiedFields = modifiedBeanClass.getDeclaredFields();
            Field[] originalFields = originalCopyBeanClass.getDeclaredFields();

            // 遍历Bean中的每个属性
            for (int i = 0; i < modifiedFields.length; i++) {
                Field originalField = originalFields[i];
                Field modifiedField = modifiedFields[i];

                PropertyDescriptor originalPd = new PropertyDescriptor(originalField.getName(), originalCopyBeanClass);
                Method originalMethod = originalPd.getReadMethod();
                Object originalFiledValue = originalMethod.invoke(originalCopyBean);

                PropertyDescriptor modifiedPd = new PropertyDescriptor(modifiedField.getName(), modifiedBeanClass);
                Method modifiedMethod = modifiedPd.getReadMethod();
                Object modifiedFiledValue = modifiedMethod.invoke(modifiedBean);

                // String类型的做比较
                if (modifiedFiledValue instanceof java.lang.String) {
                    if (StringUtils.isNotBlank(descriptionMap.get(modifiedField.getName()))) {
                        if (!StringUtils.equals((String) modifiedFiledValue, (String) originalFiledValue)) {
                            if (StringUtils.isNotBlank(modifiedField.getName())) {
                                resultBuffer.append(descriptionMap.get(modifiedField.getName())).append("由")
                                        .append(StringUtils.isBlank((String) originalFiledValue) ? NULL_VALUE : (String) originalFiledValue).append("修改为")
                                        .append(StringUtils.isBlank((String) modifiedFiledValue) ? NULL_VALUE : (String) modifiedFiledValue).append(BR);

                            }

                        }
                    }

                    result = StringUtils.removeEnd(resultBuffer.toString(), BR);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return result;
    }
}
