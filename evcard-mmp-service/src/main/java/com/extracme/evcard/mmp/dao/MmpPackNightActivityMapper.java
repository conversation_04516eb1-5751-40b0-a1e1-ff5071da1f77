package com.extracme.evcard.mmp.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.extracme.evcard.mmp.dto.*;
import com.extracme.evcard.mmp.dto.activity.OrderRewardActivityFullDTO;
import com.extracme.evcard.mmp.model.ActivityFullDetail;
import com.extracme.evcard.mmp.dto.activity.BrandActivityDetailDTO;
import com.extracme.evcard.mmp.dto.activity.InviteActivityDetailDTO;
import com.extracme.evcard.mmp.dto.activity.PasswordRedEnvelopeDetailDTO;
import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.model.MmpPackNightActivity;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

/**
 * Mapper类，，对应表mmp_pack_night_activity
 */
@OecsMapper
public interface MmpPackNightActivityMapper extends Dao<MmpPackNightActivity> {
    /**
     * @param name 活动名
     * @param ids id
     * @param activityStatus 状态
     * @param orgId orgId
     * @param createdStartTime 开始日期
     * @param createdEndTime 结束日期
     * @return vo vo
     */
    List<NightCarInfoDTO> queryNightCarInfoList(@Param("id") String id,@Param("name") String name, @Param("ids") String ids, @Param("activityStatus") Integer activityStatus,
            @Param("orgId") String orgId, @Param("createdStartTime") Date createdStartTime, @Param("createdEndTime") Date createdEndTime, @Param("type") Integer type,
            @Param("createOperName") String createOperName);

    /**
     * @param date date
     * @param id id
     * @param remark
     * @return vo vo
     */
    int updateById(@Param("date") Date date, @Param("id") Long id, @Param("remark") String remark);

    /**
     * @param id id
     * @return vo vo
     */
    int deleteNightCarInfo(@Param("id") Long id);

    /**
     * @param id id
     * @return vo vo
     */
    NightCarActivityDTO queryNightCar(@Param("id") Long id);

    /**
     * @param status status
     * @return vo vo
     */
    List<Integer> selectByStatusStart(int status);

    /**
     * @param status1 status1
     * @return vo vo
     */
    List<Integer> selectByStatusCompleted(int status1);

    /**
     * @param list list
     */
    void updateActivityStatus(List<Integer> list);

    /**
     * @param list1 list1
     */
    void updateActivityStatusCompleted(List<Integer> list1);

    /**
     * @param orgId orgId
     * @return vo vo
     */
    List<NightCarInfoDTO> queryActivityDate(@Param("orgId") String orgId);

    List<ActivityNameDTO> queryNightCarActivityList();

    List<NightCarActivityDTO> queryLicensePlate(@Param("date") Date date, @Param("date1") Date date1, @Param("orgId") String orgId);

    List<NightCarActivityDTO> queryLicensePlate1(@Param("date") Date date, @Param("date1") Date date1, @Param("id") Long id, @Param("orgId") String orgId);

    List<NightCarInfoDTO> queryActivityDate1(@Param("orgId") String orgId, @Param("id") Long id);

    List<VehicleModel> queryVehicleModel();

    ThirdActivityDetailDTO selectThirdActivityById(Long id);

    EActivityDetailDTO selectEActivityById(Long id);

    int updateActivitySuspendStatus(@Param("id") Long id, @Param("createOperId") Long createOperId, @Param("createOperName") String createOperName);

    int updateActivityResumeStatus(@Param("id") Long id, @Param("createOperId") Long createOperId, @Param("createOperName") String createOperName);

    int updateActivityPauseStatus(@Param("id") Long id, @Param("createOperId") Long createOperId, @Param("createOperName") String createOperName);

    List<Integer> selectThirdActivityByStatusStart();

    List<Integer> selectThirdActivityByStatusCompleted();

    int updateActivityImmediateStartStatus(@Param("id") Long id, @Param("createOperId") Long createOperId, @Param("createOperName") String createOperName);

    int updateActivityPublishStatus(@Param("id") Long id, @Param("createOperId") Long createOperId, @Param("createOperName") String createOperName);

    int updateActivityByIdSelective(MmpPackNightActivity mmpPackNightActivity);

    MmpPackNightActivity selectEActivityByOrgId(String orgId);

    List<Integer> selectEActivityByStatusStart();

    List<Integer> selectEActivityByStatusCompleted();

    int updateSignActivityIdById(@Param("id") Long id, @Param("signActivityId") String signActivityId);

    Integer selectActivityOrgIdByChannelKey(@Param("activityChannelKey") String activityChannelKey, @Param("orgId") String orgId, @Param("id") Long id);

    ChannelRewardActivityDetailDTO selectChannelRewardActivityById(Long id);

    List<MmpPackNightActivity> selectActivitiesByThirdIds(List<Long> thirdActivityIds);

    List<MmpPackNightActivity> selectChannelRewardActivityByStatusStart();

    List<MmpPackNightActivity> selectChannelRewardActivityByStatusCompleted();

    // 优惠券批量导入活动详情
    CouponBatchImportActivityDetailDTO getCouponBatchImportActivityDetail(@Param("id") Long id);

    // 活动名称唯一性check
    Integer getActivityNameNum(@Param("activityName") String activityName);

    // 优惠券发放记录一览活动名称下拉框
    List<String> queryActivityNameList();

    // 根据活动编号查询活动名称、类型、所属单位
    ActivityInfoDTO queryActivityInfoById(@Param("actionId") String actionId);

    // 根据活动名称、类型、所属单位查询活动编号
    List<String> getActivityId(@Param("activityName")String activityName, @Param("type")Integer type, @Param("orgId")String orgId);

    // 根据活动名称、类型、所属单位查询活动编号和活动名称
    List<ActivityIdAndNameDTO> getActivityIdAndName(@Param("activityName")String activityName, @Param("type")Integer type, @Param("orgId")String orgId);

    /**
     * 查询邀请好友活动
     * @param id
     * @return
     */
    InviteActivityDetailDTO selectInviteActivityById(Long id);

    /**
     * 根据活动id查询活动配置id
     * @param activityIds 活动id
     * @return
     */
    List<InviteActivityDetailDTO> getInviteThirdActivityIdsByActionIds(String[] activityIds);

    /**
     * 根据orgId 和 活动类型 type 找到活动
     * @param orgId 运营公司
     * @param type 活动类型
     * @return
     */
    MmpPackNightActivity selectOnGoingActivityByOrgIdAndType(@Param("orgId") String orgId,
                                                             @Param("type") Integer type);

    MmpPackNightActivity selectOnGoingActivityByOrgIdsAndType(@Param("orgId") String orgId,
                                                             @Param("type") Integer type);
    MmpPackNightActivity getById(@Param("id") Long id);

    /**
     * 根据id查询扫码发券活动详情
     * @param id
     * @return
     */
    SweepActivityDetailDTO selectSweepActivityById(Long id);

    // 生成券码详情
    CouponBatchImportActivityDetailDTO getCreateRedeemCodeActivityDetail(@Param("id") Long id);

    /**
     * 查询扫码发券即将开始的活动
     * @return
     */
    List<Integer> selectSweepActivityByStatusStart();

    /**
     * 查询扫码发券即将停止的活动
     * @return
     */
    List<Integer> selectSweepActivityByStatusCompleted();

    /**
     * 查询品牌活动详情
     * @param id
     * @return
     */
    BrandActivityDetailDTO selectBrandActivityById(Long id);

    /**
     * 查询品牌活动发券即将开始的活动
     * @return
     */
    List<Integer> selectBrandActivityByStatusStart();

    /**
     * 查询品牌活动发券即将停止的活动
     * @return
     */
    List<Integer> selectBrandActivityByStatusCompleted();

    /**
     * 查询订单活动详情信息
     * @param id
     * @return
     */
    OrderShareActivityDetailDTO selectOrderShareActivityById(Long id);

    /**
     * 查询订单分享即将开始的活动
     * @return
     */
    List<Integer> selectOrderShareActivityByStatusStart();


    /**
     * 查询订单分享发券即将停止的活动
     * @return
     */
    List<Integer> selectOrderShareActivityByStatusCompleted();

	List<NightCarInfoDTO> getActivityInfoList(@Param("name") String name, @Param("ids") Integer ids, @Param("activityStatus") Integer status, @Param("orgId") String orgId, @Param("type") Integer type);

	List<ActivityNameDTO> getActivityNameList();

	List<ActivityInfoDTO> queryActivityInfoList(@Param("list") Set<String> activityIdList);

    /**
     * 查询同一公司已有活动的活动时间与本次添加的活动时间作比较，找出存在冲突的活动
     * @param id 修改的时候传入修改的活动id
     * @param orgId 所属公司
     * @param activityStartDate 活动开始时间
     * @param activityEndDate 活动结束时间
     * @return
     */
    List<Long> queryActivityTimeConflict(@Param("id") Long id,
                                                     @Param("orgId") String orgId, @Param("type") Integer type,
                                                     @Param("activityStartDate") String activityStartDate,
                                                     @Param("activityEndDate") String activityEndDate);


    List<Map<String, Object>> queryActivityTimeConflictByType(@Param("id") Long id,
                                                            @Param("type") Integer type,
                                                            @Param("activityStartDate") String activityStartDate,
                                                            @Param("activityEndDate") String activityEndDate);

    /**
     * 查询一开始的活动id(活动日期涵盖昨日, 当日需统计发券情况)
     * @return
     */
    List<String> selectStartedActivityId();

    /**
     * 查询进行中、已暂停和今天停止的活动
     * @return
     */
    List<String> selectUnderwayActivityId();

    /**
     * 查询口令是否存在相应的活动
     * @param id 修改的时候传入修改的活动id
     * @param password 所属公司
     * @param activityStartDate 活动开始时间
     * @param activityEndDate 活动结束时间
     * @return
     */
    List<Long> queryActivityTimeConflictByPassword(@Param("id") Long id,
                                         @Param("password") String password,
                                         @Param("activityStartDate") String activityStartDate,
                                         @Param("activityEndDate") String activityEndDate);

    /**
     * 查询口令是否存在相应的活动-
     * @param id 修改的时候传入修改的活动id
     * @param password 所属公司
     * @param startDate 活动开始时间
     * @param endDate 活动结束时间
     * @return
     */
    List<PasswordRedEnvelopeDetailDTO> queryActivityByPassword(@Param("id") Long id,
                                                               @Param("password") String password,
                                                               @Param("startDate") String startDate,
                                                               @Param("endDate") String endDate);

    /**
     * 查询口令红包活动详情
     * @param id
     * @return
     */
    ActivityFullDetail getPasswordRedEnvelopeById(Long id);


    List<OrderRewardActivityFullDTO> queryOrderRewardActivityConflict(@Param("id") Long id,
                                                                      @Param("orgId") String password,
                                                                      @Param("activityStartDate") String activityStartDate,
                                                                      @Param("activityEndDate") String activityEndDate);

    /**
     * 查询同一公司已有活动的活动时间与本次添加的活动时间作比较，找出存在冲突的活动
     * @param id 修改的时候传入修改的活动id
     * @param orgId 所属公司
     * @param activityStartDate 活动开始时间
     * @param activityEndDate 活动结束时间
     * @return
     */
    List<Long> queryActivityTimeConflictWithGroup(@Param("id") Long id,
                                         @Param("orgId") String orgId, @Param("type") Integer type,
                                         @Param("groupId") String groupId,
                                         @Param("activityStartDate") String activityStartDate,
                                         @Param("activityEndDate") String activityEndDate);

    /**
     * 查询同一公司已有活动的活动时间与本次添加的活动时间作比较，找出存在冲突的活动
     * @param id 修改的时候传入修改的活动id
     * @param orgId 所属公司
     * @param activityStartDate 活动开始时间
     * @param activityEndDate 活动结束时间
     * @return
     */
    List<Long> queryActivityTimeConflictOfOrgIdsWithGroup(@Param("id") Long id,
                                                  @Param("orgId") String orgId, @Param("type") Integer type,
                                                  @Param("groupId") String groupId,
                                                  @Param("activityStartDate") String activityStartDate,
                                                  @Param("activityEndDate") String activityEndDate);

    List<Long> queryActivityTimeConflictOfOrgIds(@Param("id") Long id,
                                                          @Param("orgId") String orgId, @Param("type") Integer type,
                                                          @Param("activityStartDate") String activityStartDate,
                                                          @Param("activityEndDate") String activityEndDate);
}
