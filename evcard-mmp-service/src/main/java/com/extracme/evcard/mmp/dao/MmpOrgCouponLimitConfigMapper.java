package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.dto.MmpOrgCouponLimitConfigDTO;
import com.extracme.evcard.mmp.dto.OrgInfoDTO;
import com.extracme.evcard.mmp.model.MmpOrgCouponLimitConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface MmpOrgCouponLimitConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_org_coupon_limit_config
     *
     * @mbggenerated Mon Apr 01 15:32:21 CST 2019
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_org_coupon_limit_config
     *
     * @mbggenerated Mon Apr 01 15:32:21 CST 2019
     */
    int insert(MmpOrgCouponLimitConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_org_coupon_limit_config
     *
     * @mbggenerated Mon Apr 01 15:32:21 CST 2019
     */
    int insertSelective(MmpOrgCouponLimitConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_org_coupon_limit_config
     *
     * @mbggenerated Mon Apr 01 15:32:21 CST 2019
     */
    MmpOrgCouponLimitConfig selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_org_coupon_limit_config
     *
     * @mbggenerated Mon Apr 01 15:32:21 CST 2019
     */
    int updateByPrimaryKeySelective(MmpOrgCouponLimitConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_org_coupon_limit_config
     *
     * @mbggenerated Mon Apr 01 15:32:21 CST 2019
     */
    int updateByPrimaryKey(MmpOrgCouponLimitConfig record);

    List<MmpOrgCouponLimitConfigDTO> selectConfigByOrgList(List<OrgInfoDTO> orgList);

    List<MmpOrgCouponLimitConfigDTO> selectConfigByOrgIds(List<String> orgList);

    List<MmpOrgCouponLimitConfigDTO> selectOrgConfig(@Param("orgId")String orgId, @Param("activityKey")String activityKey);

    int batchInsert(@Param("list") List<MmpOrgCouponLimitConfig> activityconfig);

    int batchDelete(@Param("list") List<Map<String,String>> orgActivityKeys);

    int batchDeleteByOrg(@Param("orgId")String orgId, @Param("list") List<String> activityKeys);

    int batchUpdate(@Param("list") List<MmpOrgCouponLimitConfig> activityconfig);
}