package com.extracme.evcard.mmp.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.dto.ProvinceDTO;
import com.extracme.evcard.mmp.model.Province;

public interface ProvinceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table province
     * @mbggenerated Tue Feb 28 09:48:54 CST 2017
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table province
     * @mbggenerated Tue Feb 28 09:48:54 CST 2017
     */
    int insert(Province record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table province
     * @mbggenerated Tue Feb 28 09:48:54 CST 2017
     */
    int insertSelective(Province record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table province
     * @mbggenerated Tue Feb 28 09:48:54 CST 2017
     */
    Province selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table province
     * @mbggenerated Tue Feb 28 09:48:54 CST 2017
     */
    int updateByPrimaryKeySelective(Province record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table province
     * @mbggenerated Tue Feb 28 09:48:54 CST 2017
     */
    int updateByPrimaryKey(Province record);

    /**
     * 查询所有的省份
     * @return
     * <AUTHOR>
     */
    List<Province> selectAll();

    /**
     * 根据id查询省份
     * @param id
     * @return
     * <AUTHOR>
     */
    Province selectByProvinceId(@Param("provinceId") Long provinceId);

    /**
     * 省份下拉框一览
     * @return
     * <AUTHOR>
     */
    List<ProvinceDTO> getAll();
}