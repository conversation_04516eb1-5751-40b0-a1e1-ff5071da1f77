package com.extracme.evcard.mmp.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.dto.PlatformOperateLogDTO;
import com.extracme.evcard.mmp.model.MmpPlatformOperateLog;

public interface MmpPlatformOperateLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_operate_log
     *
     * @mbg.generated Tue Jan 22 15:41:01 CST 2019
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_operate_log
     *
     * @mbg.generated Tue Jan 22 15:41:01 CST 2019
     */
    int insert(MmpPlatformOperateLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_operate_log
     *
     * @mbg.generated Tue Jan 22 15:41:01 CST 2019
     */
    int insertSelective(MmpPlatformOperateLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_operate_log
     *
     * @mbg.generated Tue Jan 22 15:41:01 CST 2019
     */
    MmpPlatformOperateLog selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_operate_log
     *
     * @mbg.generated Tue Jan 22 15:41:01 CST 2019
     */
    int updateByPrimaryKeySelective(MmpPlatformOperateLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_platform_operate_log
     *
     * @mbg.generated Tue Jan 22 15:41:01 CST 2019
     */
    int updateByPrimaryKey(MmpPlatformOperateLog record);

	List<PlatformOperateLogDTO> getPlatformOperateLog(@Param("platformId") Long platformId);
}