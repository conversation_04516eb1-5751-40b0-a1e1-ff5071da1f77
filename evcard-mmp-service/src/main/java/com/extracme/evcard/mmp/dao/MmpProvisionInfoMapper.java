package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.bo.ProvisionBO;
import com.extracme.evcard.mmp.dto.HiProvisionDTO;
import com.extracme.evcard.mmp.dto.UserContractViewDto;
import com.extracme.evcard.mmp.model.MmpProvisionInfo;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper类，，对应表mmp_provision_info
 */
@OecsMapper
public interface MmpProvisionInfoMapper extends Dao<MmpProvisionInfo> {

    /**
     * 查询条款列表
     * @param dto
     * @return
     */
    List<ProvisionBO> selProvisionList(@Param("bo") ProvisionBO bo);

    /**
     * 查询版本是否存在
     * @param version 版本号
     * @param provisionType 条款类型（1：会员守则 2：隐私政策 3：Q&A）
     * @return
     */
    MmpProvisionInfo selByVersionAndType(@Param("version") String version, @Param("provisionType") Integer provisionType);

    /**
     * 获取当前使用中的版本信息
     * @param provisionType 条款类型 1:会员守则 2：隐私条款 3：常见问题
     * @return
     */
    MmpProvisionInfo selCurProvision(@Param("provisionType") Integer provisionType);

    /**
     * 根据用户id查找会员条款
     * @param authId
     * @return
     */
    List<HiProvisionDTO> selectProvisionsByAuthId(String authId);

    UserContractViewDto selectUserContractInfo(@Param("authId") String authId,
                                               @Param("templateId") String templateId);
}