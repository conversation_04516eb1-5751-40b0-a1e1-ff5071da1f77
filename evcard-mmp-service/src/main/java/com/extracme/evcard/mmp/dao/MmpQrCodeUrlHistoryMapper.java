package com.extracme.evcard.mmp.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.dto.MmpQrCodeUrlHistoryDTO;
import com.extracme.evcard.mmp.model.MmpQrCodeUrlHistory;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

@OecsMapper
public interface MmpQrCodeUrlHistoryMapper extends Dao<MmpQrCodeUrlHistory> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_qr_code_url_history
     *
     * @mbggenerated Tue Aug 14 14:54:18 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_qr_code_url_history
     *
     * @mbggenerated Tue Aug 14 14:54:18 CST 2018
     */
    int insert(MmpQrCodeUrlHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_qr_code_url_history
     *
     * @mbggenerated Tue Aug 14 14:54:18 CST 2018
     */
    int insertSelective(MmpQrCodeUrlHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_qr_code_url_history
     *
     * @mbggenerated Tue Aug 14 14:54:18 CST 2018
     */
    MmpQrCodeUrlHistory selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_qr_code_url_history
     *
     * @mbggenerated Tue Aug 14 14:54:18 CST 2018
     */
    int updateByPrimaryKeySelective(MmpQrCodeUrlHistory record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_qr_code_url_history
     *
     * @mbggenerated Tue Aug 14 14:54:18 CST 2018
     */
    int updateByPrimaryKey(MmpQrCodeUrlHistory record);

	List<MmpQrCodeUrlHistoryDTO> queryQrCodeUrlHistory(@Param("qrId") Long qrId);
}