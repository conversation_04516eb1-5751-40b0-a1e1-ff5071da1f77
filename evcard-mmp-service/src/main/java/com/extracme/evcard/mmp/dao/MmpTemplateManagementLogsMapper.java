package com.extracme.evcard.mmp.dao;

import java.util.List;

import com.extracme.evcard.mmp.dto.TemplateManagementLogDTO;
import com.extracme.evcard.mmp.model.MmpTemplateManagementLogs;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

@OecsMapper
public interface MmpTemplateManagementLogsMapper extends Dao<MmpTemplateManagementLogs> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_template_management_logs
     *
     * @mbggenerated Wed May 16 13:48:21 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_template_management_logs
     *
     * @mbggenerated Wed May 16 13:48:21 CST 2018
     */
    int insert(MmpTemplateManagementLogs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_template_management_logs
     *
     * @mbggenerated Wed May 16 13:48:21 CST 2018
     */
    int insertSelective(MmpTemplateManagementLogs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_template_management_logs
     *
     * @mbggenerated Wed May 16 13:48:21 CST 2018
     */
    MmpTemplateManagementLogs selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_template_management_logs
     *
     * @mbggenerated Wed May 16 13:48:21 CST 2018
     */
    int updateByPrimaryKeySelective(MmpTemplateManagementLogs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_template_management_logs
     *
     * @mbggenerated Wed May 16 13:48:21 CST 2018
     */
    int updateByPrimaryKey(MmpTemplateManagementLogs record);

	List<TemplateManagementLogDTO> queryTemplateLog(String templateId);
}