package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.dto.ProvisionNodeDTO;
import com.extracme.evcard.mmp.model.MmpProvisionNode;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface MmpProvisionNodeMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_node
     *
     * @mbggenerated Fri Sep 25 16:55:23 CST 2020
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_node
     *
     * @mbggenerated Fri Sep 25 16:55:23 CST 2020
     */
    int insert(MmpProvisionNode record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_node
     *
     * @mbggenerated Fri Sep 25 16:55:23 CST 2020
     */
    int insertSelective(MmpProvisionNode record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_node
     *
     * @mbggenerated Fri Sep 25 16:55:23 CST 2020
     */
    MmpProvisionNode selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_node
     *
     * @mbggenerated Fri Sep 25 16:55:23 CST 2020
     */
    int updateByPrimaryKeySelective(MmpProvisionNode record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_node
     *
     * @mbggenerated Fri Sep 25 16:55:23 CST 2020
     */
    int updateByPrimaryKey(MmpProvisionNode record);

    List<MmpProvisionNode> selectActiveNodes(@Param("provisionType") Integer provisionType);

    void batchInsert(@Param("list") List<ProvisionNodeDTO> nodes,
                     @Param("provisionType") Integer provisionType,
                     @Param("provisionId") Long provisionId, @Param("version") String version,
                     @Param("createOperId") Long createOperId, @Param("createOperName") String createOperName);

    void batchDisable(@Param("list") List<String> nodeIds,
                      @Param("provisionId") Long provisionId, @Param("version") String version,
                      @Param("updateTime") Date dateTime, @Param("updateOperId") Long updateOperId,
                      @Param("updateOperName") String updateOperName);

    void batchDisableAll(@Param("provisionId") Long provisionId, @Param("version") String version,
                      @Param("updateTime") Date dateTime, @Param("updateOperId") Long updateOperId,
                      @Param("updateOperName") String updateOperName);


}