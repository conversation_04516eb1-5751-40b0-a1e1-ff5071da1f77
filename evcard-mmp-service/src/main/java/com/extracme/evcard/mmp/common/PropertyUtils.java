package com.extracme.evcard.mmp.common;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.env.Environment;

/**
 * <AUTHOR>
 */
public class PropertyUtils {

    private static final Logger logger = LoggerFactory.getLogger(PropertyUtils.class);


    public static String getProperty(String key) {
        Environment environment = SpringContextUtil.getBean(Environment.class);
        return environment.getProperty(key);
    }

    public static String getProperty(String key, String defaultValue) {
        String value = null;
        try{
            value = getProperty(key);
        }catch(Exception ex) {
            logger.info("加载properties文件内容完成...........");
        }

        if(!StringUtils.isNoneBlank(value)) {
            value = defaultValue;
        }
        return value;
    }
}
