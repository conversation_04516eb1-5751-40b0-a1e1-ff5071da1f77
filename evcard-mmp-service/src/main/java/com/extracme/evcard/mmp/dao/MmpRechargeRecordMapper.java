package com.extracme.evcard.mmp.dao;

import java.util.List;

import com.extracme.evcard.mmp.dto.*;
import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.model.MmpRechargeRecord;

public interface MmpRechargeRecordMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_record
     *
     * @mbg.generated Mon Sep 10 09:04:07 CST 2018
     */
    int deleteByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_record
     *
     * @mbg.generated Mon Sep 10 09:04:07 CST 2018
     */
    int insert(MmpRechargeRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_record
     *
     * @mbg.generated Mon Sep 10 09:04:07 CST 2018
     */
    int insertSelective(MmpRechargeRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_record
     *
     * @mbg.generated Mon Sep 10 09:04:07 CST 2018
     */
    MmpRechargeRecord selectByPrimaryKey(String id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_record
     *
     * @mbg.generated Mon Sep 10 09:04:07 CST 2018
     */
    int updateByPrimaryKeySelective(MmpRechargeRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_record
     *
     * @mbg.generated Mon Sep 10 09:04:07 CST 2018
     */
    int updateByPrimaryKey(MmpRechargeRecord record);

	List<RechargeRecordDTO> queryRechargeRecordList(RechargeRecordDTO rechargeRecordDTO);

	RechargeEActivityDetailDTO getEOfferCouponActivityDetail(@Param("id") Long id);

	String getHqEActivityId();

	List<EAccountInfoDto> getAuthInfos(EAccountDto eRemainDTO);


    List<EAccountInfoDto> getMemEAccountList(EAccountQueryDto eaccountQuryDto);

    /**
     * 根据会员主键key查询会员列表
     * @param memberIds 会员主键Id数组
     * @return List<EAccountDto>
     */
    List<EAccountInfoDto> getEAccountByIds(@Param("pkIds")Integer[] memberIds);


	List<AgencyERemainDTO> getAgencyERemainNumList(AgencyERemainDTO agencyERemainDTO);

	List<RechargeRecordDTO> exportExternalTaskList(RechargeRecordDTO rechargeRecordDTO);
}