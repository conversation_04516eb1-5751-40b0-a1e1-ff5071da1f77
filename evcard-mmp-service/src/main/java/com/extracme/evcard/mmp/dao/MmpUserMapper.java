package com.extracme.evcard.mmp.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.dto.LoginUserInfo;
import com.extracme.evcard.mmp.dto.QueryUserInfoDetail;
import com.extracme.evcard.mmp.dto.QueryUserInfoListDTO;
import com.extracme.evcard.mmp.dto.QueryUserInfoResult;
import com.extracme.evcard.mmp.model.MmpUser;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

/**
 * Mapper类，，对应表mmp_user
 */
@OecsMapper
public interface MmpUserMapper extends Dao<MmpUser>{

    /**
     * 根据用户名查询用户
     * @param username
     * @return
     * <AUTHOR>
     */
    MmpUser selectByUsername(@Param("username") String username);
    
    /**
     * 获取用户信息列表
     * @param queryUserInfoListDTO
     * @return
     * <AUTHOR>
     */
//    @Deprecated
//	List<QueryUserInfoResult> queryUserInfoList(
//			@Param("dto") QueryUserInfoListDTO queryUserInfoListDTO,
//			@Param("userOrgCode") String userOrgCode);
	
	/**
     * 获取用户详情
     * @param userId
     * @return
     * <AUTHOR>
     */
	QueryUserInfoDetail queryUserInfoDetail(@Param("userId") Long userId);
	
	/**
     * 获取登陆用户信息
     * @param userName
     * @return
     * <AUTHOR>
     */
	LoginUserInfo selectLoginInfo(@Param("username") String username);

	
	/**
	 * 查询邮箱地址
	 * @param mobilePhones
	 * @param orgId
	 * @return
	 */
	List<String> queryMailAddress(@Param("mobilePhones") String[] mobilePhones, @Param("orgId") String orgId);

	List<MmpUser> selectByIds(@Param("ids") Long[] ids);

	List<LoginUserInfo> selectOperatorByIds(@Param("ids") Long[] ids);
}