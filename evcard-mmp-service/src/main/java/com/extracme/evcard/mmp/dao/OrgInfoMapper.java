package com.extracme.evcard.mmp.dao;

import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.dto.ComBoxDTO;
import com.extracme.evcard.mmp.dto.OrgInfoDTO;
import com.extracme.evcard.mmp.dto.OrgTreeDTO;
import com.extracme.framework.data.dao.OecsMapper;

/**
 * Mapper类，，对应表org_info
 */
@OecsMapper
public interface OrgInfoMapper {

    /**
     * 查询所有机构信息.<br>
     * @return
     * <AUTHOR>
     */
    public List<OrgInfoDTO> findAll();

    /**
     * 查询所有机构信息.<br>
     * @return
     * <AUTHOR>
     */
    public List<ComBoxDTO> getAllOrgInfo();

    /**
     * 查询指定类型的机构列表.<br>
     * @param orgClass 机构类型.<br>
     * @param orgId 查询的根机构.<br>
     * @return
     * <AUTHOR>
     */
    public List<OrgInfoDTO> findByOrgClass(@Param("orgClass") int[] orgClass, @Param("orgId") String orgId);

    /**
     * 根据主键查询机构
     * @param orgId
     * @return
     */
    public OrgInfoDTO selectByPrimaryKey(@Param("orgId") String orgId);

    /**
     * 根据orgId查询所有机构信息.<br>
     * @return
     * <AUTHOR>
     */
    public List<OrgInfoDTO> getOrgInfoById(@Param("orgId") String orgId);

    /**
     * 根据orgId查询机构名称
     * @param orgId
     * <AUTHOR>
     * @return
     */
    public String getNameById(@Param("orgId") String orgId);

	public List<OrgInfoDTO> getOrgIdList(@Param("orgId") String orgId);

    public List<OrgInfoDTO> getOrgIdPageList(@Param("orgId") String orgId);

    public List<OrgInfoDTO> getChildOrgIdList(@Param("orgId") String orgId);

    /**
     * 根据orgIds查询机构列表
     * @param orgId
     * @return
     */
    public List<OrgInfoDTO> getOrgListByIds(@Param("orgId") String[] orgId);

    /**
     * 根据orgIds查询机构列表
     * @param orgId
     * @return
     */
    List<Map<String, String>> getOrgMapByIds(@Param("orgId") Set<String> orgId);

    String getOrgCityByOrgCode(String orgId);
}