package com.extracme.evcard.mmp.config;


import com.extracme.evcard.mmp.common.OSSUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OssConfig {
    @Value("${ali_accessId}")
    private String accessKeyId;

    @Value("${ali_accessKey}")
    private String accessKeySecret;

    @Value("${oss_bucket}")
    private String bucketName;

    @Value("${oss_endPoint}")
    private String endpoint;

    @Bean(value = "ossUtil")
    public OSSUtil initOssUtils(){
        OSSUtil ossUtil = new OSSUtil();
        ossUtil.setAccessId(accessKeyId);
        ossUtil.setAccessKey(accessKeySecret);
        ossUtil.setBucket(bucketName);
        ossUtil.setEndPoint(endpoint);
        ossUtil.init();
        return ossUtil;
    }

}
