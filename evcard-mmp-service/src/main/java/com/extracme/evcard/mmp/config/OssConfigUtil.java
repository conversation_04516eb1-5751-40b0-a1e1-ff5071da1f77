package com.extracme.evcard.mmp.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

@Configuration
//@PropertySource(value = "classpath:aliyunConfig.properties")
public class OssConfigUtil {

    private static String accessKeyId;
    private static String accessKeySecret;
    private static String bucketName;
    private static String endpoint;
    private static String ENV;
    private static String basePath;

    private static String httpsBasePath;

    // 时行人脸图片url前缀
    private static String sgmCarSharingFacePathPrefix;


    public static String getHttpsBasePath() {
        return httpsBasePath;
    }

    @Value("${https_basePath:https://evcard.oss-cn-shanghai.aliyuncs.com}")
    public void setHttpsBasePath(String value) {
        httpsBasePath = value;
    }

    @Value("${ali_accessId}")
    public void setAccessKeyId(String value) {
        accessKeyId = value;
    }

    @Value("${ali_accessKey}")
    public void setAccessKeySecret(String value) {
        accessKeySecret = value;
    }

    @Value("${oss_bucket}")
    public void setBucketName(String value) {
        bucketName = value;
    }

    @Value("${oss_endPoint}")
    public void setEndpoint(String value) {
        endpoint = value;
    }

    @Value("${env}")
    public void setENV(String value) {
        ENV = value;
    }

    @Value("${showImg_title}")
    public void setBasePath(String value) {
        basePath = value;
    }


    public static String getAccessKeyId() {
        return accessKeyId;
    }

    public static String getAccessKeySecret() {
        return accessKeySecret;
    }

    public static  String getBucketName() {
        return bucketName;
    }

    public static String getEndpoint() {
        return endpoint;
    }

    public static String getENV() {
        return ENV;
    }

    public static String getBasePath() {
        return basePath;
    }

    public static String getSgmCarSharingFacePathPrefix() {
        return sgmCarSharingFacePathPrefix;
    }

    @Value("${sgm_car_sharing_face_path_prefix:http://extracme-sgm}")
    public void setSgmCarSharingFacePathPrefix(String value) {
        sgmCarSharingFacePathPrefix = value;
    }
}
