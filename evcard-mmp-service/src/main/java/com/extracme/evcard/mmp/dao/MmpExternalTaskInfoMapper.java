package com.extracme.evcard.mmp.dao;

import java.util.List;

import com.extracme.evcard.mmp.bo.QueryTaskStatisticsBO;
import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.dto.TaskStatisticsDTO;
import com.extracme.evcard.mmp.model.MmpExternalTaskInfo;

public interface MmpExternalTaskInfoMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_external_task_info
	 * @mbg.generated  Wed Dec 05 15:44:29 CST 2018
	 */
	int deleteByPrimaryKey(String taskSeq);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_external_task_info
	 * @mbg.generated  Wed Dec 05 15:44:29 CST 2018
	 */
	int insert(MmpExternalTaskInfo record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_external_task_info
	 * @mbg.generated  Wed Dec 05 15:44:29 CST 2018
	 */
	int insertSelective(MmpExternalTaskInfo record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_external_task_info
	 * @mbg.generated  Wed Dec 05 15:44:29 CST 2018
	 */
	MmpExternalTaskInfo selectByPrimaryKey(String taskSeq);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_external_task_info
	 * @mbg.generated  Wed Dec 05 15:44:29 CST 2018
	 */
	int updateByPrimaryKeySelective(MmpExternalTaskInfo record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table mmp_external_task_info
	 * @mbg.generated  Wed Dec 05 15:44:29 CST 2018
	 */
	int updateByPrimaryKey(MmpExternalTaskInfo record);
	
	/**
	 * 任务创建
	 * 
	 * @param record
	 * @return
	 * @since
	 */
	int insertTask(MmpExternalTaskInfo record);
	
	/**
	 * 任务更新
	 * 
	 * @param record
	 * @return
	 * @since
	 */
	int updateTask(MmpExternalTaskInfo record);

	/**
	 * 查询任务统计列表
	 * @param queryTaskStatisticsBO
	 * @return
	 */
	List<TaskStatisticsDTO> queryTaskStatisticsList(QueryTaskStatisticsBO queryTaskStatisticsBO);
}