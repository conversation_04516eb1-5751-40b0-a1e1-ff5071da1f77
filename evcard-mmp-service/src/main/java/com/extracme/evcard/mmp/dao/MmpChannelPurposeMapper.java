package com.extracme.evcard.mmp.dao;

import java.util.List;

import com.extracme.evcard.mmp.dto.ChannelPurposeDTO;
import com.extracme.evcard.mmp.model.MmpChannelPurpose;

public interface MmpChannelPurposeMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_channel_purpose
     *
     * @mbg.generated Mon Jan 28 14:35:59 CST 2019
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_channel_purpose
     *
     * @mbg.generated Mon Jan 28 14:35:59 CST 2019
     */
    int insert(MmpChannelPurpose record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_channel_purpose
     *
     * @mbg.generated Mon Jan 28 14:35:59 CST 2019
     */
    int insertSelective(MmpChannelPurpose record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_channel_purpose
     *
     * @mbg.generated Mon Jan 28 14:35:59 CST 2019
     */
    MmpChannelPurpose selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_channel_purpose
     *
     * @mbg.generated Mon Jan 28 14:35:59 CST 2019
     */
    int updateByPrimaryKeySelective(MmpChannelPurpose record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_channel_purpose
     *
     * @mbg.generated Mon Jan 28 14:35:59 CST 2019
     */
    int updateByPrimaryKey(MmpChannelPurpose record);

	List<ChannelPurposeDTO> getChannelPurpose();
}