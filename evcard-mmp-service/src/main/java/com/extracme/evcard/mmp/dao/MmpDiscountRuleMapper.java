package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.dto.DiscountRuleDTO;
import com.extracme.evcard.mmp.model.MmpDiscountRule;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@OecsMapper
public interface MmpDiscountRuleMapper extends Dao<MmpDiscountRule> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_discount_rule
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_discount_rule
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    int insert(MmpDiscountRule record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_discount_rule
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    int insertSelective(MmpDiscountRule record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_discount_rule
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    MmpDiscountRule selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_discount_rule
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    int updateByPrimaryKeySelective(MmpDiscountRule record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_discount_rule
     *
     * @mbggenerated Wed May 09 13:53:04 CST 2018
     */
    int updateByPrimaryKey(MmpDiscountRule record);

    List<DiscountRuleDTO> findByAgencyId(@Param("agencyId") String agencyId, @Param("discountType") Integer discountType);
}