package com.extracme.evcard.mmp.dao;

import java.util.List;

import com.extracme.evcard.mmp.model.MmpRechargePackagesOrganization;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

@OecsMapper
public interface MmpRechargePackagesOrganizationMapper extends Dao<MmpRechargePackagesOrganization> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_organization
     *
     * @mbggenerated Thu Apr 19 09:36:16 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_organization
     *
     * @mbggenerated Thu Apr 19 09:36:16 CST 2018
     */
    int insert(MmpRechargePackagesOrganization record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_organization
     *
     * @mbggenerated Thu Apr 19 09:36:16 CST 2018
     */
    int insertSelective(MmpRechargePackagesOrganization record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_organization
     *
     * @mbggenerated Thu Apr 19 09:36:16 CST 2018
     */
    MmpRechargePackagesOrganization selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_organization
     *
     * @mbggenerated Thu Apr 19 09:36:16 CST 2018
     */
    int updateByPrimaryKeySelective(MmpRechargePackagesOrganization record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_recharge_packages_organization
     *
     * @mbggenerated Thu Apr 19 09:36:16 CST 2018
     */
    int updateByPrimaryKey(MmpRechargePackagesOrganization record);

    /**
     * 根据套餐id 批量删除运营公司
     * @param packagesId
     * @return
     */
	int batchDelPackagesOrganization(Long packagesId);

    /**
     * 批量保存套餐运营公司
     * @param list
     * @return
     */
	int batchInsertPackagesOrganization(List<MmpRechargePackagesOrganization> list);

    /**
     * 根据套餐id查询运营公司
     * @param packagesId
     * @return
     */
    List<String> selectRechargePackagesOrganizationByPackagesId(Long packagesId);
}