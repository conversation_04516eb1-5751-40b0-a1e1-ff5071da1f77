package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.model.ShortActivitiesSku;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface ShortActivitiesSkuMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table short_activities_sku
     *
     * @mbggenerated Mon Apr 20 10:44:54 CST 2020
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table short_activities_sku
     *
     * @mbggenerated Mon Apr 20 10:44:54 CST 2020
     */
    int insert(ShortActivitiesSku record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table short_activities_sku
     *
     * @mbggenerated Mon Apr 20 10:44:54 CST 2020
     */
    int insertSelective(ShortActivitiesSku record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table short_activities_sku
     *
     * @mbggenerated Mon Apr 20 10:44:54 CST 2020
     */
    ShortActivitiesSku selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table short_activities_sku
     *
     * @mbggenerated Mon Apr 20 10:44:54 CST 2020
     */
    int updateByPrimaryKeySelective(ShortActivitiesSku record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table short_activities_sku
     *
     * @mbggenerated Mon Apr 20 10:44:54 CST 2020
     */
    int updateByPrimaryKey(ShortActivitiesSku record);

    List<ShortActivitiesSku> selectRunningActivityByTime(@Param("activityStartTime") Date activityStartTime,
                                                    @Param("activityEndTime") Date activityEndTime,
                                                    @Param("orgId") String orgId,
                                                    @Param("cityIds") List<String> cityIds);
}