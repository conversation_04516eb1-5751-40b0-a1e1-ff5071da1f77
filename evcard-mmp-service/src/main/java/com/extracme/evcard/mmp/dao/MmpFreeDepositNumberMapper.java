package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.dto.AgencyFreeDepositDetailDTO;
import com.extracme.evcard.mmp.model.MmpFreeDepositNumber;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

@OecsMapper
public interface MmpFreeDepositNumberMapper extends Dao<MmpFreeDepositNumber>{
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_free_deposit_number
     *
     * @mbggenerated Fri May 18 16:23:34 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_free_deposit_number
     *
     * @mbggenerated Fri May 18 16:23:34 CST 2018
     */
    int insert(MmpFreeDepositNumber record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_free_deposit_number
     *
     * @mbggenerated Fri May 18 16:23:34 CST 2018
     */
    int insertSelective(MmpFreeDepositNumber record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_free_deposit_number
     *
     * @mbggenerated Fri May 18 16:23:34 CST 2018
     */
    MmpFreeDepositNumber selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_free_deposit_number
     *
     * @mbggenerated Fri May 18 16:23:34 CST 2018
     */
    int updateByPrimaryKeySelective(MmpFreeDepositNumber record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_free_deposit_number
     *
     * @mbggenerated Fri May 18 16:23:34 CST 2018
     */
    int updateByPrimaryKey(MmpFreeDepositNumber record);

    AgencyFreeDepositDetailDTO getFreeDeposit(String agencyId);
}