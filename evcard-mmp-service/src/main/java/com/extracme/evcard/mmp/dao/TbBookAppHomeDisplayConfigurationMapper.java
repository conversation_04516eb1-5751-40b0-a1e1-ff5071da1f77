package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.dto.activity.SearchAppHomeConfigTo;
import com.extracme.evcard.mmp.model.TbBookAppHomeDisplayConfiguration;
import com.extracme.evcard.mmp.model.TbBookAppHomeDisplayConfigurationExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbBookAppHomeDisplayConfigurationMapper {
    int countByExample(TbBookAppHomeDisplayConfigurationExample example);

    int deleteByExample(TbBookAppHomeDisplayConfigurationExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TbBookAppHomeDisplayConfiguration record);

    int insertSelective(TbBookAppHomeDisplayConfiguration record);

    List<TbBookAppHomeDisplayConfiguration> selectByExample(TbBookAppHomeDisplayConfigurationExample example);

    TbBookAppHomeDisplayConfiguration selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TbBookAppHomeDisplayConfiguration record, @Param("example") TbBookAppHomeDisplayConfigurationExample example);

    int updateByExample(@Param("record") TbBookAppHomeDisplayConfiguration record, @Param("example") TbBookAppHomeDisplayConfigurationExample example);

    int updateByPrimaryKeySelective(TbBookAppHomeDisplayConfiguration record);

    int updateByPrimaryKey(TbBookAppHomeDisplayConfiguration record);

    /**
     * 查询是否存在
     * @param orgId
     * @param asList
     * @return
     */
    List<TbBookAppHomeDisplayConfiguration> selectExistConfig(@Param("orgId")String orgId, @Param("list") List<Integer> asList);

    /**
     *
     * @param to
     * @return
     */
    List<TbBookAppHomeDisplayConfiguration> selectList(SearchAppHomeConfigTo to);

    /**
     *
     * @param to
     * @return
     */
    long selectCount(SearchAppHomeConfigTo to);
}