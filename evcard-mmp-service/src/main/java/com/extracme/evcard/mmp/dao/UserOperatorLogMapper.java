package com.extracme.evcard.mmp.dao;

import java.util.List;
import java.util.Map;

import com.extracme.evcard.mmp.dto.sms.SmsOperatorLogDTO;
import org.apache.ibatis.annotations.Param;

import com.extracme.evcard.mmp.dto.NightCarOperatorLogDTO;
import com.extracme.evcard.mmp.dto.UserOperatorLogDTO;
import com.extracme.evcard.mmp.model.UserOperatorLog;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

/**
 * Mapper类，，对应表user_operator_log
 */
@OecsMapper
public interface UserOperatorLogMapper extends Dao<UserOperatorLog> {
    // 操作日志一览
    List<UserOperatorLogDTO> queryAll(String authId);

    // <!-- 插入固定资产操作日志的记录 -->
    Integer saveSelective(UserOperatorLog userOperatorLog);

    void insert(Map map);

    List<NightCarOperatorLogDTO> queryNightCarOperator(@Param("id") String id);

    /**
     * 根据FOREIGN_KEY查询用户操作日志记录
     * @param id
     * @return
     */
    List<UserOperatorLogDTO> queryUserOperatorLog(@Param("id") String id);

    /**
     * 获取营销短信操作日志
     *
     * @param logId logId
     * @return List<SmsOperatorLogDTO>
     */
    List<SmsOperatorLogDTO> querySmsOperatorLog(@Param("logId") String logId);

}