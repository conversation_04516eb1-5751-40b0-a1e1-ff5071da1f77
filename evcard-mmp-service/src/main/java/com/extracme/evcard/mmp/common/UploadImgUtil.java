package com.extracme.evcard.mmp.common;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;

import com.aliyun.oss.*;
import com.extracme.evcard.mmp.config.OssConfigUtil;
import org.apache.commons.io.IOUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import com.aliyun.oss.model.OSSObject;

/**
 * 上传图片阿里OSS
 */
public class UploadImgUtil {

    private final static String OSS_BUCKET = OssConfigUtil.getBucketName();
    private final static String OSS_ENDPOINT = OssConfigUtil.getEndpoint();
    private final static String ALI_ACCESSID = OssConfigUtil.getAccessKeyId();
    private final static String ALI_ACCESSKEY = OssConfigUtil.getAccessKeySecret();
    private final static String ENV = OssConfigUtil.getENV();

    private final static String OSS_BASEPATH = OssConfigUtil.getBasePath();

    private final static String HTTPS_OSS_BASEPATH = OssConfigUtil.getHttpsBasePath();

    private static final Log logger = LogFactory.getLog(UploadImgUtil.class);

    /**
     * 上传
     * @param sourceFile 文件
     * @param pathUrl 存放路径地址
     * <AUTHOR>
     */
    public static void uploadStream(final InputStream sourceFile, final String pathUrl) {
        ThreadPoolUtils.executor.execute(() -> {
            // 上传图片到oss服务器
            OSSClient ossClient = null;
            try {
                ossClient = new OSSClient(OSS_ENDPOINT, ALI_ACCESSID, ALI_ACCESSKEY);
                ossClient.putObject(OSS_BUCKET, ENV + pathUrl, sourceFile);
                IOUtils.closeQuietly(sourceFile);
            } catch (OSSException e) {
                // TODO Auto-generated catch block
                logger.error(e);
            } catch (ClientException e) {
                logger.error(e);
            } catch (Exception e) {
                logger.error(e);
            } finally {
                // 关闭client
                if (ossClient != null) {
                    ossClient.shutdown();
                }
            }
        });
    }

    /**
     * 上传
     * @param sourceFile 文件
     * @param pathUrl 存放路径地址
     * <AUTHOR>
     */
    public static void uploadStream(final File sourceFile, final String pathUrl) {
        ThreadPoolUtils.executor.execute(() -> {
            // 上传图片到oss服务器
            OSSClient ossClient = null;
            FileInputStream fileInputStream = null;
            try {
                fileInputStream = new FileInputStream(sourceFile);
                ossClient = new OSSClient(OSS_ENDPOINT, ALI_ACCESSID, ALI_ACCESSKEY);
                ossClient.putObject(OSS_BUCKET, ENV + pathUrl, fileInputStream);
            } catch (OSSException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            } catch (ClientException e) {
                // TODO Auto-generated catch block
                e.printStackTrace();
            } catch (Exception e) {
                // TODO: handle exception
            } finally {
                IOUtils.closeQuietly(fileInputStream);
                // 关闭client
                if (ossClient != null) {
                    ossClient.shutdown();
                }
            }
        });
    }

    /**
     * 上传 同步上传
     * @param sourceFile 文件
     * @param pathUrl 存放路径地址
     * <AUTHOR>
     */
    public static boolean uploadStreamSyn(final InputStream sourceFile, final String pathUrl) {
        // 上传图片到oss服务器
        OSSClient ossClient = null;
        try {
            ossClient = new OSSClient(OSS_ENDPOINT, ALI_ACCESSID, ALI_ACCESSKEY);
            ossClient.putObject(OSS_BUCKET, ENV + pathUrl, sourceFile);
            IOUtils.closeQuietly(sourceFile);
            return true;
        } catch (OSSException e) {
            logger.error(e);
        } catch (ClientException e) {
            logger.error(e);
        } catch (Exception e) {
            logger.error(e);
        } finally {
            // 关闭client
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return false;
    }

    /**
     * 上传
     * @param sourceFile  文件
     * @param pathUrl    存放路径地址
     * <AUTHOR>
     */
    public static boolean uploadStreamSyn(final File sourceFile,final String pathUrl){
        // 上传图片到oss服务器
        OSSClient ossClient = null;
        FileInputStream fileInputStream = null;
        try {
            //TODO buffed
            fileInputStream = new FileInputStream(sourceFile);
            ossClient = new OSSClient(OSS_ENDPOINT, ALI_ACCESSID, ALI_ACCESSKEY);
            ossClient.putObject(OSS_BUCKET, ENV + pathUrl, fileInputStream);
            return true;
        } catch (OSSException e) {
            logger.error(e);
        } catch (ClientException e) {
            logger.error(e);
        } catch (Exception e) {
            logger.error(e);
        } finally {
            IOUtils.closeQuietly(fileInputStream);
            // 关闭client
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
        return false;
    }

    /**
     * 上传
     * @param sourceFile 文件
     * @param pathUrl 存放路径地址
     * <AUTHOR>
     */
    public static String uploadImgStream(final InputStream sourceFile, final String pathUrl) {
        ThreadPoolUtils.executor.execute(() -> {
            // 上传图片到oss服务器
            OSSClient ossClient = null;
            try {
                ossClient = new OSSClient(OSS_ENDPOINT, ALI_ACCESSID, ALI_ACCESSKEY);
                ossClient.putObject(OSS_BUCKET, ENV + pathUrl, sourceFile);
                IOUtils.closeQuietly(sourceFile);
            } catch (OSSException e) {
                // TODO Auto-generated catch block
                logger.error(e);
            } catch (ClientException e) {
                logger.error(e);
            } catch (Exception e) {
                logger.error(e);
            } finally {
                // 关闭client
                if (ossClient != null) {
                    ossClient.shutdown();
                }
            }

        });
        return pathUrl;
    }
    
    public static BufferedInputStream downloadStream(String key) {
    	OSSClient ossClient = new OSSClient(OSS_ENDPOINT, ALI_ACCESSID, ALI_ACCESSKEY);
		OSSObject object = ossClient.getObject(OSS_BUCKET, ENV+key);
		BufferedInputStream input = new BufferedInputStream(object.getObjectContent());
		return input;
    }
    
    public static void deleteStream(String key) {
    	OSSClient ossClient = new OSSClient(OSS_ENDPOINT, ALI_ACCESSID, ALI_ACCESSKEY);
		ossClient.deleteObject(OSS_BUCKET, ENV+key);
    }


    /**
     * 根据文件路径转化为文件流 ，上传到OSS
     *
     * @param filePath   文件本地地址
     * @param objectName 对象名称
     */
    public static String uploadSynByFilePath(final String filePath, String objectName) {
        HttpURLConnection conn = null;
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(filePath);
            return uploadFileStreamSyn(fis, objectName,false);
        } catch (Exception e) {
            logger.error("uploadSynByFileUrl失败", e);
            return null;
        } finally {
            IOUtils.closeQuietly(fis);
            IOUtils.close(conn);
        }
    }

    /**
     * 根据文件路径转化为文件流 ，上传到OSS
     *
     * @param filePath   文件本地地址
     * @param objectName 对象名称
     */
    public static String uploadSynByFilePathGetHttpsPath(final String filePath, String objectName) {
        HttpURLConnection conn = null;
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(filePath);
            return uploadFileStreamSyn(fis, objectName,true);
        } catch (Exception e) {
            logger.error("uploadSynByFileUrl失败", e);
            return null;
        } finally {
            IOUtils.closeQuietly(fis);
            IOUtils.close(conn);
        }
    }


    /**
     * 上传  同步上传
     *
     * @param sourceFile 文件
     * @param pathUrl 存放路径地址
     * @param isHttps 返回地址是否要求https
     */
    public static String uploadFileStreamSyn(final InputStream sourceFile, final String pathUrl,boolean isHttps) {
        OSS ossClient = null;
        try {
            ossClient = new OSSClientBuilder().build(OSS_ENDPOINT, ALI_ACCESSID, ALI_ACCESSKEY);
            ossClient.putObject(OSS_BUCKET, ENV + pathUrl, sourceFile);
            IOUtils.closeQuietly(sourceFile);
            // 返回阿里云全链接地址
            if (isHttps) {
                return HTTPS_OSS_BASEPATH + "/" + ENV + pathUrl;
            }else{
                return OSS_BASEPATH + "/" + ENV + pathUrl;
            }
        } catch (Exception e) {
            logger.error("uploadFileStreamSyn失败", e);
            return null;
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }


}
