package com.extracme.evcard.mmp.common;

import com.extracme.evcard.mmp.config.OssConfigUtil;
import com.extracme.evcard.mmp.dao.*;
import com.extracme.evcard.mmp.dto.MembershipDTO;
import com.extracme.evcard.mmp.dto.MembershipInfoDTO;
import com.extracme.evcard.mmp.dto.OperatorDTO;
import com.extracme.evcard.mmp.model.*;
import com.extracme.evcard.mmp.service.IOperateCityService;
import com.extracme.evcard.redis.JedisUtil;
import com.extracme.evcard.rpc.vipcard.dto.OperatorDto;
import com.extracme.evcard.tcs.provider.api.dto.FileItemDTO;
import com.extracme.evcard.tcs.provider.api.dto.OperateDTO;
import com.extracme.framework.core.dto.DefaultServiceRespDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 项目名称：evcard-fas-service
 * 类名称：ComUtil
 * 类描述：共通类
 * 创建人：shenjh-沈建华
 * 创建时间：2017年4月11日 下午4:11:52
 * 修改备注：
 *
 * @version1.0
 */
@SuppressWarnings("ALL")
public class ComUtil {

    private static final Logger log = LoggerFactory.getLogger(ComUtil.class);

    public static final String SmsMode = PropertyUtils.getProperty("smsMode");
    public static final String CounterfeitTemplateId = PropertyUtils.getProperty("CounterfeitSmsTemplateId");
    public static final String ReexamineTemplateId = PropertyUtils.getProperty("ReexamineSmsTemplateId");

    public static final String masAppUrl = PropertyUtils.getProperty("mas.api.url");
    public static final String logoutApi = PropertyUtils.getProperty("logout.api");

    public static TimeZone timeZoneChina = TimeZone.getTimeZone("Asia/Shanghai");// 获取时区
    public static final FastDateFormat ISO_DATETIME_TIME_ZONE_FORMAT = FastDateFormat.getInstance("yyyy-MM-dd'T'HH:mm:ssZZ");

    // 插入
    public static final String TABLE_OPE_INSERT = "新增";
    // 更新
    public static final String TABLE_OPE_UPDATE = "修改";
    // 删除
    public static final String TABLE_OPE_DELETE = "删除";
    // 启用
    public static final String TABLE_OPE_USING = "启用";
    // 禁用
    public static final String TABLE_OPE_FORBIDDEN = "禁用";

    // 存储用户信息的key前缀
    private static String USER_REDISKEY = "user_sso_";

    public static final String DATE_TYPE1 = "yyyy-MM-dd HH:mm:ss";

    public static final String DATE_TYPE2 = "yyyy-MM-dd HH:mm:ss.SSS";

    public static final String DATE_TYPE3 = "yyyyMMddHHmmssSSS";

    public static final String DATE_TYPE4 = "yyyyMMddHHmmss";

    public static final String DATE_TYPE5 = "yyyy-MM-dd";

    public static final String DATE_TYPE6 = "yy-MM-dd-HH-mm-ss";

    public static final String DATE_TYPE7 = "yyyy-MM-dd HH:mm";

    public static final String DATE_TYPE8 = "yyyyMMdd";

    public static final String DATE_TYPE9 = "yyyy-M-d H:m:s:S";

    public static final String DATE_TYPE10 = "yyyyMMddHHmm";

    public static final String DATE_TYPE11 = "yyyy-M-d H:m:s";

    public static final String DATE_TYPE12 = "yy-MM-dd HH:mm:ss";

    public static final String DATE_TYPE13 = "yyyy/MM/dd HH:mm:ss";

    public static final String DATE_TYPE14 = "MM-dd HH:mm:ss";

    public static final String DATE_TYPE15 = "yyyy年MM月dd日 HH:mm";
    public static final String DATE_TYPE16 = "MM月dd日 HH:mm";

    public static final String DATE_TYPE17 = "yyyyMM";

    public static final String DATE_TYPE18 = "HHmmss";
    public static final String DATE_TYPE19 = "HH:mm:ss";
    public static final String DATE_TYPE20 = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_TYPE21 = "HHmmssSSS";
    public static final String DATE_TYPE22 = "yyyyMMdd000000";
    public static final String DATE_TYPE23 = "yyyyMMdd235900";

    public static final String DATE_TYPE24 = "yyyyMMddHH0000";
    public static final String DATE_TYPE25 = "yyyyMMddHH5959";
    public static final String DATE_TYPE26 = "yyyy-MM";
    public static final String DATE_TYPE27 = "yyyyMMdd235959";

    public static final String DATE_TYPE28 = "yyyyMM01000000";
    public static final String DATE_TYPE29 = "yyyyMM31235959";
    public static final String DATE_TYPE30 = "yyyyMMddHH";

    public static final String DATE_TYPE31 = "HH:mm";

    public static final String msg = "您没有权限！";

    /**
     * 正则-url
     */
    public static final Pattern URL_PATTERN = Pattern.compile("^((https|http)?:\\/\\/)[^\\s]+");
    /**
     * 正则-邮件标题
     */
    public static final Pattern EMAIL_TITLE_PATTERN = Pattern.compile("(?<=title\\>).*(?=</title)");
    /**
     * 正则-数字
     */
    public static final Pattern NUMBER_PATTERN = Pattern.compile("[0-9]*");
    /**
     * 正则-数值
     */
    public static final Pattern NUMERIC_PATTERN = Pattern.compile("^[0-9]+([.]{1}[0-9]+){0,1}$");
    public static final Pattern COMPLEX_NUM_PATTERN = Pattern.compile("([1-9]\\d*\\.?\\d*)|(0\\.\\d*[1-9])");
    /**
     * 正则-电话
     */
    public static final Pattern TEL_PATTERN = Pattern.compile("[0-9]{8,12}");
    public static final Pattern MOBILE_PATTERN = Pattern.compile("^1[0-9]{10}$");
    /**
     * 英文和数字
     */
    public static final Pattern EN_NUM_PATTERN = Pattern.compile("[A-Za-z0-9]*");
    public static final Pattern LOW_EN_NUM_PATTERN = Pattern.compile("^[0-9a-z_]{1,}$");


    public static final Pattern FLOAT_NUM_PATTERN = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$");

    /**
     * 取得用户名
     *
     * @param request 页面request
     * @return
     */
    public static ComModel getUserInfo(HttpServletRequest request) {
        // 调试开关
        int debugMode = 1;
        try {
            debugMode = Integer.parseInt(PropertyUtils.getProperty("debug.mode"));
        } catch (Exception e) {
            debugMode = 1;
        }

        ComModel model = new ComModel();

        if (debugMode == 0) {
            model.setCreateTime(new Timestamp(System.currentTimeMillis()));
            model.setUpdateTime(new Timestamp(System.currentTimeMillis()));

            // 获取组织机构ID
            // TODO 正式上线要改成从用户登陆情报取得信息，临时放在配置文件里面是为了单体测试方便
            String loginOrgId = "00";
            model.setCreateOperId(269L);
            model.setUpdateOperId(269L);
            model.setCreateOperName("孙彬");
            model.setOrgId(loginOrgId);
            model.setUserName("13951302948@hq");
            return model;
        } else {
            String username = request.getRemoteUser();
            Map<String, String> map = JedisUtil.hgetAll(USER_REDISKEY + username);
            if (map != null && !map.isEmpty()) {
                long id = Long.parseLong(map.get("id"));
                String name = map.get("name");
                String orgid = map.get("orgCode");
                Timestamp time = new Timestamp(System.currentTimeMillis());
                model.setCreateTime(time);
                model.setCreateOperId(id);
                model.setCreateOperName(name);
                model.setUpdateTime(time);
                model.setUpdateOperId(id);
                model.setUpdateOperName(name);
                model.setOrgId(orgid);
                return model;
            }
        }
        return null;
    }

    public static ComModel getUserInfo(OperatorDTO operatorDTO) {
        if (operatorDTO == null) {
            return null;
        }
        ComModel model = new ComModel();
        Timestamp time = new Timestamp(System.currentTimeMillis());
        model.setCreateTime(time);
        model.setCreateOperId(operatorDTO.getOperatorId());
        model.setCreateOperName(operatorDTO.getOperatorName());
        model.setUpdateTime(time);
        model.setUpdateOperId(operatorDTO.getOperatorId());
        model.setUpdateOperName(operatorDTO.getOperatorName());
        return model;
    }

    /**
     * 获取当前时间
     *
     * @return
     */
    public static String getUpdateTime() {
        // 指定格式
        DateFormat date_format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        date_format.setTimeZone(timeZoneChina);

        // 返回指定格式的字符串
        return date_format.format(new Date());
    }

    /**
     * 取得导出EXCEL头部
     *
     * @return
     */
    public static String getExcelName() {

        return "temp" + System.currentTimeMillis() + Contants.XLSX;
    }

    /**
     * 取得上传图片的名字
     *
     * @return
     */
    public static String getImageName() {
        return "temp" + System.currentTimeMillis();
    }

    /**
     * 取得操作履历信息
     *
     * @param opeType      操作类型
     * @param functionName 机能名称
     * @return
     */
    public static String getOpeContent(String opeType, String functionName) {
        return opeType + functionName;
    }

    /**
     * @param opeType      操作类型
     * @param functionName 机能名称
     * @return
     */
    public static String getUpdateContent(String object, String dbObject, String voObject) {
        if (dbObject == null || StringUtils.trim(dbObject).isEmpty() || dbObject == StringUtils.EMPTY) {
            dbObject = Contants.NULLVALUE;
        }
        if (voObject == null || StringUtils.trim(voObject).isEmpty() || voObject == StringUtils.EMPTY) {
            voObject = Contants.NULLVALUE;
        }
        if (StringUtils.equals(dbObject, voObject)) {
            return "";
        }
        return Contants.TABLE_OPE_UPDATE + object + Contants.MARK_COLON + "由" + dbObject + Contants.UPDATETO + voObject + Contants.MARK_SEMICOLON;
    }

    /**
     * 修改图片log保存共通
     *
     * @param opeType      操作类型
     * @param functionName 机能名称
     * @return
     */
    public static String getUpdateImgContent(String object, String dbObject, String voObject) {
        if (dbObject == null || StringUtils.trim(dbObject).isEmpty() || dbObject == StringUtils.EMPTY) {
            dbObject = Contants.NULLVALUE;
        }
        if (voObject == null || StringUtils.trim(voObject).isEmpty() || voObject == StringUtils.EMPTY) {
            voObject = Contants.NULLVALUE;
        }
        if (StringUtils.equals(dbObject, voObject)) {
            return "";
        }
        return Contants.TABLE_OPE_UPDATE + object + Contants.BR;
    }

    /**
     * 删除小数点后面多余的0
     *
     * @param s 字符串
     * @return
     */
    public static String rvZeroAndDot(String s) {
        if (s.isEmpty()) {
            return "";
        }

        if (s.indexOf(".") > 0) {
            s = s.replaceAll("0+?$", "");// 去掉多余的0
            s = s.replaceAll("[.]$", "");// 如最后一位是.则去掉
        }
        return s;
    }

    // 采购计划编号前缀
    public static final String CGJH_PREFIX = "CGJH";

    // 采购任务编号前缀
    public static final String CGRW_PREFIX = "CGRW";

    // 合同管理编号前缀
    public static final String HT_PREFIX = "HT";

    /**
     * 获取带毫秒的日期字符串
     *
     * @return
     */
    public static String getTimestampWithMsec() {
        String msecStr = "";
        DateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss.SSS");
        Timestamp datetime = new Timestamp(System.currentTimeMillis());
        msecStr = sdf.format(datetime);

        return msecStr;
    }

    /**
     * 取得当前类和当前方法的异常信息
     *
     * @return
     */
    public static String getExceptionMsg(Exception ex) {
        StackTraceElement[] stackTrace = new Throwable().getStackTrace();
        // 类名
        String className = stackTrace[1].getClassName();
        // 方法名
        String methodName = stackTrace[1].getMethodName();

        return "类名:" + className + " 方法名:" + methodName + " 异常信息:" + ex.toString();
    }

    /**
     * 取得车管系统访问接口路径
     *
     * @param ip            IP地址
     * @param port          端口号
     * @param interfaceName 接口名称
     * @return
     */
    public static String getUrl(String ip, String port, String interfaceName) {
        return Contants.HTTP + ip + Contants.MARK_COLON + port + interfaceName;
    }

    /**
     * 获取当前时间
     *
     * @param type 指定格式
     * @return
     */
    public static String getSystemDate(String type) {

        // 指定格式
        DateFormat date_format = new SimpleDateFormat(type);
        date_format.setTimeZone(timeZoneChina);

        // 范围指定格式的字符串
        return date_format.format(new Date());
    }

    /**
     * 时间转化
     *
     * @param dateStr
     * @param fromType
     * @param toType
     * @return
     */
    public static String getFormatDate(String dateStr, String fromType, String toType) {
        return DateUtils.getFormatDate(dateStr, fromType, toType);
    }

    /**
     * 新增操作日志
     *
     * @param operatorContent       日志内容
     * @param foreignKey
     * @param foreignKey2
     * @param createUser            操作人员
     * @param userOperatorLogMapper 操作日志mapper
     */
    public static void insertOperatorLog(String operatorContent, String foreignKey, String foreignKey2, String createUser, UserOperatorLogMapper userOperatorLogMapper) {
        UserOperatorLog userOperatorLog = new UserOperatorLog();
        userOperatorLog.setOperatorContent(operatorContent);
        userOperatorLog.setForeignKey(foreignKey);
        userOperatorLog.setForeignKey2(foreignKey2);
        userOperatorLog.setCreatedUser(createUser);
        userOperatorLog.setCreatedTime(getSystemDate("yyyyMMddHHmmssSSS"));
        userOperatorLogMapper.saveSelective(userOperatorLog);
    }

    /**
     * 身份证验证
     *
     * @param Id 身份证号
     * @return
     */
    public static boolean checkIDCard(String Id) {
        boolean bRet = false;
        if (Id == null) {
            return false;
        }

        // 长度是18位的身份证
        if (Id.length() == 18) {
            // 18位身份证验证
            bRet = CheckIDCard18(Id);

            return bRet;
        }
        // 长度是15位的身份证
        else if (Id.length() == 15) {
            // 15位身份证验证
            bRet = CheckIDCard15(Id);

            return bRet;
        } else {
            return false;
        }
    }

    /**
     * 18位身份证验证
     *
     * @param Id 身份证号
     * @return
     */
    private static boolean CheckIDCard18(String Id) {
        int sum = 0;

        // 数字验证

        if (!isNumeric(Id.substring(0, 17))) {
            return false;
        }

        // 省份验证
        String address = "11x22x35x44x53x12x23x36x45x54x13x31x37x46x61x14x32x41x50x62x15x33x42x51x63x21x34x43x52x64x65x71x81x82x91";
        if (address.indexOf(Id.substring(0, 2)) == -1) {
            return false;
        }

        // 生日验证
        String birth = Id.substring(6, 14);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        dateFormat.setLenient(false);
        try {
            dateFormat.parse(birth);
        } catch (Exception ex) {
            return false;
        }

        // 校验码验证
        // 校验码
        String[] arrVarifyCode = ("1,0,x,9,8,7,6,5,4,3,2").split(",");
        // Wi表示第i位置上的加权因子
        String[] Wi = ("7,9,10,5,8,4,2,1,6,3,7,9,10,5,8,4,2").split(",");
        // Ai表示第i位置上的身份证号码数字值
        char[] Ai = Id.toCharArray();
        // 对前17位数字本体码加权求和
        for (int i = 0; i < 17; i++) {
            sum += Integer.parseInt(Wi[i]) * (Integer.parseInt("" + Ai[i]));
        }
        // 以11对计算结果取模
        int yy = sum % 11;
        // Math.DivRem(sum, 11, out y);
        // 根据模的值得到对应的校验码对应关系
        if (!arrVarifyCode[yy].equals(Id.substring(17, 18).toLowerCase())) {
            return false;
        }

        // 符合GB11643-1999标准
        return true;
    }

    /**
     * 15位身份证验证
     *
     * @param Id 身份证号
     * @return
     */
    private static boolean CheckIDCard15(String Id) {

        // 数字验证
        if (!isNumeric(Id.substring(0, 14))) {
            return false;
        }

        // 省份验证
        String address = "11x22x35x44x53x12x23x36x45x54x13x31x37x46x61x14x32x41x50x62x15x33x42x51x63x21x34x43x52x64x65x71x81x82x91";
        if (address.indexOf(Id.substring(0, 2)) == -1) {
            return false;
        }

        // 生日验证
        String birth = Id.substring(6, 12);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        dateFormat.setLenient(false);
        try {
            dateFormat.parse(birth);
        } catch (Exception ex) {
            return false;
        }

        // 符合15位身份证标准
        return true;
    }

    /**
     * 数字验证
     *
     * @param str
     * @return
     */
    public static boolean isNumeric(String str) {
        Matcher isNum = ComUtil.NUMBER_PATTERN.matcher(str);
        if (isNum.matches()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 手机号验证
     * 大陆手机号码11位数，匹配格式：前三位固定格式+后8位任意数
     * 此方法中前三位格式有：
     * 13+任意数
     * 15+除4的任意数
     * 18+除1和4的任意数
     * 17+除9的任意数
     * 147
     */
    public static boolean checkMobilePhone(String mobilePhone) {
//        String regExp = "^1[3|4|5|7|8][0-9]{9}$";
        Matcher m = ComUtil.MOBILE_PATTERN.matcher(mobilePhone);
        if (m.matches()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 截取图片路径
     *
     * @param picUrl 上传图片路径
     * @return
     */
    private static String getNormalFileSubUrl(String picUrl) {
        String url = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(picUrl)) {
            //处理http与https
            picUrl = picUrl.replace(Contants.HTTPS, Contants.HTTP);
            String[] arr = picUrl.split(Contants.OSS_BASE_PATH);
            if(arr.length > 1) {
                url = arr[1];
            }
        }
        return url;
    }

    /**
     * 截取图片路径
     *
     * @param picUrl 上传图片路径
     * @return
     */
    public static String subStringUrl(String picUrl) {
        if(StringUtils.isBlank(picUrl)) {
            return StringUtils.EMPTY;
        }
        //不为敏感图片，直接截取url
        String url = picUrl;
        if (!picUrl.contains(Contants.SENSITIVE_BUCKET)){
            url =  getNormalFileSubUrl(picUrl);
        }
        //截取敏感图片地址
        if (StringUtils.isNotBlank(picUrl)) {
            String[] arr = picUrl.split(Contants.SENSITIVE_BUCKET);
            if(arr.length > 1) {
                url = Contants.SENSITIVE_BUCKET + arr[1];
            }
        }
        return trimRelativeUrl(url);
    }

    /**
     * 兼容时行处理人脸照片
     * @param picUrl
     * @return
     */
    public static String subStringUrlAdapter(String picUrl) {
        if(StringUtils.isBlank(picUrl)) {
            return StringUtils.EMPTY;
        }

        // 时行图片 直接返回，不做处理，数据库存的是时行完整url。
        if (picUrl.startsWith(OssConfigUtil.getSgmCarSharingFacePathPrefix()) || picUrl.startsWith("https://extracme-sgm")){
            return picUrl;
        }

        //不为敏感图片，直接截取url
        String url = picUrl;
        if (!picUrl.contains(Contants.SENSITIVE_BUCKET)){
            url =  getNormalFileSubUrl(picUrl);
        }
        //截取敏感图片地址
        if (StringUtils.isNotBlank(picUrl)) {
            String[] arr = picUrl.split(Contants.SENSITIVE_BUCKET);
            if(arr.length > 1) {
                url = Contants.SENSITIVE_BUCKET + arr[1];
            }
        }
        return trimRelativeUrl(url);
    }


    public static String getOssFilePath(String fileName){
        if(StringUtils.isBlank(fileName)) {
            return "";
        }
        if(fileName.startsWith(Contants.HTTP) || fileName.startsWith(Contants.HTTPS)) {
            return fileName;
        }
        if(fileName.startsWith("/")) {
            fileName = fileName.substring(1);
        }
        StringBuffer sb = new StringBuffer();
        sb.append(Contants.OSS_BASE_PATH)
                .append("/")
                .append(fileName);
        return sb.toString();
    }

    /**
     * 图片路径比较(相对路径)
     * @param oldUrl
     * @param newUrl
     * @return
     */
    public static boolean equalsUrl(String oldUrl, String newUrl) {
        oldUrl = trimRelativeUrl(oldUrl);
        newUrl = trimRelativeUrl(newUrl);
        return StringUtils.equals(oldUrl, newUrl);
    }

    /**
     * 截取相对路径的前导 /
     * @param url
     * @return
     */
    public static String trimRelativeUrl(String url){
        if(StringUtils.isNotBlank(url) && url.startsWith(Contants.LEFT_DIAGONAL)) {
            url = url.substring(0);
        }
        return url;
    }

    /**
     * 获取会员卡的内部编号
     *
     * @return
     * @userType 会员类型(0:内部会员 1：个人分时会员卡 5:旅游分时用户卡 6:企业分时会员卡）
     */
    public static String generateInternalNo(CardInfoMapper cardInfoMapper, int userType) {
        Map condition = new HashMap();
        condition.put("userType", userType);
        List<Integer> list = cardInfoMapper.queryInternalNo(condition);
        int seq = list.get(0);
        int index = seq + 1;
        String str = index + "";
        while (true) {
            if (str.indexOf("4") > -1) {
                str = (++index) + "";
            } else {
                break;
            }
        }
        condition.put("newSeq", str);

        cardInfoMapper.updateCardSeq(condition);

        DecimalFormat df = new DecimalFormat("000000000");

        String strSeq = df.format(seq);
        Random random = new Random();
        int randomNum = random.nextInt(100);
        df = new DecimalFormat("00");
        String strrandomNum = df.format(randomNum);
        while (true) {
            if (strrandomNum.indexOf("4") > -1) {
                randomNum = random.nextInt(100);
                strrandomNum = df.format(randomNum);
            } else {
                break;
            }
        }
        return strSeq.substring(0, 1) + strrandomNum + strSeq.substring(3);
    }

    /***************************************************************************
     * 时间运算 (月份加算)
     * @param date
     *            运算前时间
     * @param value
     *            add的值
     * @return String
     */
    public static String AddMonth(String dateStr, String format, int mon) {
        DateFormat dateFmt = new SimpleDateFormat(format);
        dateFmt.setTimeZone(timeZoneChina);
        Date tmpDate;

        try {
            tmpDate = dateFmt.parse(dateStr);
            Calendar cal = Calendar.getInstance();
            cal.setTime(tmpDate);
            cal.add(Calendar.MONTH, mon);
            return dateFmt.format(cal.getTime());
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 添加权限
     *
     * @param authId   用户id
     * @param comModel 当前登录人对象信息
     * @return
     */
    public static DefaultServiceRespDTO addAuthority(String authId, ComModel comModel, MembershipInfoMapper membershipInfoMapper) {
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        if (comModel != null) {
            String userOrgId = comModel.getOrgId();
            if (StringUtils.isNotBlank(userOrgId)) {
                if (StringUtils.equals(userOrgId, "00")) {
                    return vo;
                } else {
                    String[] authKey = authId.split(",");
                    // 记录不等的次数
                    int count = 0;
                    for (String key : authKey) {
                        String membershipOrgId = membershipInfoMapper.getOrgIdByAuthId(key);
                        if (!membershipOrgId.startsWith(userOrgId)) {
                            count++;
                        }
                    }
                    if (count > 0) {
                        vo.setCode(Integer.valueOf(Contants.RETURN_AUTHORITY_CODE));
                        vo.setMessage(msg);
                        return vo;
                    }
                }
            }
        }
        return vo;
    }

    /**
     * 取得系统时间
     */
    public static String getSystemTime() {
        Calendar c = Calendar.getInstance();
        int year = c.get(Calendar.YEAR);
        int month = c.get(Calendar.MONTH);
        int date = c.get(Calendar.DATE);
        int hour = c.get(Calendar.HOUR_OF_DAY);
        int minute = c.get(Calendar.MINUTE);
        int second = c.get(Calendar.SECOND);
        return "系统时间:" + year + "/" + month + "/" + date + " " + hour + ":" + minute + ":" + second;
    }

    /**
     * String转换成Date
     *
     * @param dateStr
     * @param fromType
     * @return
     */
    public static Date getDateFromStr(String dateStr, String fromType) {
        return DateUtils.getDateFromStr(dateStr, fromType);
    }

    /**
     * 读取message.properties 文件，根据指定的key，得到文字列
     *
     * @param key
     * @return
     */
    public static String getMsgPropertiesStr(String key) {
        return PropertyUtils.getProperty(key, StringUtils.EMPTY);
    }

    /**
     * 对象转化为字符串
     *
     * @param obj
     * @return
     */
    public static String ObjectToString(Object obj) {
        if (obj == null) {
            return "";
        } else {
            return obj.toString();
        }
    }

    /**
     * 两个字符串的大小比较
     *
     * @param obj
     * @return
     */
    public static int compareTo(String s1, String s2) {
        int i = s1.compareTo(s2);
        if (i > 0) {
            return 1;
        }
        return -1;
    }

    /**
     * 比较是否到期
     *
     * @param
     * @return
     * @throws ParseException
     */
    public static boolean isActiveEnd(String validDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String Nowdate = sdf.format(new Date());//获取当前时间

        try {
            if (sdf.parse(Nowdate).getTime() > sdf.parse(validDate).getTime()) {
                return true;//当前时间大于到期时间
            } else {
                return false;//当前时间小于到期时间
            }
        } catch (ParseException e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 验证时间格式
     *
     * @param time
     * @return
     */
    public static String validDays(String time) {
        try {
            if (StringUtils.isBlank(time) || time.length() != 10) {
                return null;
            }
            Date date = DateFormatUtils.ISO_DATE_FORMAT.parse(time);
            return DateFormatUtils.ISO_DATE_FORMAT.format(date);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 验证时间格式
     *
     * @param time
     * @return
     */
    public static String validDays(String time, String dateType, String toType) {
        try {
            if (StringUtils.isBlank(time) || time.length() != dateType.length()) {
                return null;
            }
            Date date = DateUtils.getDateFromStr(time, dateType);
            if(date != null) {
                return DateUtils.getFormatDate(date, toType);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 限英文小写字母、数字和下划线
     */
    public static boolean checkInputType(String str) {
        Matcher m = LOW_EN_NUM_PATTERN.matcher(str);
        if (m.matches()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断时间字符串是否符合指定的时间样式
     */
    public static boolean checkdataStyle(String data, String style) {
        try {
            DateFormat dateFromFmt = new SimpleDateFormat(style);
            dateFromFmt.setLenient(false);
            dateFromFmt.parse(data);
        } catch (ParseException e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 比较日期大小  大于等于
     *
     * @param time1
     * @param time2
     * @return
     */
    public static boolean compareDate2(String time1, String time2) {
        Date dt1 = getDateFromStr(time1, ComUtil.DATE_TYPE5);
        Date dt2 = getDateFromStr(time2, ComUtil.DATE_TYPE5);
        if (dt1.getTime() >= dt2.getTime()) {
            return true;
        } else {
            return false;
        }
    }

    /*
     * 获取拆分后的url
     * @param filePath
     * @return
     */
    public static String getFileSplitPath(String filePath) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(filePath)) {
            String path[] = filePath.split(",");
            StringBuffer buffer = new StringBuffer();
            for (String p : path) {
                buffer.append(ComUtil.subStringUrl(p));
                buffer.append(",");
            }
            filePath = buffer.toString().substring(0, buffer.toString().length() - 1);
        }
        return filePath;
    }

    /**
     * 拼接完整的url返回
     *
     * @param filePath
     * @return
     */
    public static String getFileFullPath(String filePath) {
        String fileUrl = Contants.OSS_BASE_PATH;
        if (org.apache.commons.lang3.StringUtils.isNotBlank(filePath)) {
            String path[] = filePath.split(",");
            StringBuffer buffer = new StringBuffer();
            for (String p : path) {
                buffer.append(fileUrl);
                buffer.append(p);
                buffer.append(",");
            }
            filePath = buffer.toString().substring(0, buffer.toString().length() - 1);
        }
        return filePath;
    }


    /**
     * 拼接完整的url返回
     *
     * @param filePath
     * @return
     */
    public static String getActivityFullPath(String filePath, String prefix) {
        if (StringUtils.isBlank(filePath)) {
            return StringUtils.EMPTY;
        }
        if(filePath.startsWith(Contants.HTTP)) {
            if(filePath.contains(Contants.QUESTION_MARK)) {
                //只取相对路径
                String path[] = filePath.split(Contants.QUESTION_REGIX);
                StringBuffer buffer = new StringBuffer(prefix).append(Contants.QUESTION_MARK);
                for (int i = 1; i < path.length; i++) {
                    buffer.append(path[i]);
                }
            }
        }else {
            return prefix + filePath;
        }
        return filePath;
    }

    /**
     * 优惠券使用时间格式转换（yyyyMMddHHmmss转换成yyyy-MM-dd HH:mm:ss 或 HHmm转换成HH:mm）
     *
     * @param time(两种形式：yyyyMMddHHmmss或HHmm)
     * @param oldformat
     * @param newformat
     * @param length（14或者4）
     * @return
     * @throws ParseException
     */
    public static String getTime(String time, String oldformat, String newformat, int length) throws ParseException {
        String newTime = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(time)) {
            time = time.substring(0, length);
            SimpleDateFormat sdf = new SimpleDateFormat(oldformat);
            Date date = sdf.parse(time);
            SimpleDateFormat sf = new SimpleDateFormat(newformat);
            newTime = sf.format(date);
        }
        return newTime;
    }

//    /**
//     * 车型Seq转换成车型名称
//     *
//     * @param vehicleModelIds(格式：1,2)
//     * @param vehicleModelMapper
//     * @return
//     */
//    public static String getVehicleModelNames(String vehicleModelIds, VehicleModelMapper vehicleModelMapper) {
//        String vehicleModelNames = StringUtils.EMPTY;
//        if (StringUtils.isNotBlank(vehicleModelIds)) {
//            String[] vehicleModelIdArray = vehicleModelIds.split(",");
//            List<String> list = vehicleModelMapper.getVehicleModelNames(vehicleModelIdArray);
//            if (CollectionUtils.isNotEmpty(list)) {
//                for (String vehicleModelName : list) {
//                    vehicleModelNames += vehicleModelName + ",";
//                }
//                vehicleModelNames = vehicleModelNames.substring(0, vehicleModelNames.length() - 1);
//            }
//        }
//        return vehicleModelNames;
//    }

//    /**
//     * 网点Seq转换成网点名称
//     *
//     * @param shopNameIds(格式：1,2)
//     * @param shopInfoMapper
//     * @return
//     */
//    public static String getShopNames(String shopNameIds, ShopInfoMapper shopInfoMapper) {
//        String shopNames = StringUtils.EMPTY;
//        if (StringUtils.isNotBlank(shopNameIds)) {
//            String[] shopNameIdArray = shopNameIds.split(",");
//            List<String> list = shopInfoMapper.getShopNames(shopNameIdArray);
//            if (CollectionUtils.isNotEmpty(list)) {
//                for (String shopName : list) {
//                    shopNames += shopName + ",";
//                }
//                shopNames = shopNames.substring(0, shopNames.length() - 1);
//            }
//        }
//        return shopNames;
//    }

    public static String getCityNameByCityId(String cities, List<Map<String, String>> cityResult) {
        List<String> cityNameList = new ArrayList<>();
        if (StringUtils.isNotBlank(cities) && cities.split(",").length > 0) {
            String[] cityIds = cities.split(",");
            for (String cityId : cityIds) {
                for (Map<String, String> value : cityResult) {
                    String cId = value.get("cityId");
                    if (cityId.equals(cId)) {
                        String cityName = value.get("city");
                        cityNameList.add(cityName);
                        break;
                    }
                }
            }
        }
        return StringUtils.join(cityNameList, ",");
    }

    public static String getNameById(String idStr, List<Map<String, String>> nameMap) {
        List<String> nameList = new ArrayList<>();
        if (StringUtils.isNotBlank(idStr) && idStr.split(Contants.MARK_COMMA).length > 0) {
            String[] ids = idStr.split(Contants.MARK_COMMA);
            for (String id : ids) {
                for (Map<String, String> value : nameMap) {
                    String cId = value.get("id");
                    if (id.equals(cId)) {
                        String name = value.get("name");
                        nameList.add(name);
                        break;
                    }
                }
            }
        }
        return StringUtils.join(nameList, ",");
    }

    /**
     * 计算两个日期之间相差的天数
     *
     * @param smdate 较小的时间
     * @param bdate  较大的时间
     * @return 相差天数
     * @throws ParseException
     * @throws ParseException
     */
    public static int daysBetween(Date smdate, Date bdate) throws ParseException {
        Calendar cal = Calendar.getInstance();
        cal.setTime(smdate);
        long time1 = cal.getTimeInMillis();
        cal.setTime(bdate);
        long time2 = cal.getTimeInMillis();
        long between_days = (time2 - time1) / (1000 * 3600 * 24);
        return Integer.parseInt(String.valueOf(between_days));
    }

    /**
     * 计算两个时间相差的天数
     * @param startStr
     * @param endStr
     * @param format
     * @return
     * @throws ParseException
     */
    public static int daysBetween(String startStr, String endStr, String format) {
        try{
            Date smdate = ComUtil.getDateFromStr(startStr, format);
            Date bdate = ComUtil.getDateFromStr(endStr, format);
            return ComUtil.daysBetween(smdate, bdate);
        }
        catch (Exception ex) {
            return -1;
        }
    }

    public static void insertRechargePackagesOperatorLog(String foreignKey, String operatorContent, Long createOperId, String createUser, MmpRechargePackagesLogsMapper mmpRechargePackagesLogsMapper) {
        MmpRechargePackagesLogs record = new MmpRechargePackagesLogs();
        record.setForeignKey(foreignKey);
        record.setContent(operatorContent);
        record.setCreateOperId(createOperId);
        record.setCreateOperName(createUser);
        record.setCreateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));
        mmpRechargePackagesLogsMapper.insertSelective(record);
    }

    /**
     * 比较两个集合元素是否相同
     *
     * @param list1
     * @param list2
     * @return
     */
    public static boolean isListEqual(List<String> list1, List<String> list2) {
        if (list1.size() != list2.size()) {
            return false;
        }
        for (Object o : list2) {
            if (!list1.contains(o)) {
                return false;
            }
        }
        return true;
    }


    /**
     * 获取网点城市名称
     *
     * @param pickShopCity       网点城市id
     * @param operateCityService
     * @return
     */
    public static String getShopCityName(String pickShopCity, IOperateCityService operateCityService) {
        String shopCityName = StringUtils.EMPTY;
        if(StringUtils.isNotBlank(pickShopCity)) {
            String[] pickShopCityVal = pickShopCity.split(",");
            if (pickShopCityVal.length > 0) {
                Set<String> cityIdSet = new HashSet<>();
                for (String val : pickShopCityVal) {
                    shopCityName = operateCityService.getCityNameByCityIdFromMap(val);
                    if (StringUtils.isNotBlank(shopCityName)) {
                        cityIdSet.add(shopCityName);
                    }
                }
                shopCityName = StringUtils.join(cityIdSet, ",");
            }
        }
        return shopCityName;
    }

    /**
     * 计算开始时间加月份是否超过结束时间
     * @param startDate 开始时间
     * @param endDate  结束时间
     * @param month 月份
     * @return
     */
    public static boolean compareDate3(String startDate, String endDate, int month) {
        String dt1 = AddMonth(startDate, DATE_TYPE5, month);
        if (endDate.compareTo(dt1) > 0) {
            return true;
        } else {
            return false;
        }
    }

    public static void insertTemplateManagementLog(String operatorContent,String foreignKey, Long createOperId, String createUser, MmpTemplateManagementLogsMapper mmpTemplateManagementLogsMapper) {
    	MmpTemplateManagementLogs record = new MmpTemplateManagementLogs();
        record.setForeignKey(foreignKey);
        record.setContent(operatorContent);
        record.setCreateOperId(createOperId);
        record.setCreateOperName(createUser);
        record.setCreateTime(Timestamp.valueOf(ComUtil.getSystemDate(ComUtil.DATE_TYPE1)));
        mmpTemplateManagementLogsMapper.insertSelective(record);
    }
    
    public static boolean compareTime(String time1, String time2,String timeType) {
        Date dt1 = getDateFromStr(time1, timeType);
        Date dt2 = getDateFromStr(time2, timeType);
        if (dt1.getTime() > dt2.getTime()) {
            return true;
        } else {
            return false;
        }
    }
    
    public static boolean stringToInteger(String str) {
    	try {
			if(Integer.valueOf(str) > 10) {
				return false;
			}
		} catch (NumberFormatException e) {
			return false;
		}
    	return true;
    }
    
    public static boolean isListEqu(List<String> list1, List<String> list2) {
		if (list1 == list2) {
			return true;
		}
		if (list1 == null && list2 == null) {
			return true;
		}
		if (list1 == null || list2 == null) {
			return false;
		}
		if (list1.size() != list2.size()) {
			return false;
		}
		for (Object o : list1) {
			if (!list2.contains(o)) {
                return false;
            }
		}
		for (Object o : list2) {
			if (!list1.contains(o)) {
                return false;
            }
		}
		return true;
	}

    public static String sortStrs(String str) {
        if(StringUtils.isBlank(str)) {
            return StringUtils.EMPTY;
        }
        String[] items = StringUtils.split(str, Contants.MARK_COMMA);
        Arrays.sort(items);
        return StringUtils.join(items, Contants.MARK_COMMA);
    }

    public static boolean splitContain(String str, String tar, String seperater) {
        List<String> items1 = Arrays.asList(StringUtils.split(str, seperater));
        List<String> items2 = Arrays.asList(StringUtils.split(tar, seperater));
        if(CollectionUtils.isEmpty(items1) || CollectionUtils.isEmpty(items2)) {
            return false;
        }
        return CollectionUtils.containsAny(items1, items2);
    }

    /**
     * 计算时间差（单位：分钟）
     * @param smdate
     * @param bdate
     * @return
     * @throws ParseException
     */
    public static long minuteBetween(Date smdate, Date bdate) throws ParseException {
        Calendar cal = Calendar.getInstance();
        cal.setTime(smdate);
        long time1 = cal.getTimeInMillis();
        cal.setTime(bdate);
        long time2 = cal.getTimeInMillis();
        long between_seconds = (time2 - time1) / 1000;
        double minutes = Math.ceil(between_seconds * 1.0 / 60);
        return (long)minutes;
    }

    private static final String DAY_DESC = "天";
    private static final String HOUR_DESC = "小时";
    private static final String MIN_DESC = "分钟";

    /**
     * 分钟数格式化显示： 1天2小时0分
     * @param mins
     * @return
     */
    public static String formatMin(String mins){
        if(!StringUtils.isBlank(mins)) {
            try{
                long minutes = Long.valueOf(mins);
                long day = minutes / (60*24);
                minutes = minutes % (60*24);
                long hour = minutes / 60;
                long minute = minutes % 60;

                StringBuffer sb = new StringBuffer();
                if(day > 0) {
                    sb.append(day).append(DAY_DESC);
                }
                if(hour > 0 || (hour == 0 && day > 0 && minute > 0)) {
                    sb.append(hour).append(HOUR_DESC);
                }
                if(minute > 0) {
                    sb.append(minute).append(MIN_DESC);
                }
                return sb.toString();

            }catch (Exception ex) {
            }
        }
        return StringUtils.EMPTY;
    }

    public static void main(String[] args) {
        System.out.println(ComUtil.formatMin("fjlakjgklaga"));
        System.out.println(ComUtil.formatMin("0"));
        System.out.println(ComUtil.formatMin("7"));
        System.out.println(ComUtil.formatMin("60"));
        System.out.println(ComUtil.formatMin("70"));
        System.out.println(ComUtil.formatMin("120"));
        System.out.println(ComUtil.formatMin("1440"));
        System.out.println(ComUtil.formatMin("1500"));
        System.out.println(ComUtil.formatMin("1501"));
    }
    
    /**
    *
    * @param hideKeyInfo
    */
   public static final String SECRET_MSG = "****";
    public static final String SECRET_MARK = "*";
   public static void setFullMemberInfo(boolean hideKeyInfo, MembershipInfoDTO membershipInfo){
       if(hideKeyInfo) {
           //加密信息
           //身份证 保留前3位后4位 其他部分隐藏
           //外籍 非外籍 驾驶证号判断 只对审核通过的会员
           String reviewStatusNum = membershipInfo.getReviewStatus();
           String driverCode = membershipInfo.getDrivingLicense1();
           //审核通过的会员做驾照和手机号脱敏
           //if ("1".equals(reviewStatusNum) && StringUtils.isNotBlank(driverCode)) {
               String userType1 = membershipInfo.getUserType();
               //驾照脱敏
               if(driverCode == null || driverCode.length() < 6) {
                   driverCode = SECRET_MSG;
               }else if ("1".equals(userType1) || StringUtils.isBlank(userType1)) {
                   //本籍 驾照号码 保留前3位后4位 其他部分隐藏
                   driverCode = getCommSecretMsg(driverCode, 3, 4, 4);
               } else if ("0".equals(userType1)) {
                   //外籍 驾照号码 保留后4位 其他部分隐藏
                   driverCode = getCommSecretMsg(driverCode, 0, 4, 4);
               }
               membershipInfo.setDrivingLicense(driverCode);
               //手机号脱敏
               membershipInfo.setMobilePhone(getCommSecretMsg(membershipInfo.getMobilePhone(), 3, 4));
               membershipInfo.setAuthKey(getCommSecretMsg(membershipInfo.getAuthId(), 3, 4, 4));
           //}
           // 会员姓名脱敏
           membershipInfo.setName(getCommSecretMsg(membershipInfo.getName(), 1, 0));
       }

       //是否需要复查标识
       if("1".equals(membershipInfo.getReviewStatus()) && "2".equals(membershipInfo.getDriverLicenseInputType())
           && membershipInfo.getReviewStatus() == null) {
    	   membershipInfo.setNeedReexamine("1");
       }
       else {
    	   membershipInfo.setNeedReexamine("0");
       }
   }

    public static void setKeySecretMemberInfo(MemberKeyInfo membershipInfo){
        //手机号脱敏
        membershipInfo.setMobilePhone(getCommSecretMsg(membershipInfo.getMobilePhone(), 3, 4));
        // 会员姓名脱敏
        membershipInfo.setName(getCommSecretMsg(membershipInfo.getName(), 1, 0));
    }


    /**
     * 通用关键信息混淆方法
     * 保留前left位，后right位 其他部分使用*隐藏
     * @param str
     * @param left
     * @param right
     * @return
     */
   public static String getCommSecretMsg(String str, int left, int right, int maxMark) {
       int len = left + right;
       //保留前left位，后right位 其他部分使用*隐藏
       if(StringUtils.isBlank(str)) {
           return StringUtils.EMPTY;
       }
       if(str.length() <= len) {
           return SECRET_MSG;
       }
       int markNum = str.length() - len;
       if(maxMark > 0 && maxMark < markNum) {
           markNum = maxMark;
       }
       String secret = StringUtils.repeat(SECRET_MARK, markNum);
       String encodeStr = str.substring(0, left)
               .concat(secret)
               .concat(str.substring(str.length() - right));
       return encodeStr;
   }

    public static String getCommSecretMsg(String str, int left, int right) {
       return getCommSecretMsg(str, left, right, -1);
    }

    public static int compareTo(Double s1, Double s2) {
        if(s1 == null && s2 == null){ return 0;}
        if(s1 == null) {return -1;}
        if(s2 == null) {return 1;}

        BigDecimal val1 = new BigDecimal(s1);
        BigDecimal val2 = new BigDecimal(s2);
        return val1.compareTo(val2);
    }

    public static int compareTo(Integer s1, Integer s2) {
        if(s1 == null && s2 == null) {return 0;}
        if(s1 == null) {return -1;}
        if(s2 == null) {return 1;}

        return Integer.compare(s1, s2);
    }

    public static Double doubleObject(Object obj) {
       if(obj == null) {
           return null;
       }
       try {
           return (Double)obj;
       }catch (Exception e) {
           return null;
       }
    }

    public static Long longObject(String obj) {
        if(obj == null) {
            return null;
        }
        try {
            return Long.valueOf(obj);
        }catch (Exception e) {
            return null;
        }
    }


    /** 会员状态（-1:资料不全 0:待审核 1：审核通过 2: 审核不通过 3：用户无效 4:重新审核） */
    public static String getRevirwStatusDesc(String value){
        if("-1".equals(value)) {
            return "资料不全";
        }else if("0".equals(value)) {
            return "待审核";
        }else if("1".equals(value)) {
            return "审核通过";
        }else if("2".equals(value)) {
            return "审核不通过";
        }else if("4".equals(value)) {
            return "重新审核";
        }
        return "用户无效";
    }
    /** 认证状态 0 未认证 1 未刷脸 2 已认证 */
    public static String getAuthStatusDesc(String value){
        if("0".equals(value)) {
            return "未认证";
        }else if("1".equals(value)) {
            return "未刷脸";
        }else if("2".equals(value)) {
            return "已认证";
        }
        return "未认证";
    }

    /**
     * 获取请求操作人信息
     * @param request
     * @return
     */
    public static OperateDTO getOperatorInfo(HttpServletRequest request) {
       ComModel comModel = getUserInfo(request);
       OperateDTO dto = new OperateDTO();
       dto.setOperatorName(comModel.getCreateOperName());
       dto.setOperatorId(comModel.getCreateOperId());
       return dto;
    }


    /**
     * 获取请求操作人信息
     * @param request
     * @return
     */
    public static OperatorDto getOperatorDto(HttpServletRequest request) {
        ComModel comModel = getUserInfo(request);
        OperatorDto dto = new OperatorDto();
        dto.setOperatorName(comModel.getCreateOperName());
        dto.setOperatorId(comModel.getCreateOperId());
        return dto;
    }

    public static FileItemDTO fromMultipartFile(MultipartFile partFile, String title) throws IOException {
        if(null == partFile) {
            return null;
        }
        FileItemDTO fileDTO = new FileItemDTO();
        fileDTO.setSize(partFile.getSize());
        fileDTO.setBytes(partFile.getBytes());
        fileDTO.setName(partFile.getName());
        fileDTO.setOriginalName(partFile.getOriginalFilename());
        fileDTO.setTitle(title);
        return fileDTO;
    }

    /**
     * 判断做update操作时，值是否被更新。
     * @param oldObj
     * @param newObj
     * @return
     */
    public static boolean checkColUpdated(Object oldObj, Object newObj) {
        if(newObj == null) {
            return false;
        }
        return ObjectUtils.notEqual(oldObj, newObj);
    }


    /**
     * 比较两个集合元素是否相同
     * null 与 empty collection认为相同
     * @param a
     * @param b
     * @return
     */
    public static boolean eualCollection(Collection a, Collection b) {
        int size1 = CollectionUtils.isEmpty(a) ? 0 : a.size();
        int size2 = CollectionUtils.isEmpty(b) ? 0 : b.size();
        if (size1 == size2) {
            return (size1 == 0) || (a.containsAll(b) && b.containsAll(a));
        }
        return false;
    }

    public static boolean checkIsSgmAppKey(String appKey){
        // 通用、别克代步车、校园版不发短信
        if (StringUtils.equals(appKey, Contants.APP_KEY)
                || StringUtils.equals(appKey, Contants.APP_KEY_IBUICK)
                || StringUtils.equals(appKey, Contants.APP_KEY_SONG)) {
            return true;
        }
        return false;
    }

    public static boolean checkIsUsersInMainlandChina(String national){
        if (StringUtils.isNotEmpty(national)) {
            if (national.contains(Contants.CHINA_NATIONAL)) {
                return true;
            }
        }
        return false;
    }


    /**
     * 通过国籍判断用户是否需要自动校验驾照三要素
     * 国籍包含中国且非大陆军人
     * @param national
     * @return
     */
    public static boolean checkIsUserNeedLicenseVerify(String national){
        if (StringUtils.isNotEmpty(national)) {
            if (national.contains(Contants.CHINA_NATIONAL)
                    && !StringUtils.equals(national, Contants.CHINA_NATIONAL_SOLIER)) {
                return true;
            }
        }
        return false;
    }

    public static Double getDoubleFromStr(String str, Double def) {
        if(StringUtils.isNotBlank(str)) {
            try{
                return Double.valueOf(str);
            }catch (Exception e) {
            }
        }
        return def;
    }

    public static String getStr(Object obj) {
        return (obj == null) ? null : obj.toString();
    }

    public static String getStr(Object obj, String def) {
        return (obj == null) ? def : obj.toString();
    }


    public static Integer getIntValue(String msg) {
        try {
            return Integer.valueOf(msg);
        }catch (Exception ex) {
            return null;
        }
    }

    public static Long getLongValue(String msg) {
        try {
            return Long.valueOf(msg);
        }catch (Exception ex) {
            return null;
        }
    }

    public static Number getValue(Number value, Number def) {
        return (value == null) ? def : value;
    }

    public static List<Integer> getIntList(String str){
        if(StringUtils.isNotBlank(str)) {
            List<Integer> list = new ArrayList<>();
            String[] values = StringUtils.split(str, ",");
            for (String s : values) {
                Integer value = getIntValue(s);
                if(value != null) {
                    list.add(value);
                }
            }
            return list;
        }
        return null;
    }

    /**
     * 文件类型和大小检查
     * @param file
     * @param fileName
     * @param maxSize
     * @param types
     * @return
     */
    public static DefaultServiceRespDTO checkUploadFile(File file, String fileName, Integer maxSize, String[] types){
        DefaultServiceRespDTO vo = new DefaultServiceRespDTO();
        //判断文件格式
        if (file == null || StringUtils.isBlank(fileName)) {
            vo.setCode(-1);
            vo.setMessage("请选择文件");
        }
        String suffix = StringUtils.substringAfterLast(fileName,".");
        if(!ArrayUtils.contains(types, suffix)) {
            vo.setCode(-1);
            vo.setMessage("请选择指定类型的文件：" + StringUtils.join(types, "，"));
        }
        //判断文件大小
        // 校验文件大小
        if(maxSize != null && maxSize * 1024 * 1024 < file.length()) {
            vo.setCode(-1);
            vo.setMessage("上传失败：模板限制大小为" + maxSize + "M");
        }
        return vo;
    }

    public static final String getReviewStatusName(Integer reviewStatus) {
        if(reviewStatus != null) {
            int index = reviewStatus + 1;
            if(index >= 0 && index < Contants.REVIEW_STATUS_NAMES.length) {
                return Contants.REVIEW_STATUS_NAMES[index];
            }
        }
        return StringUtils.EMPTY;
    }

    public static void setMemberNationalInfo(MembershipDTO membershipInfo){
        /**
         *	用户类型（0：外籍用户，1：大陆用户，2：港澳台用户 3:大陆用户（军人））
         */
        if(StringUtils.equals(Contants.CHINA_HKMT_NATIONAL, membershipInfo.getNational())) {
            membershipInfo.setUserType("2");
            membershipInfo.setUserTypeName("中国港澳台");
        }else if(StringUtils.equals(Contants.CHINA_NATIONAL_SOLIER, membershipInfo.getNational())) {
            membershipInfo.setUserType("3");
            membershipInfo.setUserTypeName("大陆用户（军人）");
            if(StringUtils.equals(membershipInfo.getIdType(), "5")) {
                membershipInfo.setIdType("1");
            }
        }else if(StringUtils.contains(membershipInfo.getNational(), Contants.CHINA_NATIONAL)) {
            membershipInfo.setUserType("1");
            membershipInfo.setUserTypeName("大陆用户");
        }else {
            membershipInfo.setUserType("0");
            membershipInfo.setUserTypeName("外籍");
        }
    }

    public static boolean checkAmount(Double d) {
        if(d == null) {
            return false;
        }
        return ComUtil.FLOAT_NUM_PATTERN.matcher(String.valueOf(d)).matches();
    }

    public static void checkAndMkDir(String dir) {
        File baseDir = new File(dir);
        if(!baseDir.exists() || !baseDir.isDirectory()) {
            log.info("临时目录不存在，初次创建, dir={}", dir);
            baseDir.mkdirs();
        }
    }

    public static boolean isNumber(String str){
        if(StringUtils.isBlank(str)){
            return false;
        }
        Pattern pattern = Pattern.compile("[0-9]*");
        return pattern.matcher(str).matches();
    }

    public static boolean compareStrings(String str1, String str2,String splitString) {
        try {
            if (StringUtils.isBlank(str1) && StringUtils.isBlank(str2)) {
                return true;
            }
            if (StringUtils.isBlank(str1) || StringUtils.isBlank(str2)) {
                return false;
            }

            // 分割字符串并转换为 Set
            Set<String> set1 = new HashSet<>(Arrays.asList(str1.split(splitString)));
            Set<String> set2 = new HashSet<>(Arrays.asList(str2.split(splitString)));

            // 比较 Set 内容
            return set1.equals(set2);
        } catch (Exception e) {
            return false;
        }
    }
}
