package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.dto.MmpProvisionGroupNodeDto;
import com.extracme.evcard.mmp.dto.ProvisionNodeDTO;
import com.extracme.evcard.mmp.model.MmpProvisionGroupNode;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface MmpProvisionGroupNodeMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group_node
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group_node
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    int insert(MmpProvisionGroupNode record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group_node
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    int insertSelective(MmpProvisionGroupNode record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group_node
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    MmpProvisionGroupNode selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group_node
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    int updateByPrimaryKeySelective(MmpProvisionGroupNode record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_provision_group_node
     *
     * @mbggenerated Thu Sep 24 16:27:10 CST 2020
     */
    int updateByPrimaryKey(MmpProvisionGroupNode record);

    List<MmpProvisionGroupNodeDto> queryByGroups(@Param("list") List<Long> groupIds);

    void batchInsert(@Param("groupId") Long groupId,
                     @Param("list") List<ProvisionNodeDTO> nodes,
                     @Param("createOperId") Long createOperId, @Param("createOperName") String createOperName);


    void batchDisable(@Param("groupId") Long groupId,
                      @Param("list") List<String> nodeIds,
                      @Param("updateTime") Date dateTime, @Param("updateOperId") Long updateOperId,
                      @Param("updateOperName") String updateOperName);

    void batchDelete(@Param("groupId") Long groupId,
                      @Param("list") List<String> nodeIds);

    MmpProvisionGroupNode queryActiveGroupNode(@Param("groupId") Long groupId, @Param("nodeId") String nodeId);

    MmpProvisionGroupNode selectNextToGroupNode(@Param("id") Long id,
                                                @Param("groupId") Long groupId,
                                                @Param("rankId")Long rankId);

    MmpProvisionGroupNode selectPrevGroupNode(@Param("id") Long id,
                                              @Param("groupId") Long groupId,
                                              @Param("rankId")Long rankId);

    void updateRankByPrimaryKey(@Param("id") Long id, @Param("rankId")Long rankId,
                                @Param("updateTime") Date dateTime, @Param("updateOperId") Long updateOperId,
                                @Param("updateOperName") String updateOperName);


    Map<Long,Long> selectNodeRankRange(@Param("groupId") Long groupId);
}