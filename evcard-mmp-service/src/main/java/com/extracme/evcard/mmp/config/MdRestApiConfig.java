package com.extracme.evcard.mmp.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "md.rest.api")
public class MdRestApiConfig {
    private String baseUrl;

    private String getCacheGoodsModelName;
    private String getCacheStore;
    private String getOrderCountByStatusUrl;
    private String getCacheGoodsVehicleModelRelation;
    private String getPersonalContractInfo;
    private String getContractListByPersonal;
    private String getUserPayedCount;
    private String getUnpaidGoodsOrderByMId;


    // app5.8 押金接口
    private String getMemberDepositInfos;
    private String getMemberDepositLogs;
    private String rapidRefund;
    private String queryRapidRefundRecord;

    // 优惠券模板升级
    // 查询车辆品牌信息
    private String queryVehicleBrandInfoNoLogin;
    // 查询车辆级别信息  舒适型、商务型
    private String queryVehicleLevelList;

    // 顶部城市
    private String getAllTopCityInfo;
    // 二级渠道列表
    private String querySecondChannelListForMmp;
}
