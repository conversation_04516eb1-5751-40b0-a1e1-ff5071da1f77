package com.extracme.evcard.mmp.config;


import com.aliyun.openservices.ons.api.PropertyKeyConst;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

@Configuration
@Data
public class OnsConfig {
    @Value("${ons_addr}")
    private String onsAddr;

    @Value("${ali.mmp.AccessKey}")
    private String accessKey;

    @Value("${ali.mmp.SecretKey}")
    private String secretKey;

    @Value("${ali.mmp.gid}")
    private String onsGid;

    @Value("${ali.mmp.topic}")
    private String onsTopic;

    @Value("${ali.mmp.couponImport.topic}")
    private String onsCouponTopic;


    public Properties getOnsProperties() {
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.AccessKey, this.accessKey);
        properties.setProperty(PropertyKeyConst.SecretKey, this.secretKey);
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, this.onsAddr);
        return properties;
    }
}
