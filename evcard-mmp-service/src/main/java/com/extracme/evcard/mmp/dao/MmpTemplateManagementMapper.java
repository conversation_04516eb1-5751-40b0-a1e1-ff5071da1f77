package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.dto.TemplateManagementDTO;
import com.extracme.evcard.mmp.model.MmpTemplateManagement;
import com.extracme.framework.data.dao.Dao;
import com.extracme.framework.data.dao.OecsMapper;

import java.util.List;

@OecsMapper
public interface MmpTemplateManagementMapper extends Dao<MmpTemplateManagement> {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_template_management
     *
     * @mbggenerated Wed May 16 13:48:48 CST 2018
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_template_management
     *
     * @mbggenerated Wed May 16 13:48:48 CST 2018
     */
    int insert(MmpTemplateManagement record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_template_management
     *
     * @mbggenerated Wed May 16 13:48:48 CST 2018
     */
    int insertSelective(MmpTemplateManagement record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_template_management
     *
     * @mbggenerated Wed May 16 13:48:48 CST 2018
     */
    MmpTemplateManagement selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_template_management
     *
     * @mbggenerated Wed May 16 13:48:48 CST 2018
     */
    int updateByPrimaryKeySelective(MmpTemplateManagement record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_template_management
     *
     * @mbggenerated Wed May 16 13:48:48 CST 2018
     */
    int updateByPrimaryKey(MmpTemplateManagement record);

	List<TemplateManagementDTO> queryTemplateList();

    int deleteTemplateByTemplateId(Long id);

    MmpTemplateManagement selectTemplateInfoById(Long id);
}