package com.extracme.evcard.mmp.dao;

import com.extracme.evcard.mmp.dto.UserOperatorLogDTO;
import com.extracme.evcard.mmp.model.MmpCouponLimitOperateLog;
import com.extracme.evcard.mmp.model.MmpOrgCouponLimitConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MmpCouponLimitOperateLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_coupon_limit_operate_log
     *
     * @mbggenerated Tue Apr 02 17:55:30 CST 2019
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_coupon_limit_operate_log
     *
     * @mbggenerated Tue Apr 02 17:55:30 CST 2019
     */
    int insert(MmpCouponLimitOperateLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_coupon_limit_operate_log
     *
     * @mbggenerated Tue Apr 02 17:55:30 CST 2019
     */
    int insertSelective(MmpCouponLimitOperateLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_coupon_limit_operate_log
     *
     * @mbggenerated Tue Apr 02 17:55:30 CST 2019
     */
    MmpCouponLimitOperateLog selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_coupon_limit_operate_log
     *
     * @mbggenerated Tue Apr 02 17:55:30 CST 2019
     */
    int updateByPrimaryKeySelective(MmpCouponLimitOperateLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table mmp_coupon_limit_operate_log
     *
     * @mbggenerated Tue Apr 02 17:55:30 CST 2019
     */
    int updateByPrimaryKey(MmpCouponLimitOperateLog record);

    /**
     * 批量写入日志
     * @param operatorLogs
     * @return
     */
    int batchInsert(@Param("list") List<MmpCouponLimitOperateLog> operatorLogs);

    /**
     * 根据FOREIGN_KEY查询用户操作日志记录
     * @param orgId
     * @return
     */
    List<UserOperatorLogDTO> queryUserOperatorLog(@Param("orgId") String orgId);
}