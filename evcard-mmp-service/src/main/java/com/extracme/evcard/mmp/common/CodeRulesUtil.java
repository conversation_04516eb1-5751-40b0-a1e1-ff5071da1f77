package com.extracme.evcard.mmp.common;

import java.util.Random;


/**
 * code码生成器，算法原理：<br/>
 * 1) 获取id: 通过时间戳，并使用redis锁保证唯一 <br/>
 * 2) 使用自定义进制转为：32进制 gpm6 <br/>
 * 3) 转为字符串，并在后面加随机字符（char[] b 32进制中不包含的字符）：gpm6A <br/>
 * 4）在后面随机产生若干个随机数字字符（这里使用32进制中包含的字符）：gpm6A7 <br/>
 * 转为自定义进制后就不会出现A这个字符，然后在后面加个'A'，这样就能确定唯一性。最后在后面产生一些随机字符进行补全。<br/>
 * 
 * <AUTHOR> yibo
 */
public class CodeRulesUtil {
    
    private static final char[] r = new char[]{'j','g','h','s','z','7','v','w','t','k','q','x','r','c','8','f','i','0','b','d','6','m','n','9','u','4','p','5','2','y','3','e'};
 
    /** (不能与自定义进制有重复) */
    private static final char b = 'a';
    
    /** 进制长度 */
    private static final int binLen = r.length;
 
    /** 序列最小长度 */
    private static final int s = 8;
 
    /**
     * 进制换算（加密）.<br>
     * @param id
     * @return
     */
    public static String encode(long id) {
        char[] buf=new char[32];
        int charPos=32;
        while((id / binLen) > 0) {
            int ind=(int)(id % binLen);
            buf[--charPos]=r[ind];
            id /= binLen;
        }
        buf[--charPos]=r[(int)(id % binLen)];
        String str = new String(buf, charPos, (32 - charPos));
        // 不够长度的自动随机补全
        if(str.length() < s) {
            StringBuilder sb=new StringBuilder();
            sb.append(str);
            sb.append(b);
            Random rnd=new Random();
            for(int i=1; i < s - str.length(); i++) {
            	sb.append(r[rnd.nextInt(binLen)]);
            }
            str = sb.toString();
        }
        return str;
    }
 
    /**
     * 进制换算（解密）.<br>
     * @param code
     * @return
     */
   public static long decode(String code) {
        char chs[]=code.toCharArray();
        long res=0L;
        for(int i=0; i < chs.length; i++) {
            int ind=0;
            for(int j=0; j < binLen; j++) {
                if(chs[i] == r[j]) {
                    ind=j;
                    break;
                }
            }
            if(chs[i] == b) {
                break;
            }
            if(i > 0) {
                res=res * binLen + ind;
            } else {
                res=ind;
            }
        }
        return res;
    }
    
    public static void main(String[] args) {
    	int id = 100;
    	for(int i=199000; i<=200000; i++) {
    		String code = CodeRulesUtil.encode(++id);
    		System.out.println(id + "  " + code + "   " + decode(code));
    	}
    }
    
}
